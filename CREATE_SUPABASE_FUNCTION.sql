-- ===========================================
-- Supabase SQL Function để Check User Existence
-- ===========================================
-- Copy và paste vào Supabase SQL Editor để chạy

-- Function: Check if user exists by email
CREATE OR REPLACE FUNCTION check_user_exists(user_email text)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE email = user_email
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test function với email của bạn
SELECT check_user_exists('<EMAIL>') as user_exists;

-- Expected result: 
-- - true nếu email đã đăng ký
-- - false nếu email chưa đăng ký
