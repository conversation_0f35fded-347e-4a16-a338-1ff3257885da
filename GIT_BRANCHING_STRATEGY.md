# 🌿 Git Branching Strategy - CookSpark Project

## 🎯 **<PERSON><PERSON><PERSON> tiêu ch<PERSON>h:**
T<PERSON><PERSON> nhiều nh<PERSON>h độc lập như các "snapshot" để có thể quay lại bất cứ lúc nào mà không ảnh hưởng đến nhau.

## 📋 **Quy trình chuẩn cho mỗi milestone:**

### 1. **<PERSON><PERSON><PERSON> bị commit:**
```bash
# Kiểm tra trạng thái
git status

# Thêm tất cả thay đổi
git add .

# Commit với message mô tả rõ ràng
git commit -m "🎉 [Tên tính năng] - [Mô tả ngắn gọn]

✨ Features:
- [Tính năng 1]
- [Tính năng 2]
- [Tính năng 3]

🔧 Improvements:
- [Cải tiến 1]
- [Cải tiến 2]

🐛 Bug Fixes:
- [Sửa lỗi 1]
- [Sửa lỗi 2]"
```

### 2. **T<PERSON><PERSON> nh<PERSON>h mới:**
```bash
# Tạo và chuyển sang nh<PERSON>h mới
git checkout -b [tên-nhánh-mới]

# <PERSON><PERSON><PERSON> nh<PERSON>h lên GitHub với tracking
git push -u origin [tên-nhánh-mới]
```

### 3. **Tạo tag milestone:**
```bash
# Tạo tag với mô tả chi tiết
git tag -a v[version] -m "🎉 [Tên Version] Release

🚀 Major Features:
- [Tính năng chính 1]
- [Tính năng chính 2]

🛠️ Technical Improvements:
- [Cải tiến kỹ thuật 1]
- [Cải tiến kỹ thuật 2]

📱 Platform Support:
- iOS (tested)
- Android (ready)

🎯 Status: [Ready for Production/Development/Testing]"

# Đẩy tag lên GitHub
git push origin v[version]
```

## 🏷️ **Quy tắc đặt tên nhánh:**

### **Format:** `[type]-[feature-name]`

### **Types:**
- `version-x.x` - Các version chính (1.0, 1.1, 2.0...)
- `feature-` - Tính năng mới
- `fix-` - Sửa lỗi
- `improve-` - Cải tiến
- `experiment-` - Thử nghiệm
- `backup-` - Backup trước khi thay đổi lớn

### **Ví dụ tên nhánh:**
```
version-1.0              ✅ Code cleanup & optimization
version-1.1              📱 UI/UX improvements  
feature-user-profile     👤 Quản lý profile user
feature-meal-planning    📅 Lập kế hoạch bữa ăn
feature-recipe-share     🔗 Chia sẻ công thức
feature-offline-mode     📱 Chế độ offline
feature-notifications   🔔 Thông báo push
feature-social-login    👥 Đăng nhập mạng xã hội
feature-premium         💎 Tính năng premium
feature-dark-mode       🌙 Chế độ tối
fix-login-bug           🐛 Sửa lỗi đăng nhập
improve-performance     ⚡ Tối ưu hiệu suất
experiment-ai-recipe    🤖 Thử nghiệm AI gợi ý
backup-before-refactor  💾 Backup trước refactor
```

## 🔄 **Workflow làm việc:**

### **Bước 1: Phát triển**
- Làm việc trên nhánh hiện tại
- Test và đảm bảo code hoạt động

### **Bước 2: Lưu trữ milestone**
- Commit tất cả thay đổi
- Tạo nhánh mới cho milestone
- Đẩy lên GitHub
- Tạo tag nếu cần

### **Bước 3: Tiếp tục phát triển**
- Có thể tiếp tục trên nhánh hiện tại
- Hoặc tạo nhánh mới cho tính năng khác
- Không lo mất code cũ

### **Bước 4: Quay lại khi cần**
```bash
# Xem tất cả nhánh
git branch -a

# Chuyển sang nhánh cũ
git checkout [tên-nhánh-cũ]

# Tạo nhánh mới từ nhánh cũ
git checkout -b [tên-nhánh-mới] [tên-nhánh-gốc]
```

## 💡 **Lợi ích của strategy này:**

- ✅ **An toàn:** Mỗi nhánh là 1 bản backup hoàn chỉnh
- ✅ **Linh hoạt:** Có thể quay lại bất kỳ version nào
- ✅ **Thử nghiệm:** Test tính năng mới mà không sợ hỏng
- ✅ **Demo:** Có thể show các version khác nhau cho client
- ✅ **Rollback:** Dễ dàng quay lại version ổn định
- ✅ **Parallel Development:** Có thể phát triển nhiều tính năng cùng lúc
- ✅ **History:** Lưu trữ lịch sử phát triển rõ ràng

## 🚨 **Lưu ý quan trọng:**

1. **Luôn commit trước khi tạo nhánh mới**
2. **Đặt tên nhánh mô tả rõ ràng**
3. **Tạo tag cho các milestone quan trọng**
4. **Không bao giờ force push lên nhánh đã share**
5. **Backup local trước khi thực hiện thay đổi lớn**

## 📞 **Khi cần hỗ trợ:**

Chỉ cần kéo file này vào chat và nói:
> "Tôi muốn tạo nhánh mới cho [tính năng X] theo quy tắc này"

Hoặc:
> "Tôi muốn quay lại nhánh [tên nhánh] để tiếp tục phát triển"

---
**📅 Created:** $(date)  
**🎯 Purpose:** Git branching strategy for independent development  
**👤 Author:** CookSpark Development Team
