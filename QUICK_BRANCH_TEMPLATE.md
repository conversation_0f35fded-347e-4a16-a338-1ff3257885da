# 🚀 Quick Branch Creation Template

## 📋 **Template để tạo nh<PERSON>h nhanh:**

### **Bước 1: Chuẩn bị thông tin**
```
Tên tính năng: [Điền tên tính năng]
Loại nhánh: [version/feature/fix/improve/experiment/backup]
Mô tả ngắn: [Mô tả 1 câu về tính năng]
```

### **Bước 2: Copy & paste commands**
```bash
# Kiểm tra trạng thái
git status

# Thêm tất cả thay đổi
git add .

# Commit với message
git commit -m "🎉 [TÊN TÍNH NĂNG] - [MÔ TẢ NGẮN]

✨ Features:
- [Tính năng 1]
- [Tính năng 2]

🔧 Improvements:
- [Cải tiến 1]
- [Cải tiến 2]

🐛 Bug Fixes:
- [Sửa lỗi nếu có]"

# Tạo nhánh mới
git checkout -b [TÊN-NHÁNH]

# Đ<PERSON><PERSON> lên GitHub
git push -u origin [TÊN-NHÁNH]

# Tạo tag (nếu cần)
git tag -a v[VERSION] -m "🎉 [TÊN VERSION] Release"
git push origin v[VERSION]
```

## 🎯 **Ví dụ cụ thể:**

### **Scenario: Tạo tính năng Dark Mode**
```bash
git add .
git commit -m "🎉 Dark Mode Implementation - Hoàn thành chế độ tối

✨ Features:
- Theme switcher trong settings
- Dark/Light mode tự động theo hệ thống
- Lưu preference của user
- Smooth transition animation

🔧 Improvements:
- Tối ưu màu sắc cho dark mode
- Cải thiện contrast ratio
- Better accessibility support"

git checkout -b feature-dark-mode
git push -u origin feature-dark-mode
```

## 📞 **Cách sử dụng với AI Assistant:**

### **Template message cho AI:**
```
Tôi muốn tạo nhánh mới theo quy tắc GIT_BRANCHING_STRATEGY.md

Thông tin:
- Tính năng: [Tên tính năng]
- Loại: [feature/fix/improve/version]
- Mô tả: [Mô tả ngắn gọn]
- Version (nếu có): [x.x]

Hãy giúp tôi tạo nhánh theo đúng quy trình.
```

### **Ví dụ message:**
```
Tôi muốn tạo nhánh mới theo quy tắc GIT_BRANCHING_STRATEGY.md

Thông tin:
- Tính năng: Recipe Sharing
- Loại: feature
- Mô tả: Cho phép user chia sẻ công thức nấu ăn
- Version: không cần

Hãy giúp tôi tạo nhánh theo đúng quy trình.
```

## 🔄 **Quick Commands Reference:**

### **Xem tất cả nhánh:**
```bash
git branch -a
```

### **Chuyển sang nhánh khác:**
```bash
git checkout [tên-nhánh]
```

### **Tạo nhánh từ nhánh khác:**
```bash
git checkout -b [nhánh-mới] [nhánh-gốc]
```

### **Xem tags:**
```bash
git tag
```

### **Xem commit history:**
```bash
git log --oneline --graph
```

---
**💡 Tip:** Bookmark file này để sử dụng nhanh khi cần tạo nhánh mới!
