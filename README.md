# Nấu gì đây? - Ứng dụng quản lý món ăn cá nhân

![Logo](assets/images/logo.png)

## 📝 <PERSON><PERSON> tả

"Nấu gì đây?" là ứng dụng Flutter giúp người dùng quản lý và cá nhân hóa các món ăn đã nấu, đồng thời tích hợp chúng vào gợi ý thực đơn. Người dùng có thể thêm món ăn với thông tin chi tiết như tên, nguy<PERSON><PERSON> liệu, c<PERSON>ch nấu, và ảnh, sau đó chỉnh sửa hoặc xóa chúng dễ dàng. Các món ăn do người dùng tạo sẽ được đưa vào danh sách gợi ý, tăng tính cá nhân hóa và giảm phụ thuộc vào dữ liệu mặc định.

## ✨ Tính năng

- 👤 **<PERSON><PERSON><PERSON> thực người dùng**: <PERSON><PERSON><PERSON> nhập, đă<PERSON> ký, kh<PERSON><PERSON> phục mật khẩu với Supabase
- 🍽️ **Quản lý món ăn**: Thêm, sửa, xóa món ăn cá nhân với hình ảnh
- 📆 **Lập kế hoạch thực đơn**: Tự động gợi ý thực đơn hằng tuần
- 🔄 **Đồng bộ hóa đám mây**: Lưu trữ và đồng bộ món ăn giữa các thiết bị
- 📱 **Giao diện thân thiện**: Thiết kế Material You responsive, hỗ trợ các kích thước màn hình khác nhau
- 🌙 **Chế độ tối**: Hỗ trợ chế độ sáng/tối và theo cài đặt hệ thống

## 🛠️ Công nghệ sử dụng

- **Framework**: Flutter/Dart (>=3.0.0)
- **Quản lý trạng thái**: Provider
- **Backend/Authentication**: Supabase
- **Lưu trữ cục bộ**: SharedPreferences
- **Xử lý ảnh**: image_picker, image
- **Giao diện**: Google Fonts, flutter_staggered_animations
- **Tiện ích**: uuid, intl, path_provider, http

## 🏗️ Cấu trúc dự án

```
lib/
├── data/           # Dữ liệu và repository
├── models/         # Các model dữ liệu
├── providers/      # State management với Provider
├── screens/        # Các màn hình UI
├── services/       # Các dịch vụ xác thực, đồng bộ hóa...
├── utils/          # Tiện ích và helpers
├── widgets/        # Các widget tái sử dụng
└── main.dart       # Entry point của ứng dụng
```

## 📦 Cài đặt

1. Đảm bảo bạn đã cài đặt Flutter SDK phiên bản 3.0.0 trở lên
2. Clone repository này:
   ```bash
   git clone https://github.com/yourusername/nau-gi-day.git
   ```
3. Cài đặt các dependencies:
   ```bash
   flutter pub get
   ```
4. Chạy ứng dụng:
   ```bash
   flutter run
   ```

## 🔐 Cấu hình Supabase

Ứng dụng sử dụng Supabase làm backend. Để thiết lập:

1. Tạo tài khoản và project trên [Supabase](https://supabase.io)
2. Tạo các bảng và bucket storage theo hướng dẫn trong tệp `supabase/migrations`
3. Cập nhật các thông tin kết nối trong `lib/supabase_options.dart`

## 🖥️ Phát triển

### Thêm món ăn mới

1. Tạo đối tượng món ăn mới trong `lib/models/dish.dart`
2. Sử dụng `DishService` để lưu trữ món ăn lên Supabase
3. Đồng bộ hóa dữ liệu giữa các thiết bị sử dụng `SyncService`

### Đồng bộ hóa dữ liệu

Sử dụng `SyncService` để đồng bộ hóa dữ liệu món ăn giữa ứng dụng và Supabase:

```dart
final syncService = SyncService();
final result = await syncService.syncBidirectional();
```

## 📱 Ảnh chụp màn hình

[Thêm ảnh chụp màn hình của ứng dụng vào đây]

## 🚀 Kế hoạch phát triển

- [ ] Chia sẻ công thức món ăn trên mạng xã hội
- [ ] Tính năng theo dõi dinh dưỡng và calo
- [ ] Hỗ trợ nhiều ngôn ngữ
- [ ] Tích hợp với trợ lý ảo để gợi ý món ăn bằng giọng nói

## 👨‍💻 Tác giả

- [@yourusername](https://github.com/yourusername)

## 📄 Giấy phép

Dự án này được cấp phép theo giấy phép MIT - xem tệp [LICENSE](LICENSE) để biết chi tiết.
