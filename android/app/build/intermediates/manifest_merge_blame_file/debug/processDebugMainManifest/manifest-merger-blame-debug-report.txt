1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.minhduc.naugiday"
4    android:versionCode="5"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:5-67
15-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
16-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:2:5-76
16-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:2:22-73
17    <uses-permission
17-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:22-77
19        android:maxSdkVersion="32" />
19-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:78-104
20    <uses-permission android:name="android.permission.CAMERA" />
20-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:4:5-65
20-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:4:22-62
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:52:5-69:15
29        <intent>
29-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:53:9-56:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:54:13-72
30-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:54:21-70
31
32            <data android:mimeType="text/plain" />
32-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
32-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:19-48
33        </intent>
34        <intent>
34-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:57:9-60:18
35            <action android:name="android.intent.action.VIEW" />
35-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
35-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
36
37            <data android:scheme="https" />
37-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
38        </intent>
39        <intent>
39-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:61:9-64:18
40            <action android:name="android.intent.action.VIEW" />
40-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
40-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
41
42            <data android:scheme="http" />
42-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
43        </intent>
44        <intent>
44-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:65:9-68:18
45            <action android:name="android.intent.action.SEND" />
45-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:66:13-65
45-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:66:21-62
46
47            <data android:mimeType="*/*" />
47-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
47-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:19-48
48        </intent>
49    </queries> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
50    <!-- <uses-sdk android:minSdkVersion="14" /> -->
51    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
51-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
51-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:22-76
52    <uses-permission android:name="android.permission.WAKE_LOCK" />
52-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
52-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:22-65
53    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
53-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
53-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
54    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
54-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
54-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
55    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
55-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
55-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
56    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
56-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
56-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
57    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
57-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
57-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
58
59    <permission
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
60        android:name="com.minhduc.naugiday.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.minhduc.naugiday.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Thêm thông tin cho Firebase -->
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
64    <application
65        android:name="android.app.Application"
65-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:8:9-42
66        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
66-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
67        android:debuggable="true"
68        android:enableOnBackInvokedCallback="true"
68-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:11:9-51
69        android:extractNativeLibs="true"
70        android:icon="@mipmap/ic_launcher"
70-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:9:9-43
71        android:label="Nấu gì đây?"
71-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:7:9-36
72        android:usesCleartextTraffic="true" >
72-->D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:8:18-53
73        <activity
73-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:12:9-40:20
74            android:name="com.minhduc.naugiday.MainActivity"
74-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:13:13-41
75            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
75-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:18:13-163
76            android:exported="true"
76-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:14:13-36
77            android:hardwareAccelerated="true"
77-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:19:13-47
78            android:launchMode="singleTop"
78-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:15:13-43
79            android:taskAffinity=""
79-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:16:13-36
80            android:theme="@style/LaunchTheme"
80-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:17:13-47
81            android:windowSoftInputMode="adjustPan" >
81-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:20:13-52
82
83            <!--
84                 Specifies an Android theme to apply to this Activity as soon as
85                 the Android process has started. This theme is visible to the user
86                 while the Flutter UI initializes. After that, this theme continues
87                 to determine the Window background behind the Flutter UI.
88            -->
89            <meta-data
89-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:25:13-28:17
90                android:name="io.flutter.embedding.android.NormalTheme"
90-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:26:15-70
91                android:resource="@style/NormalTheme" />
91-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:27:15-52
92
93            <intent-filter>
93-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:29:13-32:29
94                <action android:name="android.intent.action.MAIN" />
94-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:30:17-68
94-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:30:25-66
95
96                <category android:name="android.intent.category.LAUNCHER" />
96-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:31:17-76
96-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:31:27-74
97            </intent-filter>
98            <intent-filter>
98-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:33:13-39:29
99                <action android:name="android.intent.action.VIEW" />
99-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
99-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
100
101                <category android:name="android.intent.category.DEFAULT" />
101-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:17-76
101-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:27-73
102                <category android:name="android.intent.category.BROWSABLE" />
102-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:17-78
102-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:27-75
103
104                <data android:scheme="http" />
104-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
105                <data android:scheme="https" />
105-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
106            </intent-filter>
107        </activity>
108        <!--
109 Don't delete the meta-data below.
110             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
111        -->
112        <meta-data
112-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:43:9-45:33
113            android:name="flutterEmbedding"
113-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:44:13-44
114            android:value="2" />
114-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:45:13-30
115
116        <service
116-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
117            android:name="com.google.firebase.components.ComponentDiscoveryService"
117-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
118            android:directBootAware="true"
118-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
119            android:exported="false" >
119-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:16:13-37
120            <meta-data
120-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
121                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
121-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
123            <meta-data
123-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
124                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
124-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
126            <meta-data
126-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
127                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
127-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
129            <meta-data
129-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
130                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
130-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
132            <meta-data
132-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
133                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
133-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
135            <meta-data
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
136                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
138            <meta-data
138-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
139                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
139-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
141            <meta-data
141-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
142                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
142-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
144            <meta-data
144-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
145                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
145-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
147            <meta-data
147-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
148                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
148-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
150            <meta-data
150-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
151                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
151-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
153        </service>
154
155        <provider
155-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
156            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
156-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
157            android:authorities="com.minhduc.naugiday.flutter.image_provider"
157-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
158            android:exported="false"
158-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
159            android:grantUriPermissions="true" >
159-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
160            <meta-data
160-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
162                android:resource="@xml/flutter_image_picker_file_paths" />
162-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
163        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
164        <service
164-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
165            android:name="com.google.android.gms.metadata.ModuleDependencies"
165-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
166            android:enabled="false"
166-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
167            android:exported="false" >
167-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
168            <intent-filter>
168-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
169                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
169-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
169-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
170            </intent-filter>
171
172            <meta-data
172-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
173                android:name="photopicker_activity:0:required"
173-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
174                android:value="" />
174-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
175        </service>
176
177        <activity
177-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
178            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
178-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
179            android:exported="false"
179-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
180            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
180-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
181        <activity
181-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
182            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
182-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
183            android:excludeFromRecents="true"
183-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
184            android:exported="true"
184-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
185            android:launchMode="singleTask"
185-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
186            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
186-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
187            <intent-filter>
187-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
188                <action android:name="android.intent.action.VIEW" />
188-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
188-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
189
190                <category android:name="android.intent.category.DEFAULT" />
190-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:17-76
190-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:27-73
191                <category android:name="android.intent.category.BROWSABLE" />
191-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:17-78
191-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:27-75
192
193                <data
193-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
194                    android:host="firebase.auth"
195                    android:path="/"
196                    android:scheme="genericidp" />
197            </intent-filter>
198        </activity>
199        <activity
199-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
200            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
200-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
201            android:excludeFromRecents="true"
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
202            android:exported="true"
202-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
203            android:launchMode="singleTask"
203-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
204            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
204-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
205            <intent-filter>
205-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
206                <action android:name="android.intent.action.VIEW" />
206-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
206-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
207
208                <category android:name="android.intent.category.DEFAULT" />
208-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:17-76
208-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:27-73
209                <category android:name="android.intent.category.BROWSABLE" />
209-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:17-78
209-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:27-75
210
211                <data
211-->D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
212                    android:host="firebase.auth"
213                    android:path="/"
214                    android:scheme="recaptcha" />
215            </intent-filter>
216        </activity>
217
218        <property
218-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
219            android:name="android.adservices.AD_SERVICES_CONFIG"
219-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
220            android:resource="@xml/ga_ad_services_config" />
220-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
221
222        <activity
222-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
223            android:name="com.google.android.gms.common.api.GoogleApiActivity"
223-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
224            android:exported="false"
224-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
225            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
225-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
226
227        <provider
227-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
228            android:name="com.google.firebase.provider.FirebaseInitProvider"
228-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
229            android:authorities="com.minhduc.naugiday.firebaseinitprovider"
229-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
230            android:directBootAware="true"
230-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
231            android:exported="false"
231-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
232            android:initOrder="100" />
232-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
233
234        <receiver
234-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
235            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
235-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
236            android:enabled="true"
236-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
237            android:exported="false" >
237-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
238        </receiver>
239
240        <service
240-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
241            android:name="com.google.android.gms.measurement.AppMeasurementService"
241-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
242            android:enabled="true"
242-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
243            android:exported="false" />
243-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
244        <service
244-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
245            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
245-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
246            android:enabled="true"
246-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
247            android:exported="false"
247-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
248            android:permission="android.permission.BIND_JOB_SERVICE" />
248-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
249
250        <uses-library
250-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
251            android:name="androidx.window.extensions"
251-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
252            android:required="false" />
252-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
253        <uses-library
253-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
254            android:name="androidx.window.sidecar"
254-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
255            android:required="false" />
255-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
256        <uses-library
256-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
257            android:name="android.ext.adservices"
257-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
258            android:required="false" />
258-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
259
260        <meta-data
260-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
261            android:name="com.google.android.gms.version"
261-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
262            android:value="@integer/google_play_services_version" />
262-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
263
264        <provider
264-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
265            android:name="androidx.startup.InitializationProvider"
265-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
266            android:authorities="com.minhduc.naugiday.androidx-startup"
266-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
267            android:exported="false" >
267-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
268            <meta-data
268-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
269                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
269-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
270                android:value="androidx.startup" />
270-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
271            <meta-data
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
272                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
273                android:value="androidx.startup" />
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
274        </provider>
275
276        <receiver
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
277            android:name="androidx.profileinstaller.ProfileInstallReceiver"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
278            android:directBootAware="false"
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
279            android:enabled="true"
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
280            android:exported="true"
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
281            android:permission="android.permission.DUMP" >
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
282            <intent-filter>
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
283                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
284            </intent-filter>
285            <intent-filter>
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
286                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
287            </intent-filter>
288            <intent-filter>
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
289                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
290            </intent-filter>
291            <intent-filter>
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
292                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
293            </intent-filter>
294        </receiver>
295    </application>
296
297</manifest>
