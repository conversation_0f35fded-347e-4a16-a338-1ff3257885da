  Boolean android.app.Activity  	Exception android.app.Activity  
FlutterEngine android.app.Activity  Intent android.app.Activity  Log android.app.Activity  
MethodChannel android.app.Activity  String android.app.Activity  Uri android.app.Activity  configureFlutterEngine android.app.Activity  openUrl android.app.Activity  
startActivity android.app.Activity  
ComponentName android.content  Intent android.content  equals android.content.ComponentName  Boolean android.content.Context  	Exception android.content.Context  
FlutterEngine android.content.Context  Intent android.content.Context  Log android.content.Context  
MethodChannel android.content.Context  String android.content.Context  Uri android.content.Context  configureFlutterEngine android.content.Context  openUrl android.content.Context  
startActivity android.content.Context  Boolean android.content.ContextWrapper  	Exception android.content.ContextWrapper  
FlutterEngine android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  String android.content.ContextWrapper  Uri android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  openUrl android.content.ContextWrapper  
startActivity android.content.ContextWrapper  ACTION_VIEW android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  addFlags android.content.Intent  resolveActivity android.content.Intent  PackageManager android.content.pm  Uri android.net  parse android.net.Uri  Bundle 
android.os  Log android.util  e android.util.Log  Boolean  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  
FlutterEngine  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  openUrl  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  Boolean com.example.nau_gi_day  	Exception com.example.nau_gi_day  Intent com.example.nau_gi_day  Log com.example.nau_gi_day  MainActivity com.example.nau_gi_day  
MethodChannel com.example.nau_gi_day  String com.example.nau_gi_day  Uri com.example.nau_gi_day  Boolean #com.example.nau_gi_day.MainActivity  CHANNEL #com.example.nau_gi_day.MainActivity  	Exception #com.example.nau_gi_day.MainActivity  
FlutterEngine #com.example.nau_gi_day.MainActivity  Intent #com.example.nau_gi_day.MainActivity  Log #com.example.nau_gi_day.MainActivity  
MethodChannel #com.example.nau_gi_day.MainActivity  String #com.example.nau_gi_day.MainActivity  Uri #com.example.nau_gi_day.MainActivity  getPACKAGEManager #com.example.nau_gi_day.MainActivity  getPackageManager #com.example.nau_gi_day.MainActivity  openUrl #com.example.nau_gi_day.MainActivity  packageManager #com.example.nau_gi_day.MainActivity  setPackageManager #com.example.nau_gi_day.MainActivity  
startActivity #com.example.nau_gi_day.MainActivity  Boolean com.minhduc.naugiday  	Exception com.minhduc.naugiday  Intent com.minhduc.naugiday  Log com.minhduc.naugiday  MainActivity com.minhduc.naugiday  
MethodChannel com.minhduc.naugiday  String com.minhduc.naugiday  Uri com.minhduc.naugiday  Boolean !com.minhduc.naugiday.MainActivity  CHANNEL !com.minhduc.naugiday.MainActivity  	Exception !com.minhduc.naugiday.MainActivity  
FlutterEngine !com.minhduc.naugiday.MainActivity  Intent !com.minhduc.naugiday.MainActivity  Log !com.minhduc.naugiday.MainActivity  
MethodChannel !com.minhduc.naugiday.MainActivity  String !com.minhduc.naugiday.MainActivity  Uri !com.minhduc.naugiday.MainActivity  getPACKAGEManager !com.minhduc.naugiday.MainActivity  getPackageManager !com.minhduc.naugiday.MainActivity  openUrl !com.minhduc.naugiday.MainActivity  packageManager !com.minhduc.naugiday.MainActivity  setPackageManager !com.minhduc.naugiday.MainActivity  
startActivity !com.minhduc.naugiday.MainActivity  FlutterActivity io.flutter.embedding.android  Boolean ,io.flutter.embedding.android.FlutterActivity  	Exception ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine ,io.flutter.embedding.android.FlutterActivity  Intent ,io.flutter.embedding.android.FlutterActivity  Log ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  String ,io.flutter.embedding.android.FlutterActivity  Uri ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  openUrl ,io.flutter.embedding.android.FlutterActivity  
startActivity ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  getDARTExecutor )io.flutter.embedding.engine.FlutterEngine  getDartExecutor )io.flutter.embedding.engine.FlutterEngine  setDartExecutor )io.flutter.embedding.engine.FlutterEngine  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBINARYMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  setBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  	Exception 	java.lang  Intent 	java.lang  Log 	java.lang  
MethodChannel 	java.lang  Uri 	java.lang  message java.lang.Exception  Boolean kotlin  	Exception kotlin  	Function2 kotlin  Int kotlin  Intent kotlin  Log kotlin  
MethodChannel kotlin  Nothing kotlin  String kotlin  Uri kotlin  	Exception kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  
MethodChannel kotlin.annotation  Uri kotlin.annotation  	Exception kotlin.collections  Intent kotlin.collections  Log kotlin.collections  
MethodChannel kotlin.collections  Uri kotlin.collections  	Exception kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  
MethodChannel kotlin.comparisons  Uri kotlin.comparisons  	Exception 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  
MethodChannel 	kotlin.io  Uri 	kotlin.io  	Exception 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Uri 
kotlin.jvm  	Exception 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Uri 
kotlin.ranges  	Exception kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  
MethodChannel kotlin.sequences  Uri kotlin.sequences  	Exception kotlin.text  Intent kotlin.text  Log kotlin.text  
MethodChannel kotlin.text  Uri kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      