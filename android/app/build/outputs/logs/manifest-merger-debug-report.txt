-- Merging decision tree log ---
application
INJECTED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:6:5-46:19
MERGED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:6:5-46:19
MERGED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:6:5-46:19
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:8:5-55
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f9fec2fc3d0f28680f5b2eb75e6f129\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f9fec2fc3d0f28680f5b2eb75e6f129\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448a99444dc8e6dddca838bc4d9ab010\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448a99444dc8e6dddca838bc4d9ab010\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0db43028325835ef6b33bc1ba0a835cb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0db43028325835ef6b33bc1ba0a835cb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132bfd3457d7cee07aad0c3f2de02ccc\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132bfd3457d7cee07aad0c3f2de02ccc\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f869136c93509ea251fb4456912f2dce\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f869136c93509ea251fb4456912f2dce\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0d16e26ca8265f091d5e5d1c0dab92\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0d16e26ca8265f091d5e5d1c0dab92\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f10b3fa5b6523f472b2e016ccdf3ec3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f10b3fa5b6523f472b2e016ccdf3ec3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50f790cbd740a9faaef63bc4904facbd\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50f790cbd740a9faaef63bc4904facbd\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfced024e7affa2311eb440ce490c010\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfced024e7affa2311eb440ce490c010\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030795fc0a8c2549c1bee1b6725caefa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030795fc0a8c2549c1bee1b6725caefa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:7:9-36
	android:icon
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:9:9-43
	android:enableOnBackInvokedCallback
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:11:9-51
	android:usesCleartextTraffic
		ADDED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:8:18-53
	android:name
		INJECTED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:8:9-42
manifest
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:1:1-70:12
MERGED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:1:1-70:12
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml:1:1-9:12
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.27\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f9fec2fc3d0f28680f5b2eb75e6f129\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448a99444dc8e6dddca838bc4d9ab010\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b3ad225ba2c6dfce1ed4def198cad\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27165ab8e79340a38f577774f57dceac\transformed\jetified-play-services-auth-api-phone-17.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0db43028325835ef6b33bc1ba0a835cb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132bfd3457d7cee07aad0c3f2de02ccc\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f869136c93509ea251fb4456912f2dce\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\360556df4f176453e5abbca6a333bc85\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c1f763cf9b14d042d4ed5256a1a54e5\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fe99b7f40d89e111880dc57960eacaf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bd4764f73abbfe85f9acd8bfc747afc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a00b59d66f3d447d04efbf7a958ff46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9fcc3433c6b5fbfb1356cab4d885d1b\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65013c703169629673594e4079d19301\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370de4371ecd0a8bf900c734129b4e29\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ef594f039dba070ceddab075a3b5e0b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0d16e26ca8265f091d5e5d1c0dab92\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e14c918ae462ca4a521e7d227f59b2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fc7382551b4cc65e30083a08a27fb27\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c244971f121ac3f07970422760b24ec8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3ce96bc516d06328220b9336fadbf8a\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2213e7e880d1e3d4fd354adeb899e74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\703a029afa8ae6708e36d92aa8c02834\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e4a8bab39f14832991193a5e855f150\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7efb4afa86ec31f63d62c33425063596\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab41d695728f58a43f49998fe65b1278\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40486e544f320ef431f2e33516a5ca33\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fa4250157b4ed2a7b498c0b5fb63607\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52336d9f3df191003ecbd00b3a9204f2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f10b3fa5b6523f472b2e016ccdf3ec3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50f790cbd740a9faaef63bc4904facbd\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfced024e7affa2311eb440ce490c010\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2773ef5e8c283dd34ec11a75bee9ef56\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68afd83b2b6fa844ff20ba6374c68e95\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9e73564b4e591fabb4bd610cca5212b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e5c46c35b9a4945a886a57fdcd4f2d\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45302c91a52a43375dba5a25de689f5c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e32daa78bbd4347dc589cbd844af8127\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37e6f4284884fc15d81738976d5f6869\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56c5e94cff56354563265dcaa53a651\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c0d80527de211e7e83e2306987ea4c2\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5a2965eba6fc66c7eb05f308fb9c7a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fff8ee24f460e150dbfe596088dc901c\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2a942810e0303e013faf2fa2b8e67a9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8955ec0a03f9bf12749cba5a5adeb20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486710373440df6ce9a4049706e94a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46f96eddd6ba27d208b4e6c5bc6f0c4d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc83c0667442857f0500d13eb586f1a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1b1c305be34db37cacba4d627634d54\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\005b8f2f4421283f768bde638223fadb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15be965bbfba02c55ee58184915333d2\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b9d9d87a19f0844020dfdb0d0c7ee1e\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fa555582095ee64081f618c54a1c0f\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef31dfbe09fb63b58bc135b57181c905\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3558bc2e67def886a198d65d56d2865d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030795fc0a8c2549c1bee1b6725caefa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba317318caf3aa40c495818cb80d8a71\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dd6329fe7e057f82c1793c9062bb5b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e514946cfb1c01a50f4ee64ef700e05\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b87193f6a3c0c4e3892c3fbf7a4147fa\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9fd4cad595ab2f2d44244bedb536b72\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f4c40435b138510a427d453f5e786c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e4e0ead94ae8545ec04c721f5ef91a5\transformed\multidex-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dea277fa96608e489e2df1dcc77d727a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d07a15d95f44f0f4754ad00c5515d20\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fcc7691634fd81ab2cb9b350ea879b3\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:2:5-76
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:2:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:5-107
	android:maxSdkVersion
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:78-104
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:3:22-77
uses-permission#android.permission.CAMERA
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:4:5-65
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:4:22-62
uses-permission#android.permission.INTERNET
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:5:22-64
queries
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:52:5-69:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:53:9-56:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:54:13-72
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:54:21-70
data
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:13-50
	android:mimeType
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:55:19-48
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:57:9-60:18
action#android.intent.action.VIEW
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:13-65
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:58:21-62
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:61:9-64:18
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:65:9-68:18
action#android.intent.action.SEND
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:66:13-65
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:66:21-62
activity#com.minhduc.naugiday.MainActivity
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:12:9-40:20
	android:launchMode
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:15:13-43
	android:hardwareAccelerated
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:19:13-47
	android:windowSoftInputMode
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:20:13-52
	android:exported
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:14:13-36
	android:configChanges
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:18:13-163
	android:theme
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:17:13-47
	android:taskAffinity
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:16:13-36
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:13:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:25:13-28:17
	android:resource
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:27:15-52
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:26:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:29:13-32:29
action#android.intent.action.MAIN
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:30:17-68
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:30:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:31:17-76
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:31:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:http+data:scheme:https
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:33:13-39:29
category#android.intent.category.DEFAULT
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:17-76
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:35:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:17-78
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:36:27-75
meta-data#flutterEmbedding
ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:43:9-45:33
	android:value
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:45:13-30
	android:name
		ADDED from D:\AI\nau_gi_day\android\app\src\main\AndroidManifest.xml:44:13-44
uses-sdk
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.27\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.27\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f9fec2fc3d0f28680f5b2eb75e6f129\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f9fec2fc3d0f28680f5b2eb75e6f129\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448a99444dc8e6dddca838bc4d9ab010\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448a99444dc8e6dddca838bc4d9ab010\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b3ad225ba2c6dfce1ed4def198cad\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b3ad225ba2c6dfce1ed4def198cad\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27165ab8e79340a38f577774f57dceac\transformed\jetified-play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27165ab8e79340a38f577774f57dceac\transformed\jetified-play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0db43028325835ef6b33bc1ba0a835cb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0db43028325835ef6b33bc1ba0a835cb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132bfd3457d7cee07aad0c3f2de02ccc\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132bfd3457d7cee07aad0c3f2de02ccc\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f869136c93509ea251fb4456912f2dce\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f869136c93509ea251fb4456912f2dce\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\360556df4f176453e5abbca6a333bc85\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\360556df4f176453e5abbca6a333bc85\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c1f763cf9b14d042d4ed5256a1a54e5\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c1f763cf9b14d042d4ed5256a1a54e5\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fe99b7f40d89e111880dc57960eacaf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0fe99b7f40d89e111880dc57960eacaf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bd4764f73abbfe85f9acd8bfc747afc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bd4764f73abbfe85f9acd8bfc747afc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a00b59d66f3d447d04efbf7a958ff46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a00b59d66f3d447d04efbf7a958ff46\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9fcc3433c6b5fbfb1356cab4d885d1b\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f9fcc3433c6b5fbfb1356cab4d885d1b\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65013c703169629673594e4079d19301\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65013c703169629673594e4079d19301\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370de4371ecd0a8bf900c734129b4e29\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370de4371ecd0a8bf900c734129b4e29\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ef594f039dba070ceddab075a3b5e0b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ef594f039dba070ceddab075a3b5e0b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0d16e26ca8265f091d5e5d1c0dab92\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e0d16e26ca8265f091d5e5d1c0dab92\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e14c918ae462ca4a521e7d227f59b2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e14c918ae462ca4a521e7d227f59b2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fc7382551b4cc65e30083a08a27fb27\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fc7382551b4cc65e30083a08a27fb27\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c244971f121ac3f07970422760b24ec8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c244971f121ac3f07970422760b24ec8\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3ce96bc516d06328220b9336fadbf8a\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3ce96bc516d06328220b9336fadbf8a\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2213e7e880d1e3d4fd354adeb899e74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2213e7e880d1e3d4fd354adeb899e74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\703a029afa8ae6708e36d92aa8c02834\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\703a029afa8ae6708e36d92aa8c02834\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e4a8bab39f14832991193a5e855f150\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e4a8bab39f14832991193a5e855f150\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7efb4afa86ec31f63d62c33425063596\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7efb4afa86ec31f63d62c33425063596\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab41d695728f58a43f49998fe65b1278\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab41d695728f58a43f49998fe65b1278\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40486e544f320ef431f2e33516a5ca33\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40486e544f320ef431f2e33516a5ca33\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fa4250157b4ed2a7b498c0b5fb63607\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fa4250157b4ed2a7b498c0b5fb63607\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52336d9f3df191003ecbd00b3a9204f2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52336d9f3df191003ecbd00b3a9204f2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f10b3fa5b6523f472b2e016ccdf3ec3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f10b3fa5b6523f472b2e016ccdf3ec3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50f790cbd740a9faaef63bc4904facbd\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50f790cbd740a9faaef63bc4904facbd\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfced024e7affa2311eb440ce490c010\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfced024e7affa2311eb440ce490c010\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2773ef5e8c283dd34ec11a75bee9ef56\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2773ef5e8c283dd34ec11a75bee9ef56\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68afd83b2b6fa844ff20ba6374c68e95\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68afd83b2b6fa844ff20ba6374c68e95\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9e73564b4e591fabb4bd610cca5212b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9e73564b4e591fabb4bd610cca5212b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e5c46c35b9a4945a886a57fdcd4f2d\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e5c46c35b9a4945a886a57fdcd4f2d\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45302c91a52a43375dba5a25de689f5c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45302c91a52a43375dba5a25de689f5c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e32daa78bbd4347dc589cbd844af8127\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e32daa78bbd4347dc589cbd844af8127\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37e6f4284884fc15d81738976d5f6869\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37e6f4284884fc15d81738976d5f6869\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56c5e94cff56354563265dcaa53a651\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c56c5e94cff56354563265dcaa53a651\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c0d80527de211e7e83e2306987ea4c2\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c0d80527de211e7e83e2306987ea4c2\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5a2965eba6fc66c7eb05f308fb9c7a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5a2965eba6fc66c7eb05f308fb9c7a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fff8ee24f460e150dbfe596088dc901c\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fff8ee24f460e150dbfe596088dc901c\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2a942810e0303e013faf2fa2b8e67a9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2a942810e0303e013faf2fa2b8e67a9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8955ec0a03f9bf12749cba5a5adeb20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8955ec0a03f9bf12749cba5a5adeb20\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486710373440df6ce9a4049706e94a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486710373440df6ce9a4049706e94a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46f96eddd6ba27d208b4e6c5bc6f0c4d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46f96eddd6ba27d208b4e6c5bc6f0c4d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc83c0667442857f0500d13eb586f1a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfc83c0667442857f0500d13eb586f1a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1b1c305be34db37cacba4d627634d54\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1b1c305be34db37cacba4d627634d54\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\005b8f2f4421283f768bde638223fadb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\005b8f2f4421283f768bde638223fadb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15be965bbfba02c55ee58184915333d2\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15be965bbfba02c55ee58184915333d2\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b9d9d87a19f0844020dfdb0d0c7ee1e\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b9d9d87a19f0844020dfdb0d0c7ee1e\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fa555582095ee64081f618c54a1c0f\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91fa555582095ee64081f618c54a1c0f\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef31dfbe09fb63b58bc135b57181c905\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef31dfbe09fb63b58bc135b57181c905\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3558bc2e67def886a198d65d56d2865d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3558bc2e67def886a198d65d56d2865d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030795fc0a8c2549c1bee1b6725caefa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030795fc0a8c2549c1bee1b6725caefa\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba317318caf3aa40c495818cb80d8a71\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba317318caf3aa40c495818cb80d8a71\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dd6329fe7e057f82c1793c9062bb5b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2dd6329fe7e057f82c1793c9062bb5b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e514946cfb1c01a50f4ee64ef700e05\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e514946cfb1c01a50f4ee64ef700e05\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b87193f6a3c0c4e3892c3fbf7a4147fa\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b87193f6a3c0c4e3892c3fbf7a4147fa\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9fd4cad595ab2f2d44244bedb536b72\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9fd4cad595ab2f2d44244bedb536b72\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f4c40435b138510a427d453f5e786c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f4c40435b138510a427d453f5e786c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e4e0ead94ae8545ec04c721f5ef91a5\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e4e0ead94ae8545ec04c721f5ef91a5\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dea277fa96608e489e2df1dcc77d727a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dea277fa96608e489e2df1dcc77d727a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d07a15d95f44f0f4754ad00c5515d20\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d07a15d95f44f0f4754ad00c5515d20\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fcc7691634fd81ab2cb9b350ea879b3\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fcc7691634fd81ab2cb9b350ea879b3\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\AI\nau_gi_day\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-4.17.5\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-4.16.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+22\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.15\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d07a15d95f44f0f4754ad00c5515d20\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d07a15d95f44f0f4754ad00c5515d20\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:22-76
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d5e00ee8844ac92c29bbe013638032\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3703b8983f99b8b1a048c51ee8ddf299\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0f4f983773b07d15b0f50a947d6fe14\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ebdd1fd36a1c3361918c9a2e5428e6\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d176366e84a1292d494fe80dee9a7df\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec5bfab9a6644a1bb357391e755ebcf3\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f7c2ad0fdbbef479306b8ef928b8839\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\858b17db14955c6c13fe11a85cd78513\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\085ab41ea4f42e73445eaa663d898463\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09018ca9937fd33aaaad4a72813ba8f9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6bc9dc74e0beab6aec03c8747fd8e9a0\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d535b28d675afa6239fbdd37d34e0b73\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9700d47d5033b8f697f524f662ce902\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d42c39ee2a0da4d22904321ace4760e\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49cbc1df839b72e71eacdce3e63bd8d6\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.minhduc.naugiday.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.minhduc.naugiday.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3392205747088b59d9ceb5045d2b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19031e7cd6b14e3a491c801a7cdd6bae\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09121472ff0e55e7c79f4c923dc75fb4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9affc18a094aee08051066b03a184c58\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
