## Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-dontwarn io.flutter.embedding.**

## url_launcher
-keep class androidx.lifecycle.DefaultLifecycleObserver 

## Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

## Keep your application class
-keep class com.minhduc.naugiday.** { *; }

## Keep all classes that might be used via reflection
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes EnclosingMethod

## Keep all public classes, and their public and protected fields and methods
-keep public class * {
    public protected *;
}

## Keep all classes that extend Activity
-keep public class * extends android.app.Activity

## Keep all classes that extend Application
-keep public class * extends android.app.Application

## Keep all classes that extend Service
-keep public class * extends android.app.Service

## Keep all classes that extend BroadcastReceiver
-keep public class * extends android.content.BroadcastReceiver

## Keep all classes that extend ContentProvider
-keep public class * extends android.content.ContentProvider

# Google Sign In
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-keepattributes JavascriptInterface
-keepattributes *Annotation*

-dontwarn com.google.android.gms.**
-keep class com.google.android.gms.** { *; }

-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Firebase Auth
-keep class com.google.firebase.** { *; }
-keep class com.firebase.** { *; }
-keep class org.apache.** { *; }
-keepnames class com.fasterxml.jackson.** { *; }
-keepnames class javax.servlet.** { *; }
-keepnames class org.ietf.jgss.** { *; }
-dontwarn org.w3c.dom.**
-dontwarn org.joda.time.**
-dontwarn org.shaded.apache.**
-dontwarn org.ietf.jgss.** 