package com.example.nau_gi_day

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.nau_gi_day/url_launcher"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "openUrl") {
                val urlString = call.argument<String>("url")
                if (urlString != null) {
                    try {
                        val openUrl = openUrl(urlString)
                        result.success(openUrl)
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Error opening URL: $urlString", e)
                        result.error("UNAVAILABLE", "Could not open URL: ${e.message}", null)
                    }
                } else {
                    result.error("NULL_URL", "URL string is null", null)
                }
            } else {
                result.notImplemented()
            }
        }
    }

    private fun openUrl(urlString: String): Boolean {
        val uri = Uri.parse(urlString)
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        
        try {
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
                return true
            }
            
            // Nếu không tìm thấy ứng dụng phù hợp, thử dùng cách khác
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(urlString))
            browserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(browserIntent)
            return true
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to open URL", e)
            return false
        }
    }
}
