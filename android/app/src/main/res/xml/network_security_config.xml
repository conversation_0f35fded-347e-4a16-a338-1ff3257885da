<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow all connections for development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Supabase domain -->
        <domain includeSubdomains="true">jypmpcvdjhdnenaifamc.supabase.co</domain>
        <domain includeSubdomains="true">supabase.co</domain>
        
        <!-- Google services -->
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        <domain includeSubdomains="true">googleusercontent.com</domain>
        
        <!-- YouTube -->
        <domain includeSubdomains="true">youtube.com</domain>
        <domain includeSubdomains="true">youtubei.googleapis.com</domain>
        
        <!-- Local development -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
