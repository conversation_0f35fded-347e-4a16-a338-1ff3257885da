# Cleanup Plan cho CookSpark Big Update

## Files cần XÓA BỎ (không phù hợp với định hướng mới)

### 1. Meal Planning System (cũ)
- `lib/services/meal_plan_service.dart` ❌
- `lib/providers/meal_plan_provider.dart` ❌
- `lib/screens/meal_calendar_screen.dart` ❌
- `lib/models/meal_plan.dart` ❌

### 2. Dish Management System (cũ)
- `lib/services/dish_service.dart` ❌
- `lib/services/user_dish_service.dart` ❌
- `lib/screens/add_dish_screen_1_image.dart` ❌
- `lib/screens/add_dish_screen_2_name.dart` ❌
- `lib/screens/add_dish_screen_3_details.dart` ❌
- `lib/screens/add_dish_screen_4_review.dart` ❌
- `lib/screens/edit_dish_screen.dart` ❌
- `lib/screens/all_dishes_screen.dart` ❌
- `lib/screens/dish_detail_screen.dart` ❌
- `lib/models/dish.dart` ❌

### 3. Recipe Features (cũ)
- `lib/services/recipe_service.dart` ❌
- `lib/data/food_calorie_data.dart` ❌
- `lib/widgets/ingredient_item.dart` ❌

### 4. Old Providers (cũ)
- `lib/providers/menu_provider.dart` ❌
- `lib/providers/meal_history_provider.dart` ❌

### 5. Old Models (cũ)
- `lib/models/meal.dart` ❌
- `lib/models/meal_history.dart` ❌

### 6. Old Services (cũ)
- `lib/services/meal_history_service.dart` ❌
- `lib/services/week_menu_service.dart` ❌
- `lib/services/favorites_service.dart` ❌
- `lib/services/sync_service.dart` ❌
- `lib/services/user_profile_service.dart` (thay bằng new_user_profile_service.dart)

### 7. Old Widgets (cũ)
- `lib/widgets/dish_card.dart` ❌
- `lib/widgets/meal_card.dart` ❌
- `lib/widgets/image_upload_widget.dart` ❌

## Files cần GIỮ LẠI (tái sử dụng)

### 1. Authentication System ✅
- `lib/services/auth_service.dart`
- `lib/services/supabase_auth_service.dart`
- `lib/screens/login_screen.dart`
- `lib/screens/register_screen.dart`
- `lib/screens/forgot_password_otp_screen.dart`
- `lib/screens/reset_password_screen.dart`
- `lib/screens/otp_verification_screen.dart`
- `lib/screens/auth_wrapper.dart`

### 2. Core Infrastructure ✅
- `lib/supabase_options.dart`
- `lib/services/supabase_integration_service.dart`
- `lib/services/supabase_interceptor.dart`
- `lib/services/supabase_rate_limit_service.dart`

### 3. UI Components ✅
- `lib/providers/theme_provider.dart`
- `lib/constants/app_text_styles.dart`
- `lib/widgets/safe_text.dart`
- `lib/widgets/user_info_widget.dart`
- `lib/widgets/auth_error_wrapper.dart`
- `lib/widgets/rate_limit_widget.dart`
- `lib/widgets/supabase_status_widget.dart`

### 4. Utilities ✅
- `lib/utils/app_init.dart`
- `lib/utils/logger.dart`
- `lib/utils/oauth_helper.dart`
- `lib/utils/url_launcher_helper.dart`

### 5. Core Screens ✅
- `lib/screens/settings_screen.dart`
- `lib/screens/about_screen.dart`
- `lib/screens/author_screen.dart`
- `lib/screens/loading_screen.dart`
- `lib/screens/splash_screen.dart`
- `lib/screens/user_profile_screen.dart` (cần update)

### 6. Configuration ✅
- `lib/config/google_config.dart`
- `pubspec.yaml` (đã update)
- `analysis_options.yaml`

## Files MỚI đã tạo

### 1. New Models ✅
- `lib/models/user_profile.dart`
- `lib/models/weekly_menu.dart`

### 2. New Services ✅
- `lib/services/new_user_profile_service.dart`
- `lib/services/youtube_service.dart`
- `lib/services/weekly_menu_service.dart`

### 3. New Screens ✅
- `lib/screens/onboarding_screen.dart`
- `lib/screens/new_home_screen.dart`
- `lib/screens/meal_suggestion_screen.dart`
- `lib/screens/meal_suggestion_results_screen.dart`
- `lib/screens/youtube_player_screen.dart`
- `lib/screens/weekly_menu_screen.dart`

### 4. New Providers ✅
- `lib/providers/user_profile_provider.dart`

### 5. New Widgets ✅
- `lib/widgets/ingredient_icons.dart`

### 6. Database ✅
- `supabase/migrations/20241228_new_app_schema.sql`

## Cập nhật cần thiết

### 1. main.dart
- Xóa routes cũ
- Thêm routes mới
- Update providers

### 2. home_screen.dart
- Thay thế bằng new_home_screen.dart
- Hoặc refactor để phù hợp với định hướng mới

### 3. Navigation
- Update tất cả navigation references
- Xóa các route không còn sử dụng

### 4. Dependencies
- Xóa dependencies không cần thiết
- Giữ lại dependencies cần thiết

## Thứ tự thực hiện cleanup

1. **Backup code cũ** (tạo branch backup)
2. **Xóa files không cần thiết** theo danh sách trên
3. **Update main.dart** với routes mới
4. **Update navigation** trong toàn bộ app
5. **Test authentication flow** vẫn hoạt động
6. **Test new features** hoạt động đúng
7. **Update documentation**

## Lưu ý quan trọng

- **KHÔNG xóa** authentication system
- **KHÔNG xóa** core infrastructure
- **BACKUP** trước khi xóa
- **Test thoroughly** sau khi cleanup
- **Update imports** trong các file còn lại
- **Remove unused dependencies** từ pubspec.yaml

## Commands để thực hiện

```bash
# Backup current code
git checkout -b backup-before-cleanup
git add .
git commit -m "Backup before big cleanup"

# Switch to main branch
git checkout main

# Remove old files (example)
rm lib/services/dish_service.dart
rm lib/services/meal_plan_service.dart
# ... continue with other files

# Update imports and fix errors
# Test the app
# Commit changes
git add .
git commit -m "Big cleanup: Remove old features, keep core infrastructure"
```
