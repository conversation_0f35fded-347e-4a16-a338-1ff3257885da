# 🎨 Tổng quan Thiết kế Giao diện

## 📱 CookSpark là gì?
CookSpark là ứng dụng quản lý món ăn và lập kế hoạch bữa ăn, đượ<PERSON> viết bằng Flutter. Ứng dụng giúp người dùng:
- Lên thực đơn cho tuần
- Thêm món ăn yêu thích
- <PERSON> dõi lịch sử ăn uống
- Tính toán calories

## 🎯 Nguyên tắc thiết kế

### 1. Đơn giản và dễ sử dụng
- **Màu sắc rõ ràng**: Mỗi bữa ăn có màu riêng
- **Icon dễ hiểu**: Dùng icon quen thuộc
- **Ít bước thao tác**: Làm việc nhanh chóng

### 2. Thân thiện với người Việt
- **100% tiếng Việt**: Không có từ tiếng Anh
- **Định dạng ngày Việt**: dd/MM/yyyy
- **Đơn vị quen thuộc**: gram, ml, muỗng canh

### 3. Hoạt động mượt mà
- **Chuyển màn hình đẹp**: Có hiệu ứng chuyển đổi
- **Phản hồi nhanh**: Nhấn nút có phản ứng ngay
- **Tải nhanh**: Ảnh được tối ưu

## 🏗️ Cấu trúc tổng thể

### Luồng chính của ứng dụng
```
Mở app → Đăng nhập → Màn hình chính → Các chức năng
```

### Các màn hình chính
1. **Màn hình đăng nhập** - Đăng nhập/đăng ký
2. **Màn hình chính** - Xem thực đơn tuần
3. **Danh sách món ăn** - Xem tất cả món
4. **Thêm món ăn** - Tạo món mới (4 bước)
5. **Lịch bữa ăn** - Xem lịch sử và thống kê
6. **Cài đặt** - Tùy chỉnh ứng dụng

## 🎨 Hệ thống màu sắc cơ bản

### Màu theo bữa ăn
- 🟠 **Bữa sáng**: Màu cam (năng động, bắt đầu ngày mới)
- 🟢 **Bữa trưa**: Màu xanh ngọc (tươi mát, giữa ngày)
- 🔵 **Bữa tối**: Màu chàm (ấm áp, kết thúc ngày)

### Màu chủ đề
- **Chế độ sáng**: Nền trắng, chữ đen
- **Chế độ tối**: Nền đen, chữ trắng
- **Màu nhấn**: Có thể thay đổi (xanh, tím, đỏ...)

## 📐 Bố cục màn hình

### Cấu trúc chung
```
┌─────────────────────┐
│    Thanh tiêu đề    │ ← Tên màn hình, nút back
├─────────────────────┤
│                     │
│    Nội dung chính   │ ← Thông tin chính
│                     │
├─────────────────────┤
│   Nút hành động     │ ← Nút thêm, lưu, xóa...
└─────────────────────┘
```

### Nguyên tắc bố cục
- **Từ trên xuống**: Thông tin quan trọng ở trên
- **Từ trái qua phải**: Theo thói quen đọc
- **Nhóm liên quan**: Thông tin cùng loại gần nhau

## 🔄 Cách di chuyển giữa màn hình

### Navigation Drawer (Menu bên)
Vuốt từ trái sang hoặc nhấn icon menu để mở:
- Trang chủ
- Tất cả món ăn  
- Thêm món mới
- Lịch ăn uống
- Cài đặt

### Bottom Navigation (Thanh dưới)
Một số màn hình có thanh điều hướng dưới để chuyển nhanh.

### Nút Back
Mỗi màn hình đều có nút quay lại ở góc trái trên.

## 📱 Thích ứng màn hình

### Kích thước màn hình
- **Màn hình nhỏ** (< 340px): Font nhỏ hơn, khoảng cách ít hơn
- **Màn hình vừa** (340-380px): Kích thước chuẩn
- **Màn hình lớn** (> 380px): Thoải mái hơn

### Hướng màn hình
- **Dọc**: Chế độ chính, tối ưu cho sử dụng
- **Ngang**: Hỗ trợ cơ bản, một số màn hình

## 🎭 Hiệu ứng và chuyển động

### Khi chuyển màn hình
- **Trượt từ phải**: Màn hình mới trượt vào từ bên phải
- **Mờ dần**: Một số màn hình có hiệu ứng mờ dần
- **Hero Animation**: Ảnh "bay" từ màn hình này sang màn hình khác

### Khi tương tác
- **Nhấn nút**: Có hiệu ứng gợn sóng
- **Chạm thẻ**: Thẻ sáng lên một chút
- **Tải dữ liệu**: Có vòng tròn xoay

## 🔧 Các thành phần cơ bản

### Thẻ (Card)
Hộp chứa thông tin, có viền bo tròn và đổ bóng nhẹ.

### Nút (Button)
- **Nút chính**: Màu nổi bật, cho hành động quan trọng
- **Nút phụ**: Màu nhạt hơn, cho hành động thứ yếu
- **Nút icon**: Chỉ có icon, không có chữ

### Ô nhập liệu (Input Field)
Nơi người dùng nhập thông tin, có viền và placeholder.

### Danh sách (List)
Hiển thị nhiều item cùng loại, có thể cuộn dọc.

## ⚠️ Xử lý lỗi và trạng thái

### Khi đang tải
- **Vòng tròn xoay**: Cho biết đang xử lý
- **Shimmer**: Hiệu ứng lấp lánh cho placeholder
- **Skeleton**: Khung sẵn chờ nội dung

### Khi có lỗi
- **Thông báo đỏ**: Hiện ở dưới màn hình
- **Icon cảnh báo**: Kèm theo thông báo lỗi
- **Nút thử lại**: Cho phép thử lại thao tác

### Khi không có dữ liệu
- **Icon lớn**: Biểu tượng minh họa
- **Thông báo**: "Chưa có dữ liệu"
- **Nút hành động**: "Thêm mới" hoặc "Tải lại"

## 💡 Mẹo sử dụng

### Cho developer mới
1. **Bắt đầu từ màn hình đơn giản**: Như màn hình "Về ứng dụng"
2. **Copy từ màn hình có sẵn**: Sửa đổi thay vì viết mới
3. **Test trên nhiều kích thước**: Dùng simulator khác nhau

### Cho designer
1. **Giữ nhất quán**: Dùng cùng màu sắc và font
2. **Ưu tiên mobile**: Thiết kế cho điện thoại trước
3. **Accessibility**: Đảm bảo người khuyết tật dùng được

## 🔍 Tìm hiểu thêm

### File liên quan
- **[02-he-thong-mau-sac.md](02-he-thong-mau-sac.md)**: Chi tiết về màu sắc
- **[08-thanh-phan-tai-su-dung.md](08-thanh-phan-tai-su-dung.md)**: Các widget có sẵn
- **[09-hieu-ung-chuyen-doi.md](09-hieu-ung-chuyen-doi.md)**: Animation chi tiết

### Code tham khảo
- `lib/constants/app_text_styles.dart`: Định nghĩa font chữ
- `lib/providers/theme_provider.dart`: Quản lý chủ đề
- `lib/widgets/`: Các widget tái sử dụng

---

*Đây là nền tảng để hiểu toàn bộ giao diện CookSpark. Đọc tiếp các file khác để hiểu chi tiết từng phần.*
