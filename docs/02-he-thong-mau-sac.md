# 🎨 <PERSON>ệ thống Màu sắc và Chủ đề

## 🌈 Tại sao màu sắc quan trọng?
<PERSON><PERSON><PERSON> sắc giúp người dùng:
- **Phân biệt nhanh**: Bi<PERSON>t đây là bữ<PERSON> sáng, tr<PERSON><PERSON> hay tối
- **Tạo cảm xúc**: <PERSON><PERSON><PERSON> ấm tạo cảm giác thân thiện
- **D<PERSON> nhớ**: Người dùng nhớ màu hơn là chữ

## 🍽️ Màu theo bữa ăn

### 🟠 Bữa sáng - Màu Cam
```dart
Color breakfastColor = Colors.orange;
```
**Tại sao chọn cam?**
- <PERSON><PERSON>u của mặt trời buổi sáng
- Tạo cảm giác năng động, khởi đầu ngày mới
- Kích thích cảm giác thèm ăn

**Sử dụng ở đâu?**
- Header của thẻ bữa sáng
- <PERSON><PERSON> bữa sáng trong lịch
- Viền khi chọn món cho bữa sáng

### 🟢 Bữa trưa - <PERSON><PERSON><PERSON>h ngọc
```dart
Color lunchColor = Colors.teal;
```
**Tại sao chọn xanh ngọc?**
- Màu tươi mát, phù hợp giữa ngày
- Tạo cảm giác thư giãn, nghỉ ngơi
- Không quá nổi bật, cân bằng

**Sử dụng ở đâu?**
- Header của thẻ bữa trưa
- Nút "Đã ăn trưa"
- Background của meal card khi được chọn

### 🔵 Bữa tối - Màu Chàm
```dart
Color dinnerColor = Colors.indigo;
```
**Tại sao chọn chàm?**
- Màu của bầu trời buổi tối
- Tạo cảm giác ấm áp, kết thúc ngày
- Màu sang trọng, phù hợp bữa tối

**Sử dụng ở đâu?**
- Header của thẻ bữa tối
- Highlight khi xem chi tiết bữa tối
- Progress bar cho thống kê tối

## 🎭 Chế độ Sáng và Tối

### ☀️ Chế độ Sáng (Light Mode)
```dart
ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  primarySwatch: Colors.blue,
  backgroundColor: Colors.white,
  textTheme: TextTheme(
    bodyText1: TextStyle(color: Colors.black87),
  ),
);
```

**Đặc điểm:**
- Nền trắng hoặc xám nhạt
- Chữ đen hoặc xám đậm
- Phù hợp dùng ban ngày
- Tiết kiệm pin trên màn hình LCD

### 🌙 Chế độ Tối (Dark Mode)
```dart
ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  primarySwatch: Colors.blue,
  backgroundColor: Colors.black,
  textTheme: TextTheme(
    bodyText1: TextStyle(color: Colors.white70),
  ),
);
```

**Đặc điểm:**
- Nền đen hoặc xám đậm
- Chữ trắng hoặc xám nhạt
- Phù hợp dùng ban đêm
- Tiết kiệm pin trên màn hình OLED

### 🔄 Chuyển đổi chế độ
```dart
// Trong ThemeProvider
void toggleDarkMode() {
  _darkMode = !_darkMode;
  notifyListeners(); // Báo cho UI cập nhật
}
```

## 🎨 Màu chủ đề có thể tùy chỉnh

### Màu chính (Primary Color)
Người dùng có thể chọn 1 trong 10 màu:
1. **Xanh dương** - Tin cậy, chuyên nghiệp
2. **Tím** - Sáng tạo, độc đáo
3. **Xanh lá** - Tự nhiên, tươi mới
4. **Cam đậm** - Năng động, nhiệt huyết
5. **Đỏ** - Mạnh mẽ, quyết đoán
6. **Xanh ngọc** - Cân bằng, hài hòa
7. **Chàm** - Sang trọng, ổn định
8. **Nâu** - Ấm áp, gần gũi
9. **Xanh xám** - Hiện đại, lịch lãm
10. **Hồng** - Dịu dàng, thân thiện

### Màu phụ (Secondary Color)
Màu nhấn, thường sáng hơn màu chính:
- Dùng cho nút quan trọng
- Highlight text
- Progress bar
- Loading indicator

### Cách áp dụng màu
```dart
// Lấy màu từ theme
final primaryColor = Theme.of(context).primaryColor;
final secondaryColor = Theme.of(context).accentColor;

// Sử dụng trong widget
Container(
  color: primaryColor,
  child: Text(
    'Nội dung',
    style: TextStyle(color: Colors.white),
  ),
)
```

## 🔧 Cách hoạt động trong code

### ThemeProvider
```dart
class ThemeProvider extends ChangeNotifier {
  bool _darkMode = false;
  int _primaryColorIndex = 0;
  
  // Danh sách màu có sẵn
  final List<Color> availablePrimaryColors = [
    Colors.blue,
    Colors.purple,
    Colors.green,
    // ... thêm màu khác
  ];
  
  // Getter để lấy màu hiện tại
  Color get primaryColor => availablePrimaryColors[_primaryColorIndex];
  
  // Thay đổi màu chính
  void setPrimaryColor(int index) {
    _primaryColorIndex = index;
    notifyListeners();
  }
}
```

### Sử dụng trong Widget
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          color: themeProvider.primaryColor,
          child: Text('Hello'),
        );
      },
    );
  }
}
```

## 🎯 Nguyên tắc sử dụng màu

### 1. Nhất quán
- Cùng một chức năng dùng cùng màu
- Ví dụ: Nút "Lưu" luôn màu xanh lá

### 2. Có ý nghĩa
- Đỏ: Lỗi, xóa, nguy hiểm
- Xanh lá: Thành công, lưu, OK
- Vàng: Cảnh báo, chờ xử lý
- Xanh dương: Thông tin, link

### 3. Dễ nhìn
- Đủ độ tương phản giữa chữ và nền
- Không dùng quá nhiều màu trong 1 màn hình
- Màu phải rõ ràng trên cả chế độ sáng và tối

### 4. Accessibility (Khả năng tiếp cận)
- Người mù màu vẫn phân biệt được
- Dùng thêm icon, không chỉ dựa vào màu
- Tuân thủ chuẩn WCAG về độ tương phản

## 🔍 Ví dụ thực tế

### Thẻ bữa ăn
```dart
Widget buildMealCard(MealType mealType) {
  Color mealColor;
  switch (mealType) {
    case MealType.breakfast:
      mealColor = Colors.orange;
      break;
    case MealType.lunch:
      mealColor = Colors.teal;
      break;
    case MealType.dinner:
      mealColor = Colors.indigo;
      break;
  }
  
  return Card(
    child: Column(
      children: [
        // Header với màu bữa ăn
        Container(
          color: mealColor,
          child: Text(
            mealType.displayName,
            style: TextStyle(color: Colors.white),
          ),
        ),
        // Nội dung bên dưới
        // ...
      ],
    ),
  );
}
```

### Nút trạng thái
```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: isEaten ? Colors.green : Colors.grey,
  ),
  onPressed: toggleEaten,
  child: Text(isEaten ? 'Đã ăn' : 'Chưa ăn'),
)
```

## 💡 Mẹo cho developer

### 1. Dùng Theme thay vì hard-code màu
```dart
// ❌ Không nên
Container(color: Colors.blue)

// ✅ Nên
Container(color: Theme.of(context).primaryColor)
```

### 2. Test trên cả 2 chế độ
Luôn kiểm tra giao diện trên cả chế độ sáng và tối.

### 3. Dùng màu có sẵn
Flutter có sẵn nhiều màu đẹp, không cần tự định nghĩa.

### 4. Lưu setting của user
```dart
// Lưu vào SharedPreferences
await prefs.setInt('primaryColorIndex', colorIndex);
await prefs.setBool('darkMode', isDarkMode);
```

## 🔗 File liên quan
- `lib/providers/theme_provider.dart`: Code quản lý theme
- `lib/constants/app_text_styles.dart`: Style cho text
- `lib/screens/settings_screen.dart`: Màn hình đổi màu

---

*Màu sắc tạo nên cá tính của ứng dụng. Hiểu rõ hệ thống màu giúp tạo ra giao diện đẹp và nhất quán.*
