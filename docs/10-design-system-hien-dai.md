# 🎨 Design System Hiện Đại 2024 - CookSpark

## 📝 Tổng quan

Design System mới của CookSpark được thiết kế theo xu hướng hiện đại 2024, kết hợp Material Design 3, Glassmorphism và các nguyên tắc accessibility. <PERSON>ệ thống này đảm bảo tính nhất quán, kh<PERSON> năng mở rộng và trải nghiệm người dùng tối ưu.

## 🎯 Nguyên tắc thiết kế

### 1. Hiện đại và Sống động
- **Gradient và Glassmorphism**: Sử dụng hiệu ứng kính và gradient tinh tế
- **Micro-interactions**: Animations mượt mà và phản hồi tức thì
- **Depth và Elevation**: Tạo chiều sâu thông qua shadow và elevation

### 2. Thân thiện và Dễ tiếp cận
- **Typography rõ ràng**: <PERSON><PERSON> thống font chữ có hierarchy rõ ràng
- **Contrast cao**: <PERSON><PERSON><PERSON> b<PERSON><PERSON> khả năng đọc trong mọi điều kiện
- **Touch targets**: K<PERSON>ch thước tối thiểu 44px cho các element tương tác

### 3. Responsive và Linh hoạt
- **Mobile-first**: Thiết kế ưu tiên mobile trước
- **Adaptive spacing**: Spacing tự động điều chỉnh theo screen size
- **Flexible components**: Components hoạt động tốt trên mọi kích thước

## 🌈 Hệ thống Màu sắc

### Brand Colors
```dart
// Màu thương hiệu chính
ModernColors.primaryBrand    // #6366F1 - Indigo hiện đại
ModernColors.secondaryBrand  // #8B5CF6 - Purple sống động
ModernColors.accentBrand     // #06B6D4 - Cyan tươi mới
```

### Semantic Colors
```dart
// Màu ngữ nghĩa
ModernColors.success         // #10B981 - Emerald
ModernColors.warning         // #F59E0B - Amber
ModernColors.error           // #EF4444 - Red
ModernColors.info            // #3B82F6 - Blue
```

### Meal Colors
```dart
// Màu bữa ăn hiện đại
ModernColors.breakfastPrimary // #FF6B35 - Orange vibrant
ModernColors.lunchPrimary     // #00BCD4 - Cyan
ModernColors.dinnerPrimary    // #5C6BC0 - Indigo
```

### Gradient Colors
```dart
// Gradient hiện đại
ModernColors.primaryGradient  // [#6366F1, #8B5CF6]
ModernColors.successGradient  // [#10B981, #34D399]
ModernColors.warningGradient  // [#F59E0B, #FBBF24]
```

## 📝 Typography System

### Display Styles - Cho tiêu đề lớn
```dart
AppTextStyles.displayLarge(context)   // 57px - Hero titles
AppTextStyles.displayMedium(context)  // 45px - Section headers
AppTextStyles.displaySmall(context)   // 36px - Page titles
```

### Headline Styles - Cho tiêu đề chính
```dart
AppTextStyles.headlineLarge(context)  // 32px - Main headings
AppTextStyles.headlineMedium(context) // 28px - Sub headings
AppTextStyles.headlineSmall(context)  // 24px - Card titles
```

### Title Styles - Cho tiêu đề phụ
```dart
AppTextStyles.titleLarge(context)     // 22px - List headers
AppTextStyles.titleMedium(context)    // 16px - Card subtitles
AppTextStyles.titleSmall(context)     // 14px - Labels
```

### Body Styles - Cho nội dung
```dart
AppTextStyles.bodyLarge(context)      // 16px - Main content
AppTextStyles.bodyMedium(context)     // 14px - Secondary content
AppTextStyles.bodySmall(context)      // 12px - Captions
```

### Label Styles - Cho nhãn và buttons
```dart
AppTextStyles.labelLarge(context)     // 14px - Button text
AppTextStyles.labelMedium(context)    // 12px - Form labels
AppTextStyles.labelSmall(context)     // 11px - Tiny labels
```

## 📏 Spacing System

### Base Units
```dart
AppSpacing.xs      // 4px  - Element tiny
AppSpacing.sm      // 8px  - Element small
AppSpacing.md      // 12px - Element medium
AppSpacing.lg      // 16px - Element large
AppSpacing.xl      // 20px - Component small
AppSpacing.xxl     // 24px - Component medium
AppSpacing.xxxl    // 32px - Section medium
AppSpacing.huge    // 40px - Section large
AppSpacing.massive // 48px - Page large
```

### Responsive Spacing
```dart
// Tự động điều chỉnh theo screen size
AppSpacing.responsive(context)
AppSpacing.horizontalPadding(context)
AppSpacing.verticalSpacing(context)
```

### Spacing Shortcuts
```dart
AppSpacing.spaceLG    // Vertical space 16px
AppSpacing.hSpaceMD   // Horizontal space 12px
16.vSpace             // Extension: vertical space 16px
12.hSpace             // Extension: horizontal space 12px
```

## 🎛️ Modern Components

### ModernButton
```dart
// Primary button
ModernButton.primary(
  text: 'Lưu',
  onPressed: () {},
  icon: Icons.save,
  size: ModernButtonSize.medium,
)

// Secondary button
ModernButton.secondary(
  text: 'Hủy',
  onPressed: () {},
)

// Outline button
ModernButton.outline(
  text: 'Xem thêm',
  onPressed: () {},
)

// Ghost button
ModernButton.ghost(
  text: 'Bỏ qua',
  onPressed: () {},
)

// Glassmorphism button
ModernButton.glass(
  text: 'Đặc biệt',
  onPressed: () {},
  icon: Icons.auto_awesome,
)
```

### ModernCard
```dart
// Elevated card
ModernCard.elevated(
  child: Text('Nội dung'),
)

// Outlined card
ModernCard.outlined(
  child: Text('Nội dung'),
)

// Filled card
ModernCard.filled(
  child: Text('Nội dung'),
)

// Glassmorphism card
ModernCard.glass(
  child: Text('Nội dung'),
)

// Card with header
ModernCardWithHeader(
  title: 'Tiêu đề',
  subtitle: 'Mô tả',
  leading: Icon(Icons.star),
  child: Text('Nội dung chính'),
  actions: [
    ModernButton.ghost(text: 'Hủy', onPressed: () {}),
    ModernButton.primary(text: 'Lưu', onPressed: () {}),
  ],
)
```

## ✨ Animated Widgets

### BounceWidget - Hiệu ứng bounce khi tap
```dart
BounceWidget(
  onTap: () {},
  child: Container(
    child: Text('Tap me!'),
  ),
)
```

### FadeInWidget - Hiệu ứng fade in
```dart
FadeInWidget(
  duration: Duration(milliseconds: 600),
  delay: Duration(milliseconds: 200),
  slideOffset: Offset(0, 0.3),
  child: Text('Fade in text'),
)
```

### ShimmerWidget - Loading shimmer
```dart
ShimmerWidget(
  child: Container(
    height: 100,
    color: Colors.grey[300],
  ),
)
```

### PulseWidget - Hiệu ứng pulse
```dart
PulseWidget(
  child: Icon(Icons.favorite, color: Colors.red),
)
```

## 🚀 Navigation với Modern Transitions

### ModernPageRoute
```dart
// Slide từ phải (default)
context.pushModern(NewScreen());

// Slide từ dưới
context.pushModern(
  NewScreen(),
  transition: ModernTransitionType.slideFromBottom,
);

// Fade transition
context.pushModern(
  NewScreen(),
  transition: ModernTransitionType.fade,
);

// Scale transition
context.pushModern(
  NewScreen(),
  transition: ModernTransitionType.scale,
);

// Slide và fade kết hợp
context.pushModern(
  NewScreen(),
  transition: ModernTransitionType.slideAndFade,
);
```

## 📱 Responsive Design

### Breakpoints
```dart
// Mobile: < 600px
// Tablet: 600px - 1200px  
// Desktop: > 1200px

// Sử dụng responsive spacing
EdgeInsets.all(AppSpacing.horizontalPadding(context))

// Responsive padding extension
EdgeInsets.all(16).responsive(context)
```

## 🎯 Best Practices

### 1. Sử dụng Design System
```dart
// ✅ Đúng - Sử dụng design system
ModernButton.primary(text: 'Lưu', onPressed: () {})

// ❌ Sai - Tự tạo button
ElevatedButton(
  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
  child: Text('Lưu'),
  onPressed: () {},
)
```

### 2. Spacing nhất quán
```dart
// ✅ Đúng - Sử dụng spacing system
Column(
  children: [
    Text('Title'),
    AppSpacing.spaceMD,
    Text('Content'),
  ],
)

// ❌ Sai - Hard-code spacing
Column(
  children: [
    Text('Title'),
    SizedBox(height: 12),
    Text('Content'),
  ],
)
```

### 3. Colors từ theme
```dart
// ✅ Đúng - Sử dụng theme colors
Container(
  color: Theme.of(context).colorScheme.primary,
)

// ❌ Sai - Hard-code colors
Container(
  color: Colors.blue,
)
```

## 🔧 Migration Guide

### Từ Design Cũ sang Design Mới

1. **Typography**:
   ```dart
   // Cũ
   AppTextStyles.title1(context)
   
   // Mới
   AppTextStyles.headlineLarge(context)
   ```

2. **Spacing**:
   ```dart
   // Cũ
   SizedBox(height: 16)
   
   // Mới
   AppSpacing.spaceLG
   ```

3. **Buttons**:
   ```dart
   // Cũ
   ElevatedButton(...)
   
   // Mới
   ModernButton.primary(...)
   ```

4. **Cards**:
   ```dart
   // Cũ
   Card(...)
   
   // Mới
   ModernCard.elevated(...)
   ```

## 📚 Tài liệu tham khảo

- **Material Design 3**: [m3.material.io](https://m3.material.io)
- **Flutter Design**: [flutter.dev/design](https://flutter.dev/design)
- **Accessibility**: [flutter.dev/accessibility](https://flutter.dev/accessibility)

## 🎨 Demo và Showcase

Để xem demo các component mới, chạy ứng dụng và navigate đến:
```dart
Navigator.push(context, MaterialPageRoute(
  builder: (context) => DesignShowcaseScreen(),
));
```

Hoặc thêm vào drawer menu:
```dart
ListTile(
  leading: Icon(Icons.palette),
  title: Text('Design Showcase'),
  onTap: () {
    Navigator.pushNamed(context, DesignShowcaseScreen.routeName);
  },
),
```

## 🚀 Quick Start

1. **Import các file cần thiết**:
   ```dart
   import '../constants/modern_colors.dart';
   import '../constants/app_spacing.dart';
   import '../constants/app_text_styles.dart';
   import '../widgets/modern_button.dart';
   import '../widgets/modern_card.dart';
   import '../widgets/animated_widgets.dart';
   ```

2. **Sử dụng components**:
   ```dart
   ModernCard.elevated(
     padding: AppSpacing.cardPaddingMedium,
     child: Column(
       children: [
         Text(
           'Tiêu đề',
           style: AppTextStyles.titleLarge(context),
         ),
         AppSpacing.spaceMD,
         ModernButton.primary(
           text: 'Hành động',
           onPressed: () {},
         ),
       ],
     ),
   )
   ```

3. **Áp dụng animations**:
   ```dart
   FadeInWidget(
     delay: Duration(milliseconds: 200),
     child: BounceWidget(
       onTap: () {},
       child: YourWidget(),
     ),
   )
   ```

---

*Design System này được cập nhật liên tục để đảm bảo tính hiện đại và khả năng sử dụng tốt nhất.*
