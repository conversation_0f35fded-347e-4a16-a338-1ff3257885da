# 🔧 Tổng quan Kiến trúc Backend

## 🎯 Backend là gì?
Backend là phần "não bộ" của <PERSON> dụng, xử lý:
- **Lưu trữ dữ liệu**: <PERSON><PERSON>, thự<PERSON> đơn, lịch sử
- **<PERSON><PERSON><PERSON> thực người dùng**: <PERSON><PERSON><PERSON> nhậ<PERSON>, đăng ký
- **Đồng bộ dữ liệu**: Giữa các thiết bị
- **Xử lý logic**: T<PERSON>h toán calories, gợi ý món

## 🏗️ Kiến trúc tổng thể

### Mô hình 3 tầng
```
┌─────────────────┐
│   Frontend      │ ← Giao diện người dùng (Flutter)
│   (UI Layer)    │
├─────────────────┤
│   Services      │ ← Xử lý logic nghiệp vụ
│   (Logic Layer) │
├─────────────────┤
│   Database      │ ← Lưu trữ dữ liệu (Supabase)
│   (Data Layer)  │
└─────────────────┘
```

### Luồng dữ liệu
```
User nhập liệu → UI → Service → Database → Service → UI → Hiển thị
```

## 🗄️ Cơ sở dữ liệu (Supabase)

### Tại sao chọn Supabase?
- **Miễn phí**: Có gói free tốt
- **Realtime**: Cập nhật dữ liệu ngay lập tức
- **Authentication**: Hỗ trợ đăng nhập Google
- **Storage**: Lưu trữ hình ảnh
- **API tự động**: Không cần viết API

### Các bảng chính

#### 1. `dishes` - Món ăn mặc định
```sql
CREATE TABLE dishes (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,           -- Tên món ăn
  calories INTEGER,             -- Số calories
  cooking_time INTEGER,         -- Thời gian nấu (phút)
  image_url TEXT,              -- Link ảnh
  ingredients JSONB,           -- Danh sách nguyên liệu
  instructions TEXT,           -- Hướng dẫn nấu
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. `user_dishes` - Món ăn do người dùng tạo
```sql
CREATE TABLE user_dishes (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  calories INTEGER,
  cooking_time INTEGER,
  image_url TEXT,
  ingredients JSONB,
  instructions TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. `meal_plans` - Kế hoạch bữa ăn
```sql
CREATE TABLE meal_plans (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,      -- 'breakfast', 'lunch', 'dinner'
  dish_id UUID,                 -- Có thể null nếu chưa chọn món
  dish_type TEXT,               -- 'default' hoặc 'user_created'
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. `meal_history` - Lịch sử bữa ăn
```sql
CREATE TABLE meal_history (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,
  dish_id UUID NOT NULL,
  dish_type TEXT NOT NULL,
  calories_consumed INTEGER,
  eaten_at TIMESTAMP DEFAULT NOW()
);
```

## 🔐 Bảo mật (Row Level Security)

### RLS là gì?
Row Level Security đảm bảo mỗi user chỉ thấy dữ liệu của mình.

### Ví dụ policy
```sql
-- User chỉ thấy món ăn của mình
CREATE POLICY "Users can view own dishes" ON user_dishes
  FOR SELECT USING (auth.uid() = user_id);

-- User chỉ sửa món ăn của mình  
CREATE POLICY "Users can update own dishes" ON user_dishes
  FOR UPDATE USING (auth.uid() = user_id);
```

## 🔧 Services Layer

### Cấu trúc Services
```
lib/services/
├── auth_service.dart           ← Xác thực
├── dish_service.dart          ← Quản lý món ăn
├── meal_plan_service.dart     ← Kế hoạch bữa ăn
├── meal_history_service.dart  ← Lịch sử ăn uống
├── storage_service.dart       ← Lưu trữ ảnh
├── sync_service.dart          ← Đồng bộ dữ liệu
└── user_profile_service.dart  ← Thông tin user
```

### Nguyên tắc thiết kế Service

#### 1. Mỗi Service có 1 nhiệm vụ
```dart
// ✅ Tốt - DishService chỉ lo về món ăn
class DishService {
  Future<List<Dish>> getAllDishes();
  Future<Dish> createDish(Dish dish);
  Future<void> updateDish(Dish dish);
  Future<void> deleteDish(String dishId);
}

// ❌ Không tốt - Service làm quá nhiều việc
class MegaService {
  // Quản lý món ăn, user, meal plan... tất cả
}
```

#### 2. Xử lý lỗi nhất quán
```dart
class DishService {
  Future<ServiceResult<List<Dish>>> getAllDishes() async {
    try {
      final response = await supabase.from('dishes').select();
      final dishes = response.map((json) => Dish.fromJson(json)).toList();
      return ServiceResult.success(dishes);
    } catch (e) {
      return ServiceResult.error('Không thể tải danh sách món ăn: $e');
    }
  }
}
```

#### 3. Async/Await cho tất cả
```dart
// ✅ Dùng async/await
Future<void> saveDish(Dish dish) async {
  await supabase.from('user_dishes').insert(dish.toJson());
}

// ❌ Không dùng callback phức tạp
void saveDish(Dish dish, Function(bool) callback) {
  // Code phức tạp...
}
```

## 📊 State Management

### Provider Pattern
```dart
// Provider quản lý state
class MenuProvider extends ChangeNotifier {
  List<Dish> _dishes = [];
  bool _isLoading = false;
  
  List<Dish> get dishes => _dishes;
  bool get isLoading => _isLoading;
  
  Future<void> loadDishes() async {
    _isLoading = true;
    notifyListeners();
    
    final result = await DishService().getAllDishes();
    if (result.isSuccess) {
      _dishes = result.data!;
    }
    
    _isLoading = false;
    notifyListeners();
  }
}
```

### Sử dụng Provider
```dart
// Trong main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => MenuProvider()),
    ChangeNotifierProvider(create: (_) => ThemeProvider()),
  ],
  child: MyApp(),
)

// Trong Widget
Consumer<MenuProvider>(
  builder: (context, menuProvider, child) {
    if (menuProvider.isLoading) {
      return CircularProgressIndicator();
    }
    return ListView.builder(
      itemCount: menuProvider.dishes.length,
      itemBuilder: (context, index) {
        return DishCard(dish: menuProvider.dishes[index]);
      },
    );
  },
)
```

## 🔄 Đồng bộ dữ liệu

### Chiến lược đồng bộ
1. **Online First**: Ưu tiên dữ liệu online
2. **Offline Support**: Lưu cache khi offline
3. **Conflict Resolution**: Xử lý xung đột dữ liệu

### Ví dụ SyncService
```dart
class SyncService {
  Future<void> syncUserDishes() async {
    // 1. Lấy dữ liệu local
    final localDishes = await LocalStorage.getUserDishes();
    
    // 2. Lấy dữ liệu từ server
    final serverDishes = await SupabaseService.getUserDishes();
    
    // 3. So sánh và merge
    final mergedDishes = mergeDishes(localDishes, serverDishes);
    
    // 4. Cập nhật cả local và server
    await LocalStorage.saveUserDishes(mergedDishes);
    await SupabaseService.updateUserDishes(mergedDishes);
  }
}
```

## 🖼️ Xử lý hình ảnh

### Upload ảnh lên Supabase Storage
```dart
class StorageService {
  Future<String?> uploadDishImage(File imageFile) async {
    try {
      // 1. Tạo tên file unique
      final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // 2. Upload lên Supabase Storage
      await supabase.storage
          .from('dish-images')
          .upload(fileName, imageFile);
      
      // 3. Lấy public URL
      final publicUrl = supabase.storage
          .from('dish-images')
          .getPublicUrl(fileName);
      
      return publicUrl;
    } catch (e) {
      print('Lỗi upload ảnh: $e');
      return null;
    }
  }
}
```

## ⚡ Performance và Optimization

### 1. Lazy Loading
```dart
// Chỉ tải dữ liệu khi cần
class DishListScreen extends StatefulWidget {
  @override
  _DishListScreenState createState() => _DishListScreenState();
}

class _DishListScreenState extends State<DishListScreen> {
  @override
  void initState() {
    super.initState();
    // Tải dữ liệu sau khi widget đã build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<MenuProvider>(context, listen: false).loadDishes();
    });
  }
}
```

### 2. Caching
```dart
class CacheService {
  static final Map<String, dynamic> _cache = {};
  
  static T? get<T>(String key) {
    return _cache[key] as T?;
  }
  
  static void set<T>(String key, T value) {
    _cache[key] = value;
  }
  
  static void clear() {
    _cache.clear();
  }
}
```

### 3. Pagination
```dart
class DishService {
  Future<List<Dish>> getDishes({int page = 1, int limit = 20}) async {
    final offset = (page - 1) * limit;
    
    final response = await supabase
        .from('dishes')
        .select()
        .range(offset, offset + limit - 1);
    
    return response.map((json) => Dish.fromJson(json)).toList();
  }
}
```

## 🔍 Debugging và Logging

### Logging Service
```dart
class LogService {
  static void info(String message) {
    print('[INFO] ${DateTime.now()}: $message');
  }
  
  static void error(String message, [dynamic error]) {
    print('[ERROR] ${DateTime.now()}: $message');
    if (error != null) {
      print('Error details: $error');
    }
  }
  
  static void debug(String message) {
    if (kDebugMode) {
      print('[DEBUG] ${DateTime.now()}: $message');
    }
  }
}
```

## 💡 Best Practices

### 1. Tách biệt concerns
- UI chỉ lo hiển thị
- Service lo logic nghiệp vụ
- Model chỉ chứa dữ liệu

### 2. Error handling toàn diện
```dart
try {
  await riskyOperation();
} on NetworkException {
  showError('Lỗi mạng, vui lòng thử lại');
} on AuthException {
  navigateToLogin();
} catch (e) {
  showError('Đã xảy ra lỗi không mong muốn');
  LogService.error('Unexpected error', e);
}
```

### 3. Validation dữ liệu
```dart
class DishValidator {
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Tên món ăn không được để trống';
    }
    if (name.length > 100) {
      return 'Tên món ăn không được quá 100 ký tự';
    }
    return null;
  }
}
```

## 🔗 File liên quan
- **[12-quan-ly-du-lieu.md](12-quan-ly-du-lieu.md)**: Chi tiết về database
- **[13-xac-thuc-bao-mat.md](13-xac-thuc-bao-mat.md)**: Authentication
- **[16-api-services.md](16-api-services.md)**: Các API service

---

*Backend là nền tảng của ứng dụng. Hiểu rõ kiến trúc giúp phát triển tính năng mới một cách hiệu quả.*
