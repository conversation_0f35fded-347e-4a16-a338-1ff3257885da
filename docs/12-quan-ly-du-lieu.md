# 🗄️ Quản lý Dữ liệu và Database

## 📊 Dữ liệu trong CookSpark

### Các loại dữ liệu chính
1. **Món ăn mặc định**: Do hệ thống cung cấp
2. **Món ăn người dùng**: Do user tự tạo
3. **Kế hoạch bữa ăn**: Thực đơn tuần
4. **Lịch sử ăn uống**: Đã ăn gì, khi nào
5. **Thông tin người dùng**: Profile, settings
6. **Hình ảnh**: Ảnh món ăn

## 🏗️ Cấu trúc Database

### Sơ đồ quan hệ
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    users    │    │   dishes    │    │user_dishes  │
│             │    │             │    │             │
│ - id        │    │ - id        │    │ - id        │
│ - email     │    │ - name      │    │ - user_id   │
│ - name      │    │ - calories  │    │ - name      │
│ - avatar    │    │ - image_url │    │ - calories  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                                      │
       │           ┌─────────────┐           │
       └───────────│ meal_plans  │───────────┘
                   │             │
                   │ - user_id   │
                   │ - date      │
                   │ - meal_type │
                   │ - dish_id   │
                   └─────────────┘
```

## 📋 Chi tiết các bảng

### 1. Bảng `dishes` - Món ăn mặc định
```sql
CREATE TABLE dishes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  calories INTEGER DEFAULT 0,
  cooking_time INTEGER DEFAULT 0, -- phút
  difficulty_level INTEGER DEFAULT 1, -- 1-5
  image_url TEXT,
  ingredients JSONB DEFAULT '[]',
  instructions TEXT,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Ý nghĩa các trường:**
- `id`: Mã định danh duy nhất
- `name`: Tên món ăn (VD: "Phở bò")
- `calories`: Số calories trên 1 phần ăn
- `cooking_time`: Thời gian nấu tính bằng phút
- `ingredients`: Danh sách nguyên liệu dạng JSON
- `tags`: Nhãn phân loại (VD: ["Việt Nam", "Nóng"])

**Ví dụ dữ liệu:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Phở bò",
  "calories": 350,
  "cooking_time": 120,
  "ingredients": [
    {"name": "Thịt bò", "amount": 200, "unit": "gram"},
    {"name": "Bánh phở", "amount": 100, "unit": "gram"},
    {"name": "Hành lá", "amount": 2, "unit": "cây"}
  ]
}
```

### 2. Bảng `user_dishes` - Món ăn người dùng
```sql
CREATE TABLE user_dishes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  calories INTEGER DEFAULT 0,
  cooking_time INTEGER DEFAULT 0,
  image_url TEXT,
  ingredients JSONB DEFAULT '[]',
  instructions TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  is_public BOOLEAN DEFAULT FALSE, -- Có chia sẻ không
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Khác biệt với `dishes`:**
- Có `user_id`: Thuộc về user nào
- Có `is_favorite`: Đánh dấu yêu thích
- Có `is_public`: Có thể chia sẻ cho user khác

### 3. Bảng `meal_plans` - Kế hoạch bữa ăn
```sql
CREATE TABLE meal_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  meal_type meal_type_enum NOT NULL,
  dish_id UUID,
  dish_type dish_type_enum NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enum types
CREATE TYPE meal_type_enum AS ENUM ('breakfast', 'lunch', 'dinner');
CREATE TYPE dish_type_enum AS ENUM ('default', 'user_created');
```

**Ý nghĩa:**
- `date`: Ngày ăn (VD: 2025-06-25)
- `meal_type`: Loại bữa ăn (sáng/trưa/tối)
- `dish_id`: ID của món ăn
- `dish_type`: Món mặc định hay do user tạo

### 4. Bảng `meal_history` - Lịch sử ăn uống
```sql
CREATE TABLE meal_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  meal_type meal_type_enum NOT NULL,
  dish_id UUID NOT NULL,
  dish_type dish_type_enum NOT NULL,
  dish_name TEXT NOT NULL, -- Lưu tên để tránh mất dữ liệu
  calories_consumed INTEGER DEFAULT 0,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  notes TEXT,
  eaten_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Tại sao cần bảng riêng?**
- Lưu lịch sử khi user đã ăn
- Tính thống kê calories theo thời gian
- Đánh giá món ăn
- Không bị mất dữ liệu khi xóa meal plan

## 🔐 Row Level Security (RLS)

### Tại sao cần RLS?
- **Bảo mật**: User chỉ thấy dữ liệu của mình
- **Tự động**: Không cần check trong code
- **Hiệu quả**: Database tự filter

### Ví dụ RLS Policies

#### User chỉ thấy món ăn của mình
```sql
-- Policy cho user_dishes
CREATE POLICY "Users can view own dishes" ON user_dishes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own dishes" ON user_dishes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own dishes" ON user_dishes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own dishes" ON user_dishes
  FOR DELETE USING (auth.uid() = user_id);
```

#### Món ăn mặc định mọi người đều thấy
```sql
-- Policy cho dishes (read-only cho user)
CREATE POLICY "Anyone can view default dishes" ON dishes
  FOR SELECT USING (true);
```

#### Meal plans riêng tư
```sql
CREATE POLICY "Users can manage own meal plans" ON meal_plans
  FOR ALL USING (auth.uid() = user_id);
```

## 📱 Quản lý dữ liệu trong Flutter

### Model Classes

#### Dish Model
```dart
class Dish {
  final String id;
  final String name;
  final int calories;
  final int cookingTime;
  final String imageUrl;
  final List<Ingredient> ingredients;
  final String instructions;
  final bool isNetworkImage;
  final bool isFavorite;

  Dish({
    required this.id,
    required this.name,
    this.calories = 0,
    this.cookingTime = 0,
    this.imageUrl = '',
    this.ingredients = const [],
    this.instructions = '',
    this.isNetworkImage = false,
    this.isFavorite = false,
  });

  // Chuyển từ JSON sang Object
  factory Dish.fromJson(Map<String, dynamic> json) {
    return Dish(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      calories: json['calories'] ?? 0,
      cookingTime: json['cooking_time'] ?? 0,
      imageUrl: json['image_url'] ?? '',
      ingredients: (json['ingredients'] as List?)
          ?.map((i) => Ingredient.fromJson(i))
          .toList() ?? [],
      instructions: json['instructions'] ?? '',
      isNetworkImage: json['image_url']?.startsWith('http') ?? false,
      isFavorite: json['is_favorite'] ?? false,
    );
  }

  // Chuyển từ Object sang JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'calories': calories,
      'cooking_time': cookingTime,
      'image_url': imageUrl,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'instructions': instructions,
      'is_favorite': isFavorite,
    };
  }
}
```

#### Ingredient Model
```dart
class Ingredient {
  final String name;
  final double amount;
  final String unit;

  Ingredient({
    required this.name,
    required this.amount,
    required this.unit,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    return Ingredient(
      name: json['name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      unit: json['unit'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'amount': amount,
      'unit': unit,
    };
  }
}
```

### Repository Pattern

#### DishRepository
```dart
class DishRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Lấy tất cả món ăn mặc định
  Future<List<Dish>> getDefaultDishes() async {
    try {
      final response = await _supabase
          .from('dishes')
          .select()
          .order('name');

      return response
          .map((json) => Dish.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Không thể tải món ăn mặc định: $e');
    }
  }

  // Lấy món ăn của user
  Future<List<Dish>> getUserDishes() async {
    try {
      final response = await _supabase
          .from('user_dishes')
          .select()
          .order('created_at', ascending: false);

      return response
          .map((json) => Dish.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Không thể tải món ăn của bạn: $e');
    }
  }

  // Tạo món ăn mới
  Future<Dish> createUserDish(Dish dish) async {
    try {
      final response = await _supabase
          .from('user_dishes')
          .insert(dish.toJson())
          .select()
          .single();

      return Dish.fromJson(response);
    } catch (e) {
      throw Exception('Không thể tạo món ăn: $e');
    }
  }

  // Cập nhật món ăn
  Future<void> updateUserDish(Dish dish) async {
    try {
      await _supabase
          .from('user_dishes')
          .update(dish.toJson())
          .eq('id', dish.id);
    } catch (e) {
      throw Exception('Không thể cập nhật món ăn: $e');
    }
  }

  // Xóa món ăn
  Future<void> deleteUserDish(String dishId) async {
    try {
      await _supabase
          .from('user_dishes')
          .delete()
          .eq('id', dishId);
    } catch (e) {
      throw Exception('Không thể xóa món ăn: $e');
    }
  }
}
```

## 🔄 Caching và Offline Support

### Local Storage với Hive
```dart
class LocalDishStorage {
  static const String _boxName = 'dishes';
  late Box<Map> _box;

  Future<void> init() async {
    _box = await Hive.openBox<Map>(_boxName);
  }

  // Lưu món ăn vào cache
  Future<void> cacheDishes(List<Dish> dishes) async {
    final dishesJson = dishes.map((d) => d.toJson()).toList();
    await _box.put('cached_dishes', dishesJson);
    await _box.put('cache_time', DateTime.now().millisecondsSinceEpoch);
  }

  // Lấy món ăn từ cache
  List<Dish>? getCachedDishes() {
    final dishesJson = _box.get('cached_dishes') as List?;
    if (dishesJson == null) return null;

    return dishesJson
        .map((json) => Dish.fromJson(Map<String, dynamic>.from(json)))
        .toList();
  }

  // Kiểm tra cache có hết hạn không
  bool isCacheExpired() {
    final cacheTime = _box.get('cache_time') as int?;
    if (cacheTime == null) return true;

    final now = DateTime.now().millisecondsSinceEpoch;
    final diff = now - cacheTime;
    return diff > (24 * 60 * 60 * 1000); // 24 giờ
  }
}
```

### Service với Cache
```dart
class DishService {
  final DishRepository _repository = DishRepository();
  final LocalDishStorage _localStorage = LocalDishStorage();

  Future<List<Dish>> getDishes() async {
    try {
      // Thử lấy từ server trước
      final dishes = await _repository.getDefaultDishes();
      
      // Cache lại
      await _localStorage.cacheDishes(dishes);
      
      return dishes;
    } catch (e) {
      // Nếu lỗi, dùng cache
      final cachedDishes = _localStorage.getCachedDishes();
      if (cachedDishes != null && !_localStorage.isCacheExpired()) {
        return cachedDishes;
      }
      
      throw Exception('Không có kết nối mạng và cache đã hết hạn');
    }
  }
}
```

## 📊 Tối ưu hiệu suất

### 1. Pagination
```dart
class DishService {
  Future<List<Dish>> getDishesWithPagination({
    int page = 1,
    int limit = 20,
  }) async {
    final offset = (page - 1) * limit;
    
    final response = await _supabase
        .from('dishes')
        .select()
        .range(offset, offset + limit - 1)
        .order('name');
    
    return response.map((json) => Dish.fromJson(json)).toList();
  }
}
```

### 2. Lazy Loading
```dart
class DishListProvider extends ChangeNotifier {
  List<Dish> _dishes = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;

  Future<void> loadMoreDishes() async {
    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    notifyListeners();

    try {
      final newDishes = await DishService().getDishesWithPagination(
        page: _currentPage,
        limit: 20,
      );

      if (newDishes.length < 20) {
        _hasMore = false;
      }

      _dishes.addAll(newDishes);
      _currentPage++;
    } catch (e) {
      // Handle error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
```

### 3. Search Optimization
```dart
class DishService {
  Future<List<Dish>> searchDishes(String query) async {
    if (query.trim().isEmpty) {
      return getDishes();
    }

    final response = await _supabase
        .from('dishes')
        .select()
        .or('name.ilike.%$query%,description.ilike.%$query%')
        .limit(50);

    return response.map((json) => Dish.fromJson(json)).toList();
  }
}
```

## 💡 Best Practices

### 1. Validation dữ liệu
```dart
class DishValidator {
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Tên món ăn không được để trống';
    }
    if (name.length > 100) {
      return 'Tên món ăn không được quá 100 ký tự';
    }
    return null;
  }

  static String? validateCalories(String? calories) {
    if (calories == null || calories.trim().isEmpty) {
      return null; // Calories có thể để trống
    }
    
    final cal = int.tryParse(calories);
    if (cal == null) {
      return 'Calories phải là số';
    }
    if (cal < 0 || cal > 10000) {
      return 'Calories phải từ 0 đến 10000';
    }
    return null;
  }
}
```

### 2. Error Handling
```dart
class DatabaseException implements Exception {
  final String message;
  final String? code;
  
  DatabaseException(this.message, [this.code]);
  
  @override
  String toString() => 'DatabaseException: $message';
}

class DishService {
  Future<List<Dish>> getDishes() async {
    try {
      // Database operation
    } on PostgrestException catch (e) {
      throw DatabaseException('Lỗi database: ${e.message}', e.code);
    } catch (e) {
      throw DatabaseException('Lỗi không xác định: $e');
    }
  }
}
```

### 3. Transaction Safety
```dart
class MealPlanService {
  Future<void> updateMealPlan(String date, List<MealPlan> meals) async {
    // Sử dụng transaction để đảm bảo tính nhất quán
    await _supabase.rpc('update_meal_plan_transaction', params: {
      'p_date': date,
      'p_meals': meals.map((m) => m.toJson()).toList(),
    });
  }
}
```

## 🔗 File liên quan
- **[13-xac-thuc-bao-mat.md](13-xac-thuc-bao-mat.md)**: Authentication
- **[14-dong-bo-du-lieu.md](14-dong-bo-du-lieu.md)**: Sync strategies
- **[16-api-services.md](16-api-services.md)**: API services

---

*Quản lý dữ liệu tốt là nền tảng của ứng dụng ổn định. Hiểu rõ database schema và caching giúp xây dựng app hiệu quả.*
