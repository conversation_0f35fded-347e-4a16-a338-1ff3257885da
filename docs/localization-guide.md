# Hướng dẫn Localization - CookSpark

## Tổng quan

CookSpark hỗ trợ đa ngôn ngữ với hệ thống localization tùy chỉnh, hiện tại hỗ trợ:
- **Tiếng Việt (vi)** - Ngôn ngữ mặc định
- **Ti<PERSON><PERSON> (en)** - <PERSON><PERSON><PERSON> ngữ phụ

## Cấu trúc hệ thống

### 1. AppLocalizations (`lib/l10n/app_localizations.dart`)
- Class chính quản lý tất cả các chuỗi văn bản
- Sử dụng pattern singleton với `_getValue()` method
- Tự động fallback về key nếu không tìm thấy translation

### 2. LanguageProvider (`lib/providers/language_provider.dart`)
- Quản lý state ngôn ngữ hiện tại
- Lưu trữ preference vào SharedPreferences
- Notify listeners khi ngôn ngữ thay đổi

### 3. DateFormatter (`lib/utils/date_formatter.dart`)
- Format ngày tháng theo locale
- Hỗ trợ relative time (v<PERSON><PERSON> xong, 5 phút trước, etc.)
- Tự động chuyển đổi format theo ngôn ngữ

## Cách sử dụng

### 1. Trong Widget
```dart
@override
Widget build(BuildContext context) {
  final l10n = AppLocalizations.of(context);
  
  return Text(l10n?.home ?? 'Trang chủ');
}
```

### 2. Thêm key mới
1. Thêm getter trong `AppLocalizations`:
```dart
String get newKey => _getValue('new_key');
```

2. Thêm case trong `_getValue()`:
```dart
case 'new_key': return isVietnamese ? 'Văn bản tiếng Việt' : 'English text';
```

### 3. Format ngày tháng
```dart
import '../utils/date_formatter.dart';

// Relative time
String timeAgo = DateFormatter.formatRelativeTime(context, dateTime);

// Short date
String shortDate = DateFormatter.formatShortDate(context, date);

// Full date
String fullDate = DateFormatter.formatDate(context, date);
```

## Danh sách các key hiện có

### Cơ bản
- `app_name`: Tên ứng dụng
- `home`: Trang chủ
- `settings`: Cài đặt
- `cancel`: Hủy
- `delete`: Xóa
- `save`: Lưu

### Navigation
- `meal_suggestion`: Gợi ý món ăn
- `weekly_menu`: Thực đơn tuần
- `saved_dishes`: Món ăn đã lưu
- `personal_information`: Thông tin cá nhân

### Authentication
- `login_title`: Đăng Nhập
- `welcome_back`: Chào mừng bạn quay trở lại
- `email_address`: Địa chỉ email
- `password_field`: Mật khẩu
- `forgot_password_link`: Quên mật khẩu?
- `remember_login`: Ghi nhớ đăng nhập

### Validation
- `please_enter_email`: Vui lòng nhập email
- `invalid_email`: Email không hợp lệ
- `please_enter_password`: Vui lòng nhập mật khẩu
- `password_min_length`: Mật khẩu phải có ít nhất 6 ký tự

### Date & Time
- `today`: Hôm nay
- `yesterday`: Hôm qua
- `tomorrow`: Ngày mai
- `just_now`: Vừa xong

### Notifications
- `saved_successfully`: Đã lưu thành công!
- `deleted_successfully`: Đã xóa thành công!
- `view_list`: Xem danh sách
- `undo`: Hoàn tác

## Best Practices

### 1. Luôn sử dụng fallback
```dart
// ✅ Đúng
Text(l10n?.home ?? 'Trang chủ')

// ❌ Sai
Text(l10n!.home)
```

### 2. Nhóm key theo chức năng
```dart
// Authentication keys
String get loginTitle => _getValue('login_title');
String get welcomeBack => _getValue('welcome_back');

// Settings keys  
String get settings => _getValue('settings');
String get darkMode => _getValue('dark_mode');
```

### 3. Sử dụng tên key mô tả
```dart
// ✅ Đúng
case 'confirm_delete_video': return isVietnamese ? '...' : '...';

// ❌ Sai
case 'msg1': return isVietnamese ? '...' : '...';
```

### 4. Test localization
```dart
testWidgets('Vietnamese localization works', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      locale: const Locale('vi'),
      localizationsDelegates: const [AppLocalizations.delegate],
      home: MyWidget(),
    ),
  );
  
  expect(find.text('Trang chủ'), findsOneWidget);
});
```

## Thêm ngôn ngữ mới

### 1. Cập nhật LanguageProvider
```dart
static const List<String> supportedLanguages = ['vi', 'en', 'ja']; // Thêm 'ja'
```

### 2. Cập nhật AppLocalizations
```dart
bool get isVietnamese => _locale.languageCode == 'vi';
bool get isEnglish => _locale.languageCode == 'en';
bool get isJapanese => _locale.languageCode == 'ja'; // Thêm

String _getValue(String key) {
  switch (key) {
    case 'home': 
      if (isVietnamese) return 'Trang chủ';
      if (isJapanese) return 'ホーム'; // Thêm
      return 'Home';
  }
}
```

### 3. Cập nhật DateFormatter
```dart
static String formatDate(BuildContext context, DateTime date) {
  final locale = Localizations.localeOf(context);
  
  switch (locale.languageCode) {
    case 'vi':
      return DateFormat('dd/MM/yyyy', 'vi').format(date);
    case 'ja': // Thêm
      return DateFormat('yyyy/MM/dd', 'ja').format(date);
    default:
      return DateFormat('MM/dd/yyyy', 'en').format(date);
  }
}
```

## Troubleshooting

### 1. Text không thay đổi khi switch ngôn ngữ
- Kiểm tra widget có rebuild không
- Đảm bảo sử dụng `AppLocalizations.of(context)`
- Kiểm tra LanguageProvider có notify listeners không

### 2. Fallback text hiển thị
- Kiểm tra key có đúng không
- Kiểm tra case trong `_getValue()` có tồn tại không
- Kiểm tra logic `isVietnamese`

### 3. Date format không đúng
- Kiểm tra locale có được set đúng không
- Đảm bảo import `intl` package
- Kiểm tra DateFormat pattern

## Testing

Chạy test localization:
```bash
flutter test test/localization_test.dart
```

Test coverage bao gồm:
- Vietnamese/English localization
- Language switching
- Fallback mechanism
- Key completeness
- DateFormatter functionality
