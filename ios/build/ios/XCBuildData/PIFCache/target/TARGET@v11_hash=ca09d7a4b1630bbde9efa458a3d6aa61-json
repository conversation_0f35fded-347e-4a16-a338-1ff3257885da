{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985840b79e1694cb214913dbc777496432", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/app_links", "DEFINES_MODULE": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "app_links", "INFOPLIST_FILE": "Target Support Files/app_links/ResourceBundle-app_links_ios_privacy-app_links-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -ObjC", "PRODUCT_NAME": "app_links_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited)", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984f430005bacd4b0a1e75de21fbcf6fa9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b7e1a1751e8834279e9d3f43fd3338b", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/app_links", "DEFINES_MODULE": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "app_links", "INFOPLIST_FILE": "Target Support Files/app_links/ResourceBundle-app_links_ios_privacy-app_links-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_LDFLAGS": "$(inherited) -ObjC", "PRODUCT_NAME": "app_links_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited)", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f3c738b69b512f816d362597895f74bf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b7e1a1751e8834279e9d3f43fd3338b", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/app_links", "DEFINES_MODULE": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "app_links", "INFOPLIST_FILE": "Target Support Files/app_links/ResourceBundle-app_links_ios_privacy-app_links-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "OTHER_LDFLAGS": "$(inherited) -ObjC", "PRODUCT_NAME": "app_links_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited)", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988f96e8b1f4aeecf04f500370c1f0002a", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a3a9db64d40f12e0f49ef320c8afeca7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9891a4d5fb00a4b6abce71791b2b88f175", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98536f386226231e5f0ccd8adc28a2be5f", "guid": "bfdfe7dc352907fc980b868725387e9848ddc5f11160a00b6a74aa4792b2123e"}], "guid": "bfdfe7dc352907fc980b868725387e985c1356a75bcca147e704e85bfbccfb4c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9854f2b496f3d836ac3fd0e138cbba7daf", "name": "app_links-app_links_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b94b436e886ef04c570b7ebe5735692f", "name": "app_links_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}