{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1e8380311ab9b68ddb17fb351290ce9", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1b6e585f32d4cf2cdaa1a65173aecf4", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1b6e585f32d4cf2cdaa1a65173aecf4", "buildSettings": {"CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982d271689b3ae23f1ce090fa7346e40da", "guid": "bfdfe7dc352907fc980b868725387e98a27a49b00be0ac981e5db9f07832563d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab755c4417fbce5bfdebb88d2bfb0e2", "guid": "bfdfe7dc352907fc980b868725387e989878394bd4334bc5b75f69e19db62bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf4dcee73af28816a7d56922d26660a", "guid": "bfdfe7dc352907fc980b868725387e98e1897e3d8aed17671192779f85b1c248", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985378c37db784b4816ebbb6f4ac131ce1", "guid": "bfdfe7dc352907fc980b868725387e981450c482f5c290824d437888a184ae8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809cf986166b99eb2f06cde0dcc2c5007", "guid": "bfdfe7dc352907fc980b868725387e98e4fc9878a7a9815c87ead588b617e13f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e307b4abf61d135205c85dbbecf166", "guid": "bfdfe7dc352907fc980b868725387e98931f24651cd163bcb560cc6bf0293fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc75acf64bb2641894eebc2111caa710", "guid": "bfdfe7dc352907fc980b868725387e98d4cfb9e1cbc63fdd6505f85f9a29df9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980878b30aa5f2da8db5a14207e040f3d8", "guid": "bfdfe7dc352907fc980b868725387e98603cbbc70b8dca9ffe9ae016055178e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a48494148e362cac229a3bdf717ae2", "guid": "bfdfe7dc352907fc980b868725387e98de1a9153e9195626d2e18082bddd5583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ee0975cdd49c20b1522c4a7d1ed9f9", "guid": "bfdfe7dc352907fc980b868725387e984a1d400fa247fcd1999957a8682fc831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171f92e88d9a29b3544cbaeb463100be", "guid": "bfdfe7dc352907fc980b868725387e98d32709d3a2dbac2c57e7e408179d0866", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888522f18246cb885640f8c51637bdad0", "guid": "bfdfe7dc352907fc980b868725387e9852b51035de7e7268120a09bd61060c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdcc143ce61772236daea2b21b9d95e9", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831eec05fe31514b376886f8acfdf1b5b", "guid": "bfdfe7dc352907fc980b868725387e9851124daa4e024f512c6435eeca4166ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e3c024026df7446793d14f82a5f2ca", "guid": "bfdfe7dc352907fc980b868725387e98d1ab4894e5f8a787a882b5c8c35da9b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937ec9baf7dd120328650463868ee48f", "guid": "bfdfe7dc352907fc980b868725387e980ef321af2f9f2c834e49de13f3d55bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe101d1a465b1a66e1b2671a257b9382", "guid": "bfdfe7dc352907fc980b868725387e98f0d31a429c82f17b0991aeffa8fa048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da1db918e6ff33ac762bf630835ffa9e", "guid": "bfdfe7dc352907fc980b868725387e98a42a0c118dedfd60efdbb4d16492c7b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04a50a5fd3e197fac5ef9fd50f974f8", "guid": "bfdfe7dc352907fc980b868725387e98b3c686ca16e5bce7ed24eabc68c27a7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982af93d4fa2e7a24623bdb63e8849e2ea", "guid": "bfdfe7dc352907fc980b868725387e9828accb1b5b23f919db719dddafe64428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98476a6de94fd312e02ab7cafc0e489516", "guid": "bfdfe7dc352907fc980b868725387e981a70531bb3ca84e549cc6ba35f299fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f92c36e3829077c6a2481f7fbc22fe3", "guid": "bfdfe7dc352907fc980b868725387e98d126da608ed212ace70c11a62a0c1637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861333422cebf37902e02184d9979b019", "guid": "bfdfe7dc352907fc980b868725387e98482d9659cbec4dc229fbd14d4097dbb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9835b045a8c251ff1d30e36462d7af084e", "guid": "bfdfe7dc352907fc980b868725387e989514a07acecd425069c301ac83629a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b6d2ec792d36dcf3d72faa487c980cc", "guid": "bfdfe7dc352907fc980b868725387e98c5aeecdf68b8bd150764901c66af09a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fef6e2727bc20e327eb8e43ba90580", "guid": "bfdfe7dc352907fc980b868725387e98845832e3ddc9b9efa82804181189873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee253762ae011ef2d0103e9023698443", "guid": "bfdfe7dc352907fc980b868725387e988422c8d41a531ab0e5feda22b2729450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b25b546b81afbf18f57a342de0de61a", "guid": "bfdfe7dc352907fc980b868725387e98032b5432a99c409a15c23484d253896f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc656b0d9a61afaf87aed147579284c1", "guid": "bfdfe7dc352907fc980b868725387e988ec79e04b31e29a98b9058ffe01c7fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985513f17f26391bb379cfd467ea1c15de", "guid": "bfdfe7dc352907fc980b868725387e980e70a7a6c573b1b7ccd3e1d5ab5d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985648bfd059d7e4366892d0cf72d47ff3", "guid": "bfdfe7dc352907fc980b868725387e983e056fb9529e4b65a3f33b7e5569c272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65f5a08ddf369d30de7dcfb4a709462", "guid": "bfdfe7dc352907fc980b868725387e980341c01be7632895d55fd1dac2b54ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983990896cfecf29223b0b45aaf667b16d", "guid": "bfdfe7dc352907fc980b868725387e9844b9c045f1529acb68ed027e0a48b614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c937634ec816e323f74b3f5ce683396e", "guid": "bfdfe7dc352907fc980b868725387e98b1b6e64bf6811487ed4c6e1384077226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852bec2f44b997516ebae453cc7106e04", "guid": "bfdfe7dc352907fc980b868725387e981f2b6f47d148085767ead35147c92b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820703b4d00dc7a67d8fa0baf9d2b2d57", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025fbefec2e0866deb2fb9a223d7ee06", "guid": "bfdfe7dc352907fc980b868725387e98ff4558f6e06cbb7fbd15dd015ca18854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc47b58d2245d68e63ca292176ad92bc", "guid": "bfdfe7dc352907fc980b868725387e983caf79b54a68f27872c153b09b7289cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b033a5348b1ac7c90696707a6bf3c690", "guid": "bfdfe7dc352907fc980b868725387e983550715f5dc47c6e779aeb12290a7c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2ebb597ae98d9dbb672494cf5a02a1", "guid": "bfdfe7dc352907fc980b868725387e985800f051739a623bb35f7cc22cbdb0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc43d42c24ff47b5d28299f3587c5260", "guid": "bfdfe7dc352907fc980b868725387e98f2203b5d78f4ac52bb8f2618d0202e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d9f5d42a2eb3abd2048ce372cfb13a", "guid": "bfdfe7dc352907fc980b868725387e983616275298339a71f8ce5405edcae646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983016e52d9def6847dd3a89f08e9d515b", "guid": "bfdfe7dc352907fc980b868725387e9859b2f7d951b31f73b36500eee5613569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866604323a1be9ce1f0df1dc813f4da07", "guid": "bfdfe7dc352907fc980b868725387e9800fc7c6d98b1d598e7f0946391c24f5b"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}