/// Cấu hình API keys và endpoints cho ứng dụng
class ApiConfig {
  ApiConfig._();

  // === YOUTUBE DATA API V3 ===
  
  /// YouTube Data API v3 Key
  ///
  /// Để lấy API key:
  /// 1. Truy cập: https://console.cloud.google.com/apis/credentials
  /// 2. Tạo project mới hoặc chọn project hiện có
  /// 3. Enable "YouTube Data API v3"
  /// 4. Tạo "API Key" trong phần Credentials
  /// 5. Restrict API key cho "YouTube Data API v3"
  /// 6. Thay thế giá trị dưới đây
  static const String youtubeApiKey = 'AIzaSyDLAUzqDcL00qTn6iiKIfNGI4pJp8YnC64';
  
  /// Base URL cho YouTube Data API v3
  static const String youtubeBaseUrl = 'https://www.googleapis.com/youtube/v3';
  
  /// Kiểm tra xem YouTube API key đã đượ<PERSON> cấu hình chưa
  static bool get isYouTubeApiConfigured => 
      youtubeApiKey != 'YOUR_YOUTUBE_API_KEY' && youtubeApiKey.isNotEmpty;
  
  // === SUPABASE CONFIG ===
  
  /// Supabase URL và API Key được cấu hình trong main.dart
  /// Không cần thay đổi ở đây
  
  // === API TIMEOUTS ===
  
  /// Timeout cho YouTube API calls
  static const Duration youtubeApiTimeout = Duration(seconds: 15);
  
  /// Timeout cho Supabase calls
  static const Duration supabaseTimeout = Duration(seconds: 10);
  
  // === API LIMITS ===
  
  /// Số lượng video tối đa từ YouTube search
  static const int maxYouTubeResults = 15;
  
  /// Số lượng video mock khi không có API key
  static const int mockVideoCount = 5;
  
  // === SEARCH PARAMETERS ===
  
  /// Region code cho YouTube search (Việt Nam)
  static const String youtubeRegionCode = 'VN';
  
  /// Language code cho YouTube search (Tiếng Việt)
  static const String youtubeLanguageCode = 'vi';
  
  /// Video duration filter cho YouTube search
  static const String youtubeVideoDuration = 'medium'; // 4-20 phút
  
  /// Order parameter cho YouTube search
  static const String youtubeSearchOrder = 'relevance';
  
  // === DEVELOPMENT FLAGS ===
  
  /// Bật debug logs cho API calls
  static const bool enableApiDebugLogs = true;
  
  /// Bật mock data ngay cả khi có API key (để testing)
  static const bool forceMockData = false;
  
  // === HELPER METHODS ===
  
  /// Lấy thông tin cấu hình hiện tại
  static Map<String, dynamic> getConfigInfo() {
    return {
      'youtube_api_configured': isYouTubeApiConfigured,
      'youtube_base_url': youtubeBaseUrl,
      'max_results': maxYouTubeResults,
      'region_code': youtubeRegionCode,
      'language_code': youtubeLanguageCode,
      'debug_logs_enabled': enableApiDebugLogs,
      'force_mock_data': forceMockData,
    };
  }
  
  /// Log thông tin cấu hình (để debug)
  static void logConfigInfo() {
    final config = getConfigInfo();
    print('🔧 API Configuration:');
    config.forEach((key, value) {
      print('   $key: $value');
    });
  }
}

/// Extension để dễ dàng sử dụng config
extension ApiConfigExtension on ApiConfig {
  /// Tạo URL cho YouTube search với parameters mặc định
  static Uri createYouTubeSearchUrl({
    required String query,
    String? apiKey,
    int? maxResults,
    String? order,
    String? regionCode,
    String? languageCode,
    String? videoDuration,
  }) {
    return Uri.parse('${ApiConfig.youtubeBaseUrl}/search').replace(
      queryParameters: {
        'part': 'snippet',
        'q': query,
        'type': 'video',
        'maxResults': (maxResults ?? ApiConfig.maxYouTubeResults).toString(),
        'order': order ?? ApiConfig.youtubeSearchOrder,
        'regionCode': regionCode ?? ApiConfig.youtubeRegionCode,
        'relevanceLanguage': languageCode ?? ApiConfig.youtubeLanguageCode,
        'videoDuration': videoDuration ?? ApiConfig.youtubeVideoDuration,
        'key': apiKey ?? ApiConfig.youtubeApiKey,
      },
    );
  }
  
  /// Tạo URL cho YouTube search với pageToken (pagination)
  static Uri createYouTubeSearchUrlWithPageToken({
    required String query,
    required String pageToken,
    String? apiKey,
    int? maxResults,
    String? order,
    String? regionCode,
    String? languageCode,
    String? videoDuration,
  }) {
    return Uri.parse('${ApiConfig.youtubeBaseUrl}/search').replace(
      queryParameters: {
        'part': 'snippet',
        'q': query,
        'type': 'video',
        'maxResults': (maxResults ?? ApiConfig.maxYouTubeResults).toString(),
        'order': order ?? ApiConfig.youtubeSearchOrder,
        'regionCode': regionCode ?? ApiConfig.youtubeRegionCode,
        'relevanceLanguage': languageCode ?? ApiConfig.youtubeLanguageCode,
        'videoDuration': videoDuration ?? ApiConfig.youtubeVideoDuration,
        'pageToken': pageToken, // Thêm pageToken cho pagination
        'key': apiKey ?? ApiConfig.youtubeApiKey,
      },
    );
  }

  /// Tạo URL cho YouTube videos details
  static Uri createYouTubeVideosUrl({
    required String videoIds,
    String? apiKey,
  }) {
    return Uri.parse('${ApiConfig.youtubeBaseUrl}/videos').replace(
      queryParameters: {
        'part': 'snippet,statistics,contentDetails',
        'id': videoIds,
        'key': apiKey ?? ApiConfig.youtubeApiKey,
      },
    );
  }
}
