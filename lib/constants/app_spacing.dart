import 'package:flutter/material.dart';

/// <PERSON><PERSON> thống spacing hiện đại 2024 cho CookSpark
/// Thi<PERSON><PERSON> kế theo Material Design 3 và responsive design principles
class AppSpacing {
  AppSpacing._();

  // === BASE SPACING UNITS ===
  static const double _baseUnit = 4.0;

  // === SPACING SCALE ===
  static const double xs = _baseUnit;           // 4px
  static const double sm = _baseUnit * 2;       // 8px
  static const double md = _baseUnit * 3;       // 12px
  static const double lg = _baseUnit * 4;       // 16px
  static const double xl = _baseUnit * 5;       // 20px
  static const double xxl = _baseUnit * 6;      // 24px
  static const double xxxl = _baseUnit * 8;     // 32px
  static const double huge = _baseUnit * 10;    // 40px
  static const double massive = _baseUnit * 12; // 48px

  // === SEMANTIC SPACING ===
  
  /// Spacing cho các element nhỏ (icons, badges)
  static const double elementTiny = xs;         // 4px
  static const double elementSmall = sm;        // 8px
  static const double elementMedium = md;       // 12px
  static const double elementLarge = lg;        // 16px

  /// Spacing cho components (buttons, cards, inputs)
  static const double componentTiny = sm;       // 8px
  static const double componentSmall = md;      // 12px
  static const double componentMedium = lg;     // 16px
  static const double componentLarge = xl;      // 20px
  static const double componentXLarge = xxl;    // 24px

  /// Spacing cho sections và layouts
  static const double sectionSmall = xl;        // 20px
  static const double sectionMedium = xxl;      // 24px
  static const double sectionLarge = xxxl;      // 32px
  static const double sectionXLarge = huge;     // 40px

  /// Spacing cho pages và screens
  static const double pageSmall = xxl;          // 24px
  static const double pageMedium = xxxl;        // 32px
  static const double pageLarge = huge;         // 40px
  static const double pageXLarge = massive;     // 48px

  // === RESPONSIVE SPACING ===
  
  /// Lấy spacing responsive dựa trên screen size
  static double responsive(BuildContext context, {
    double mobile = lg,
    double tablet = xl,
    double desktop = xxl,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return mobile;
    } else if (screenWidth < 1200) {
      return tablet;
    } else {
      return desktop;
    }
  }

  /// Lấy horizontal padding responsive
  static double horizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 400) {
      return lg;           // 16px cho màn hình rất nhỏ
    } else if (screenWidth < 600) {
      return xl;           // 20px cho mobile
    } else if (screenWidth < 900) {
      return xxl;          // 24px cho tablet nhỏ
    } else if (screenWidth < 1200) {
      return xxxl;         // 32px cho tablet lớn
    } else {
      return huge;         // 40px cho desktop
    }
  }

  /// Lấy vertical spacing responsive
  static double verticalSpacing(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    
    if (screenHeight < 600) {
      return lg;           // 16px cho màn hình thấp
    } else if (screenHeight < 800) {
      return xl;           // 20px cho màn hình trung bình
    } else {
      return xxl;          // 24px cho màn hình cao
    }
  }

  // === PADDING PRESETS ===
  
  /// Padding cho các element nhỏ
  static const EdgeInsets paddingTiny = EdgeInsets.all(xs);
  static const EdgeInsets paddingSmall = EdgeInsets.all(sm);
  static const EdgeInsets paddingMedium = EdgeInsets.all(md);
  static const EdgeInsets paddingLarge = EdgeInsets.all(lg);
  static const EdgeInsets paddingXLarge = EdgeInsets.all(xl);

  /// Padding cho buttons
  static const EdgeInsets buttonPaddingSmall = EdgeInsets.symmetric(
    horizontal: md, 
    vertical: sm,
  );
  static const EdgeInsets buttonPaddingMedium = EdgeInsets.symmetric(
    horizontal: lg, 
    vertical: md,
  );
  static const EdgeInsets buttonPaddingLarge = EdgeInsets.symmetric(
    horizontal: xl, 
    vertical: lg,
  );

  /// Padding cho cards
  static const EdgeInsets cardPaddingSmall = EdgeInsets.all(md);
  static const EdgeInsets cardPaddingMedium = EdgeInsets.all(lg);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(xl);

  /// Padding cho screens
  static EdgeInsets screenPadding(BuildContext context) {
    return EdgeInsets.all(horizontalPadding(context));
  }

  static EdgeInsets screenPaddingHorizontal(BuildContext context) {
    return EdgeInsets.symmetric(horizontal: horizontalPadding(context));
  }

  static EdgeInsets screenPaddingVertical(BuildContext context) {
    return EdgeInsets.symmetric(vertical: verticalSpacing(context));
  }

  // === MARGIN PRESETS ===
  
  /// Margin cho các element
  static const EdgeInsets marginTiny = EdgeInsets.all(xs);
  static const EdgeInsets marginSmall = EdgeInsets.all(sm);
  static const EdgeInsets marginMedium = EdgeInsets.all(md);
  static const EdgeInsets marginLarge = EdgeInsets.all(lg);
  static const EdgeInsets marginXLarge = EdgeInsets.all(xl);

  /// Margin cho components
  static const EdgeInsets componentMarginSmall = EdgeInsets.all(sm);
  static const EdgeInsets componentMarginMedium = EdgeInsets.all(md);
  static const EdgeInsets componentMarginLarge = EdgeInsets.all(lg);

  /// Margin cho sections
  static const EdgeInsets sectionMarginSmall = EdgeInsets.symmetric(vertical: xl);
  static const EdgeInsets sectionMarginMedium = EdgeInsets.symmetric(vertical: xxl);
  static const EdgeInsets sectionMarginLarge = EdgeInsets.symmetric(vertical: xxxl);

  // === BORDER RADIUS ===
  
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 20.0;
  static const double radiusXXLarge = 24.0;
  static const double radiusCircular = 999.0;

  /// Border radius cho các component khác nhau
  static const BorderRadius buttonRadius = BorderRadius.all(Radius.circular(radiusLarge));
  static const BorderRadius cardRadius = BorderRadius.all(Radius.circular(radiusLarge));
  static const BorderRadius inputRadius = BorderRadius.all(Radius.circular(radiusMedium));
  static const BorderRadius chipRadius = BorderRadius.all(Radius.circular(radiusXLarge));
  static const BorderRadius modalRadius = BorderRadius.all(Radius.circular(radiusXLarge));

  // === ELEVATION ===
  
  static const double elevationNone = 0.0;
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationXLarge = 12.0;
  static const double elevationXXLarge = 16.0;

  // === HELPER METHODS ===
  
  /// Tạo SizedBox với height
  static Widget verticalSpace(double height) => SizedBox(height: height);
  
  /// Tạo SizedBox với width
  static Widget horizontalSpace(double width) => SizedBox(width: width);
  
  /// Tạo vertical space responsive
  static Widget responsiveVerticalSpace(BuildContext context) {
    return SizedBox(height: verticalSpacing(context));
  }
  
  /// Tạo horizontal space responsive
  static Widget responsiveHorizontalSpace(BuildContext context) {
    return SizedBox(width: horizontalPadding(context));
  }

  // === SPACING SHORTCUTS ===
  
  static Widget get spaceXS => verticalSpace(xs);
  static Widget get spaceSM => verticalSpace(sm);
  static Widget get spaceMD => verticalSpace(md);
  static Widget get spaceLG => verticalSpace(lg);
  static Widget get spaceXL => verticalSpace(xl);
  static Widget get spaceXXL => verticalSpace(xxl);
  static Widget get spaceXXXL => verticalSpace(xxxl);
  static Widget get spaceHuge => verticalSpace(huge);
  static Widget get spaceMassive => verticalSpace(massive);

  static Widget get hSpaceXS => horizontalSpace(xs);
  static Widget get hSpaceSM => horizontalSpace(sm);
  static Widget get hSpaceMD => horizontalSpace(md);
  static Widget get hSpaceLG => horizontalSpace(lg);
  static Widget get hSpaceXL => horizontalSpace(xl);
  static Widget get hSpaceXXL => horizontalSpace(xxl);
  static Widget get hSpaceXXXL => horizontalSpace(xxxl);
  static Widget get hSpaceHuge => horizontalSpace(huge);
  static Widget get hSpaceMassive => horizontalSpace(massive);
}

/// Extension để dễ dàng sử dụng spacing
extension SpacingExtension on num {
  /// Chuyển đổi number thành vertical space
  Widget get vSpace => SizedBox(height: toDouble());
  
  /// Chuyển đổi number thành horizontal space
  Widget get hSpace => SizedBox(width: toDouble());
  
  /// Chuyển đổi number thành EdgeInsets.all
  EdgeInsets get padding => EdgeInsets.all(toDouble());
  
  /// Chuyển đổi number thành EdgeInsets.all cho margin
  EdgeInsets get margin => EdgeInsets.all(toDouble());
  
  /// Chuyển đổi number thành BorderRadius.circular
  BorderRadius get radius => BorderRadius.circular(toDouble());
}

/// Extension cho EdgeInsets
extension EdgeInsetsExtension on EdgeInsets {
  /// Thêm responsive scaling
  EdgeInsets responsive(BuildContext context) {
    final scale = MediaQuery.of(context).size.width / 375; // Base width 375px
    final clampedScale = scale.clamp(0.8, 1.5); // Giới hạn scale
    
    return EdgeInsets.fromLTRB(
      left * clampedScale,
      top * clampedScale,
      right * clampedScale,
      bottom * clampedScale,
    );
  }
}
