import 'package:flutter/material.dart';
import '../utils/font_scale_manager.dart';

/// Typography system hiện đại 2024 theo Material Design 3 và Apple HIG
/// Đ<PERSON><PERSON> bảo responsive, accessibility và không bao giờ overflow
class AppTextStyles {
  // === DISPLAY STYLES - Cho tiêu đề lớn ===
  static const double _displayLarge = 57.0;   // Hero titles
  static const double _displayMedium = 45.0;  // Section headers
  static const double _displaySmall = 36.0;   // Page titles

  // === HEADLINE STYLES - Cho tiêu đề chính ===
  static const double _headlineLarge = 32.0;  // Main headings
  static const double _headlineMedium = 28.0; // Sub headings
  static const double _headlineSmall = 24.0;  // Card titles

  // === TITLE STYLES - Cho tiêu đề phụ ===
  static const double _titleLarge = 22.0;     // List headers
  static const double _titleMedium = 16.0;    // Card subtitles
  static const double _titleSmall = 14.0;     // Labels

  // === BODY STYLES - Cho nội dung ===
  static const double _bodyLarge = 16.0;      // Main content
  static const double _bodyMedium = 14.0;     // Secondary content
  static const double _bodySmall = 12.0;      // Captions

  // === LABEL STYLES - Cho nhãn và buttons ===
  static const double _labelLarge = 14.0;     // Button text
  static const double _labelMedium = 12.0;    // Form labels
  static const double _labelSmall = 11.0;     // Tiny labels

  // === DISPLAY STYLES ===

  /// Display Large - Hero titles, splash screens
  static TextStyle displayLarge(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _displayLarge),
        fontWeight: FontWeight.w400,
        height: 1.12,
        letterSpacing: -0.25,
      );

  /// Display Medium - Section headers
  static TextStyle displayMedium(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _displayMedium),
        fontWeight: FontWeight.w400,
        height: 1.16,
        letterSpacing: 0,
      );

  /// Display Small - Page titles
  static TextStyle displaySmall(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _displaySmall),
        fontWeight: FontWeight.w400,
        height: 1.22,
        letterSpacing: 0,
      );

  // === HEADLINE STYLES ===

  /// Headline Large - Main headings
  static TextStyle headlineLarge(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _headlineLarge),
        fontWeight: FontWeight.w400,
        height: 1.25,
        letterSpacing: 0,
      );

  /// Headline Medium - Sub headings
  static TextStyle headlineMedium(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _headlineMedium),
        fontWeight: FontWeight.w400,
        height: 1.29,
        letterSpacing: 0,
      );

  /// Headline Small - Card titles
  static TextStyle headlineSmall(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _headlineSmall),
        fontWeight: FontWeight.w400,
        height: 1.33,
        letterSpacing: 0,
      );

  // === TITLE STYLES ===

  /// Title Large - List headers
  static TextStyle titleLarge(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _titleLarge),
        fontWeight: FontWeight.w400,
        height: 1.27,
        letterSpacing: 0,
      );

  /// Title Medium - Card subtitles
  static TextStyle titleMedium(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _titleMedium),
        fontWeight: FontWeight.w500,
        height: 1.50,
        letterSpacing: 0.15,
      );

  /// Title Small - Labels
  static TextStyle titleSmall(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _titleSmall),
        fontWeight: FontWeight.w500,
        height: 1.43,
        letterSpacing: 0.1,
      );

  // === BODY STYLES ===

  /// Body Large - Main content
  static TextStyle bodyLarge(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _bodyLarge),
        fontWeight: FontWeight.w400,
        height: 1.50,
        letterSpacing: 0.5,
      );

  /// Body Medium - Secondary content
  static TextStyle bodyMedium(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _bodyMedium),
        fontWeight: FontWeight.w400,
        height: 1.43,
        letterSpacing: 0.25,
      );

  /// Body Small - Captions
  static TextStyle bodySmall(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _bodySmall),
        fontWeight: FontWeight.w400,
        height: 1.33,
        letterSpacing: 0.4,
      );

  // === LABEL STYLES ===

  /// Label Large - Button text
  static TextStyle labelLarge(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _labelLarge),
        fontWeight: FontWeight.w500,
        height: 1.43,
        letterSpacing: 0.1,
      );

  /// Label Medium - Form labels
  static TextStyle labelMedium(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _labelMedium),
        fontWeight: FontWeight.w500,
        height: 1.33,
        letterSpacing: 0.5,
      );

  /// Label Small - Tiny labels
  static TextStyle labelSmall(BuildContext context) => TextStyle(
        fontSize: _responsiveSize(context, _labelSmall),
        fontWeight: FontWeight.w500,
        height: 1.45,
        letterSpacing: 0.5,
      );

  // === BACKWARD COMPATIBILITY - Các method cũ để tương thích ===

  /// Large Title - Tương thích với code cũ
  static TextStyle largeTitle(BuildContext context) => displayLarge(context);

  /// Title 1 - Tương thích với code cũ
  static TextStyle title1(BuildContext context) => headlineLarge(context);

  /// Title 2 - Tương thích với code cũ
  static TextStyle title2(BuildContext context) => headlineMedium(context);

  /// Title 3 - Tương thích với code cũ
  static TextStyle title3(BuildContext context) => headlineSmall(context);

  /// Headline - Tương thích với code cũ
  static TextStyle headline(BuildContext context) => titleLarge(context).copyWith(
        fontWeight: FontWeight.w600,
      );

  /// Body - Tương thích với code cũ
  static TextStyle body(BuildContext context) => bodyLarge(context);

  /// Callout - Tương thích với code cũ
  static TextStyle callout(BuildContext context) => titleMedium(context);

  /// Subhead - Tương thích với code cũ
  static TextStyle subhead(BuildContext context) => bodyMedium(context);

  /// Footnote - Tương thích với code cũ
  static TextStyle footnote(BuildContext context) => bodySmall(context);

  /// Caption 1 - Tương thích với code cũ
  static TextStyle caption1(BuildContext context) => labelMedium(context);

  /// Caption 2 - Tương thích với code cũ
  static TextStyle caption2(BuildContext context) => labelSmall(context);

  /// Helper: Tính toán font size responsive và an toàn
  /// Không bao giờ để overflow xảy ra
  static double _responsiveSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Device scale dựa trên width
    double deviceScale = 1.0;
    if (screenWidth < 350) {
      deviceScale = 0.85; // iPhone SE, màn hình rất nhỏ
    } else if (screenWidth < 400) {
      deviceScale = 0.9; // iPhone mini, màn hình nhỏ
    } else if (screenWidth < 600) {
      deviceScale = 1.0; // Phone thông thường
    } else if (screenWidth < 900) {
      deviceScale = 1.05; // Tablet nhỏ
    } else {
      deviceScale = 1.1; // Tablet lớn
    }

    // Áp dụng device scale trước
    final deviceAdjustedSize = baseSize * deviceScale;

    // Sau đó áp dụng Dynamic Font Scale
    final finalSize = FontScaleManager.getResponsiveFontSize(context, deviceAdjustedSize);

    // Đảm bảo không nhỏ hơn minimum readable size
    const minReadableSize = 10.0;
    return finalSize < minReadableSize ? minReadableSize : finalSize;
  }

  /// Utility: Get safe text scale factor (deprecated - use FontScaleManager)
  static double getTextScaleFactor(BuildContext context) {
    return FontScaleManager.getSystemFontScale(context);
  }

  /// Utility: Check if current text size is accessibility size
  static bool isAccessibilitySize(BuildContext context) {
    return FontScaleManager.isAccessibilitySize(context);
  }

  /// Utility: Check if should use vertical layout for large fonts
  static bool shouldUseVerticalLayout(BuildContext context) {
    return FontScaleManager.shouldUseVerticalLayout(context);
  }
}

/// Extension để dễ dùng hơn
extension TextStyleExtensions on TextStyle {
  /// Copy style với màu sắc mới
  TextStyle withColor(Color color) {
    return copyWith(color: color);
  }

  /// Copy style với font weight mới
  TextStyle withWeight(FontWeight weight) {
    return copyWith(fontWeight: weight);
  }

  /// Copy style với opacity
  TextStyle withOpacity(double opacity) {
    return copyWith(color: color?.withValues(alpha: opacity));
  }
}
