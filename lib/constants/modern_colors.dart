import 'package:flutter/material.dart';

/// <PERSON><PERSON> thống màu sắc hiện đại 2024 cho CookSpark
/// <PERSON>hi<PERSON><PERSON> kế theo xu hướng Glassmorphism, Neumorphism và Material Design 3
class ModernColors {
  ModernColors._();

  // === BRAND COLORS - <PERSON><PERSON><PERSON> thương hiệu ===
  static const Color primaryBrand = Color(0xFF6366F1);      // Indigo modern
  static const Color secondaryBrand = Color(0xFF8B5CF6);    // Purple vibrant
  static const Color accentBrand = Color(0xFF06B6D4);       // Cyan fresh

  // === SEMANTIC COLORS - <PERSON><PERSON><PERSON> ngữ nghĩa ===
  static const Color success = Color(0xFF10B981);           // Emerald
  static const Color successDark = Color(0xFF059669);
  static const Color warning = Color(0xFFF59E0B);           // Amber
  static const Color warningDark = Color(0xFFD97706);
  static const Color error = Color(0xFFEF4444);             // Red
  static const Color errorDark = Color(0xFFDC2626);
  static const Color info = Color(0xFF3B82F6);              // Blue
  static const Color infoDark = Color(0xFF2563EB);

  // === MEAL COLORS - Màu bữa ăn hiện đại ===
  static const Color breakfastPrimary = Color(0xFFFF6B35);  // Orange vibrant
  static const Color breakfastSecondary = Color(0xFFFFA726);
  static const Color lunchPrimary = Color(0xFF00BCD4);      // Cyan
  static const Color lunchSecondary = Color(0xFF26C6DA);
  static const Color dinnerPrimary = Color(0xFF5C6BC0);     // Indigo
  static const Color dinnerSecondary = Color(0xFF7986CB);

  // === DARK MODE SURFACES - Bề mặt chế độ tối ===
  static const Color darkSurface0 = Color(0xFF000000);      // True black
  static const Color darkSurface1 = Color(0xFF0F0F0F);      // Slightly elevated
  static const Color darkSurface2 = Color(0xFF1A1A1A);      // Cards
  static const Color darkSurface3 = Color(0xFF262626);      // Modals
  static const Color darkSurface4 = Color(0xFF2D2D2D);      // Navigation
  static const Color darkSurface5 = Color(0xFF404040);      // App bars

  // === LIGHT MODE SURFACES - Bề mặt chế độ sáng ===
  static const Color lightSurface0 = Color(0xFFFFFFFF);     // Pure white
  static const Color lightSurface1 = Color(0xFFFAFAFA);     // Off white
  static const Color lightSurface2 = Color(0xFFF5F5F5);     // Light gray
  static const Color lightSurface3 = Color(0xFFEEEEEE);     // Medium gray
  static const Color lightSurface4 = Color(0xFFE0E0E0);     // Border gray
  static const Color lightSurface5 = Color(0xFFBDBDBD);     // Disabled gray

  // === GRADIENT COLORS - Màu gradient hiện đại ===
  static const List<Color> primaryGradient = [
    Color(0xFF6366F1),  // Indigo
    Color(0xFF8B5CF6),  // Purple
  ];

  static const List<Color> successGradient = [
    Color(0xFF10B981),  // Emerald
    Color(0xFF34D399),  // Light emerald
  ];

  static const List<Color> warningGradient = [
    Color(0xFFF59E0B),  // Amber
    Color(0xFFFBBF24),  // Light amber
  ];

  static const List<Color> errorGradient = [
    Color(0xFFEF4444),  // Red
    Color(0xFFF87171),  // Light red
  ];

  // === GLASSMORPHISM COLORS - Màu hiệu ứng kính ===
  static const Color glassWhite = Color(0x1AFFFFFF);
  static const Color glassBlack = Color(0x1A000000);
  static const Color glassPrimary = Color(0x1A6366F1);
  static const Color glassSecondary = Color(0x1A8B5CF6);

  // === TEXT COLORS ===
  static const Color textPrimary = Color(0xFFE6E6E6);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textTertiary = Color(0xFF808080);
  static const Color textDisabled = Color(0xFF4D4D4D);

  // === BORDER COLORS ===
  static const Color borderPrimary = Color(0xFF404040);
  static const Color borderSecondary = Color(0xFF2A2A2A);
  static const Color borderAccent = Color(0xFF666666);

  /// Lấy màu gradient hiện đại cho buttons
  static LinearGradient getModernGradient(Color primaryColor, {bool isDark = false}) {
    if (isDark) {
      final hsl = HSLColor.fromColor(primaryColor);
      final color1 = hsl.withLightness(0.6).withSaturation(0.9).toColor();
      final color2 = hsl.withLightness(0.4).withSaturation(0.8).toColor();
      
      return LinearGradient(
        colors: [color1, color2],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    } else {
      return LinearGradient(
        colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
  }

  /// Lấy màu glow effect cho dark mode
  static BoxShadow getGlowEffect(Color color, {double intensity = 0.3}) {
    return BoxShadow(
      color: color.withValues(alpha: intensity),
      blurRadius: 20,
      spreadRadius: 2,
      offset: const Offset(0, 0),
    );
  }

  /// Lấy màu cho elevated surfaces
  static Color getElevatedSurface(int elevation) {
    switch (elevation) {
      case 0: return darkSurface0;
      case 1: return darkSurface1;
      case 2: return darkSurface2;
      case 3: return darkSurface3;
      case 4: return darkSurface4;
      case 5: return darkSurface5;
      default: return darkSurface5;
    }
  }
}

/// Extension cho ColorScheme để thêm màu hiện đại
extension ModernColorScheme on ColorScheme {
  /// Lấy màu text phù hợp với brightness
  Color get modernTextPrimary => brightness == Brightness.dark 
      ? ModernColors.textPrimary 
      : const Color(0xFF1A1A1A);

  Color get modernTextSecondary => brightness == Brightness.dark 
      ? ModernColors.textSecondary 
      : const Color(0xFF666666);

  Color get modernTextTertiary => brightness == Brightness.dark 
      ? ModernColors.textTertiary 
      : const Color(0xFF999999);

  /// Lấy màu border phù hợp
  Color get modernBorderPrimary => brightness == Brightness.dark 
      ? ModernColors.borderPrimary 
      : const Color(0xFFE0E0E0);

  Color get modernBorderSecondary => brightness == Brightness.dark 
      ? ModernColors.borderSecondary 
      : const Color(0xFFF0F0F0);

  /// Lấy màu success phù hợp
  Color get modernSuccess => brightness == Brightness.dark
      ? ModernColors.successDark
      : ModernColors.success;

  /// Lấy màu warning phù hợp
  Color get modernWarning => brightness == Brightness.dark
      ? ModernColors.warningDark
      : ModernColors.warning;

  /// Lấy màu error phù hợp
  Color get modernError => brightness == Brightness.dark
      ? ModernColors.errorDark
      : ModernColors.error;

  /// Lấy màu info phù hợp
  Color get modernInfo => brightness == Brightness.dark
      ? ModernColors.infoDark
      : ModernColors.info;

  /// Lấy gradient hiện đại cho primary color
  LinearGradient get modernPrimaryGradient => LinearGradient(
    colors: ModernColors.primaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Lấy gradient cho success
  LinearGradient get modernSuccessGradient => LinearGradient(
    colors: ModernColors.successGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Lấy gradient cho warning
  LinearGradient get modernWarningGradient => LinearGradient(
    colors: ModernColors.warningGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Lấy gradient cho error
  LinearGradient get modernErrorGradient => LinearGradient(
    colors: ModernColors.errorGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Lấy màu glassmorphism phù hợp
  Color get modernGlass => brightness == Brightness.dark
      ? ModernColors.glassWhite
      : ModernColors.glassBlack;
}
