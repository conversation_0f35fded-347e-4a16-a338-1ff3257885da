import 'package:flutter/material.dart';

/// Lớp quản lý đa ngôn ngữ cho ứng dụng CookSpark
/// Hỗ trợ Tiếng Việt (vi) và English (en)
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<Locale> supportedLocales = [
    Locale('vi', ''), // Tiếng Việt
    Locale('en', ''), // English
  ];

  // === COMMON STRINGS ===
  String get appName => _getValue('app_name');
  String get ok => _getValue('ok');
  String get cancel => _getValue('cancel');
  String get save => _getValue('save');
  String get delete => _getValue('delete');
  String get edit => _getValue('edit');
  String get back => _getValue('back');
  String get next => _getValue('next');
  String get complete => _getValue('complete');
  String get loading => _getValue('loading');
  String get error => _getValue('error');
  String get success => _getValue('success');

  // === AUTHENTICATION ===
  String get login => _getValue('login');
  String get register => _getValue('register');
  String get logout => _getValue('logout');
  String get email => _getValue('email');
  String get password => _getValue('password');
  String get forgotPassword => _getValue('forgot_password');
  String get loginWithGoogle => _getValue('login_with_google');

  // === LANGUAGE SELECTION ===
  String get chooseYourLanguage => _getValue('choose_your_language');
  String get selectPreferredLanguage => _getValue('select_preferred_language');
  String get continueText => _getValue('continue_text');
  String get comingSoon => _getValue('coming_soon');

  // === ONBOARDING ===
  String get chooseLanguage => _getValue('choose_language');
  String get languageDescription => _getValue('language_description');
  String get vietnamese => _getValue('vietnamese');
  String get english => _getValue('english');
  String get whatsYourName => _getValue('whats_your_name');
  String get nameDescription => _getValue('name_description');
  String get enterYourName => _getValue('enter_your_name');
  String get whatsYourGender => _getValue('whats_your_gender');
  String get genderDescription => _getValue('gender_description');
  String get male => _getValue('male');
  String get female => _getValue('female');
  String get other => _getValue('other');
  String get cookingStyle => _getValue('cooking_style');
  String get cookingStyleDescription => _getValue('cooking_style_description');
  String get simple => _getValue('simple');
  String get elaborate => _getValue('elaborate');

  // === HOME SCREEN ===
  String get home => _getValue('home');
  String get mealSuggestion => _getValue('meal_suggestion');
  String get weeklyMenu => _getValue('weekly_menu');
  String get savedVideos => _getValue('saved_videos');
  String get settings => _getValue('settings');

  // === MEAL SUGGESTION ===
  String get whatToEatToday => _getValue('what_to_eat_today');
  String get getSuggestion => _getValue('get_suggestion');
  String get searchRecipes => _getValue('search_recipes');
  String get mealSuggestionTitle => _getValue('meal_suggestion_title');
  String get whatMealToPrepare => _getValue('what_meal_to_prepare');
  String get chooseMealTypeDescription => _getValue('choose_meal_type_description');
  String get areYouVegetarian => _getValue('are_you_vegetarian');
  String get vegetarianFilterDescription => _getValue('vegetarian_filter_description');
  String get no => _getValue('no');
  String get yes => _getValue('yes');
  String get eatAllFoods => _getValue('eat_all_foods');
  String get eatOnlyVegetarian => _getValue('eat_only_vegetarian');
  String get whatMainIngredients => _getValue('what_main_ingredients');
  String get chooseIngredientsDescription => _getValue('choose_ingredients_description');
  String get orChooseSpecificIngredients => _getValue('or_choose_specific_ingredients');
  String get letAiAutoSuggest => _getValue('let_ai_auto_suggest');
  String get pleaseSelectMealType => _getValue('please_select_meal_type');
  String get systemWillChooseBestIngredients => _getValue('system_will_choose_best_ingredients');
  String get findDishes => _getValue('find_dishes');

  // === HOME SCREEN GREETINGS ===
  String get goodMorning => _getValue('good_morning');
  String get goodAfternoon => _getValue('good_afternoon');
  String get goodEvening => _getValue('good_evening');
  String get morningMessage => _getValue('morning_message');
  String get afternoonMessage => _getValue('afternoon_message');
  String get eveningMessage => _getValue('evening_message');
  String get quickAccess => _getValue('quick_access');
  String get savedDishes => _getValue('saved_dishes');
  String get viewMealPlan => _getValue('view_meal_plan');
  String get customizeApp => _getValue('customize_app');
  String get personalInfo => _getValue('personal_info');
  String get aiWillHelpYouFind => _getValue('ai_will_help_you_find');
  String get personalInformation => _getValue('personal_information');

  // === WEEKLY MENU ===
  String get today => _getValue('today');
  String get noMealsForThisDay => _getValue('no_meals_for_this_day');
  String get add => _getValue('add');

  // === SETTINGS ===
  String get account => _getValue('account');
  String get signOut => _getValue('sign_out');
  String get viewEditAccountInfo => _getValue('view_edit_account_info');
  String get signOutFromCurrentAccount => _getValue('sign_out_from_current_account');
  String get interfaceMode => _getValue('interface_mode');
  String get language => _getValue('language');
  String get darkMode => _getValue('dark_mode');
  String get enableDisableDarkMode => _getValue('enable_disable_dark_mode');
  String get primaryColors => _getValue('primary_colors');
  String get secondaryColors => _getValue('secondary_colors');
  String get mealColors => _getValue('meal_colors');
  String get breakfast => _getValue('breakfast');
  String get lunch => _getValue('lunch');
  String get dinner => _getValue('dinner');
  String get dataManagement => _getValue('data_management');
  String get clearCacheRefreshData => _getValue('clear_cache_refresh_data');
  String get updateAllDishesFromSource => _getValue('update_all_dishes_from_source');
  String get appInfo => _getValue('app_info');
  String get aboutApp => _getValue('about_app');
  String get aboutAppDescription => _getValue('about_app_description');
  String get authorInfo => _getValue('author_info');
  String get authorInfoDescription => _getValue('author_info_description');
  String get appFeedback => _getValue('app_feedback');
  String get shareFeedbackToImprove => _getValue('share_feedback_to_improve');
  String get resetToDefaultInterface => _getValue('reset_to_default_interface');
  String get clearInterfaceCache => _getValue('clear_interface_cache');
  String get synchronizeData => _getValue('synchronize_data');
  String get syncDataBetweenDevices => _getValue('sync_data_between_devices');
  String get syncNow => _getValue('sync_now');

  // === DIALOG MESSAGES ===
  String get chooseLanguageDialog => _getValue('choose_language_dialog');
  String get confirmSignOut => _getValue('confirm_sign_out');
  String get confirmClearCache => _getValue('confirm_clear_cache');
  String get confirmClearCacheMessage => _getValue('confirm_clear_cache_message');
  String get confirmClearInterfaceCache => _getValue('confirm_clear_interface_cache');
  String get confirmClearInterfaceCacheMessage => _getValue('confirm_clear_interface_cache_message');

  // === NOTIFICATION MESSAGES ===
  String get signOutSuccess => _getValue('sign_out_success');
  String get signOutError => _getValue('sign_out_error');
  String get clearCacheSuccess => _getValue('clear_cache_success');
  String get clearCacheError => _getValue('clear_cache_error');
  String get resetToDefaultSuccess => _getValue('reset_to_default_success');
  String get clearInterfaceCacheSuccess => _getValue('clear_interface_cache_success');
  String get syncOptimized => _getValue('sync_optimized');
  String get cannotOpenLink => _getValue('cannot_open_link');

  // === FALLBACK NAMES ===
  String get you => _getValue('you');
  String get user => _getValue('user');
  String get brother => _getValue('brother');
  String get sister => _getValue('sister');

  // === WEEKLY MENU ADDITIONAL ===
  String get errorOccurred => _getValue('error_occurred');
  String get cannotLoadMenu => _getValue('cannot_load_menu');
  String get tryAgain => _getValue('try_again');
  String get weekMenuEmpty => _getValue('week_menu_empty');
  String get addDishesToMenu => _getValue('add_dishes_to_menu');
  String get addDish => _getValue('add_dish');
  String get totalDishes => _getValue('total_dishes');
  String get eaten => _getValue('eaten');
  String get completion => _getValue('completion');
  String get errorLoadingMenu => _getValue('error_loading_menu');
  String get errorUpdating => _getValue('error_updating');

  // === SETTINGS ADDITIONAL ===
  String get personalInformationSettings => _getValue('personal_information_settings');
  String get signOutFromAccount => _getValue('sign_out_from_account');
  String get confirmSignOutTitle => _getValue('confirm_sign_out_title');
  String get confirmSignOutMessage => _getValue('confirm_sign_out_message');
  String get signOutSuccessMessage => _getValue('sign_out_success_message');
  String get signOutErrorMessage => _getValue('sign_out_error_message');
  String get resetToDefaultSuccessMessage => _getValue('reset_to_default_success_message');
  String get clearInterfaceCacheSuccessMessage => _getValue('clear_interface_cache_success_message');
  String get syncOptimizedMessage => _getValue('sync_optimized_message');
  String get cannotOpenLinkMessage => _getValue('cannot_open_link_message');
  String get clearCacheSuccessMessage => _getValue('clear_cache_success_message');
  String get clearCacheErrorMessage => _getValue('clear_cache_error_message');

  // === AUTHENTICATION ===
  String get loginTitle => _getValue('login_title');
  String get welcomeBack => _getValue('welcome_back');
  String get emailAddress => _getValue('email_address');
  String get passwordField => _getValue('password_field');
  String get forgotPasswordLink => _getValue('forgot_password_link');
  String get rememberLogin => _getValue('remember_login');
  String get orLoginWith => _getValue('or_login_with');
  String get continueWithGoogle => _getValue('continue_with_google');
  String get dontHaveAccount => _getValue('dont_have_account');
  String get createAccountLink => _getValue('create_account_link');
  String get pleaseEnterEmail => _getValue('please_enter_email');
  String get invalidEmail => _getValue('invalid_email');
  String get pleaseEnterPassword => _getValue('please_enter_password');
  String get passwordMinLength => _getValue('password_min_length');
  String get loginFailed => _getValue('login_failed');
  String get errorOccurredTryAgain => _getValue('error_occurred_try_again');
  String get openingGoogleLogin => _getValue('opening_google_login');
  String get loginFailedTryAgain => _getValue('login_failed_try_again');
  String get loginCancelled => _getValue('login_cancelled');
  String get tryAgainButton => _getValue('try_again_button');
  String get googleLoginError => _getValue('google_login_error');

  // === REGISTER SCREEN ===
  String get createNewAccount => _getValue('create_new_account');
  String get otpSentToEmail => _getValue('otp_sent_to_email');
  String get unknownErrorOccurred => _getValue('unknown_error_occurred');
  String get errorOccurredTryAgainLater => _getValue('error_occurred_try_again_later');
  String get googleLoginSuccessful => _getValue('google_login_successful');
  String get googleLoginFailed => _getValue('google_login_failed');
  String get googleLoginErrorOccurred => _getValue('google_login_error_occurred');
  String get enterYourEmail => _getValue('enter_your_email');
  String get otpVerificationInfo => _getValue('otp_verification_info');
  String get sendOtpCode => _getValue('send_otp_code');
  String get orText => _getValue('or_text');
  String get signInWithGoogle => _getValue('sign_in_with_google');
  String get alreadyHaveAccount => _getValue('already_have_account');
  String get youCan => _getValue('you_can');
  String get forgotPasswordShort => _getValue('forgot_password_short');
  String get pleaseWaitSeconds => _getValue('please_wait_seconds');
  String get percentComplete => _getValue('percent_complete');
  String get pleaseWaitBeforeRetry => _getValue('please_wait_before_retry');

  // === FEATURE DEVELOPMENT NOTIFICATIONS ===
  String get featureInDevelopment => _getValue('feature_in_development');
  String get weeklyMenuComingSoon => _getValue('weekly_menu_coming_soon');
  String get addToWeeklyMenuComingSoon => _getValue('add_to_weekly_menu_coming_soon');
  String get stayTuned => _getValue('stay_tuned');
  String get understood => _getValue('understood');

  // === WELCOME DIALOG ===
  String get welcomeToApp => _getValue('welcome_to_app');
  String get upcomingFeatures => _getValue('upcoming_features');
  String get upcomingFeaturesDescription => _getValue('upcoming_features_description');
  String get dontShowAgain => _getValue('dont_show_again');
  String get gotIt => _getValue('got_it');

  // === DELETE DIALOG ===
  String get deleteVideoSaved => _getValue('delete_video_saved');
  String get confirmDeleteVideo => _getValue('confirm_delete_video');
  String get deleteNow => _getValue('delete_now');
  String get cancelAction => _getValue('cancel_action');
  String get deleteAllVideos => _getValue('delete_all_videos');
  String get confirmDeleteAllVideos => _getValue('confirm_delete_all_videos');
  String get deleteAll => _getValue('delete_all');
  String get logoutDialog => _getValue('logout_dialog');
  String get confirmLogout => _getValue('confirm_logout');
  String get stayLoggedIn => _getValue('stay_logged_in');
  String get deleteData => _getValue('delete_data');
  String get confirmDeleteData => _getValue('confirm_delete_data');
  String get deleteDataAction => _getValue('delete_data_action');

  // === NOTIFICATION MESSAGES ===
  String get viewList => _getValue('view_list');
  String get close => _getValue('close');
  String get undo => _getValue('undo');
  String get savedSuccessfully => _getValue('saved_successfully');
  String get saveError => _getValue('save_error');
  String get deletedSuccessfully => _getValue('deleted_successfully');
  String get deleteError => _getValue('delete_error');

  // === DATE AND TIME ===
  String get justNow => _getValue('just_now');
  String get yesterday => _getValue('yesterday');
  String get tomorrow => _getValue('tomorrow');

  // === SAVED VIDEOS SCREEN ===
  String get savedDishesTitle => _getValue('saved_dishes_title');
  String get noSavedDishes => _getValue('no_saved_dishes');
  String get findAndSaveFavorites => _getValue('find_and_save_favorites');
  String get searchForDishes => _getValue('search_for_dishes');
  String get savedOn => _getValue('saved_on');
  String get deletingVideo => _getValue('deleting_video');
  String get videoDeletedFromFavorites => _getValue('video_deleted_from_favorites');
  String get cannotDeleteVideo => _getValue('cannot_delete_video');
  String get restoringVideo => _getValue('restoring_video');
  String get videoRestored => _getValue('video_restored');
  String get cannotRestoreVideo => _getValue('cannot_restore_video');

  // === VALIDATION MESSAGES ===
  String get pleaseSelectLanguage => _getValue('please_select_language');
  String get pleaseEnterName => _getValue('please_enter_name');
  String get pleaseSelectGender => _getValue('please_select_gender');
  String get pleaseSelectCookingStyle => _getValue('please_select_cooking_style');

  // Method để lấy giá trị theo ngôn ngữ (public for testing)
  String getValue(String key) => _getValue(key);

  // Private method để lấy giá trị theo ngôn ngữ
  String _getValue(String key) {
    final isVietnamese = locale.languageCode == 'vi';
    
    switch (key) {
      // Common
      case 'app_name': return 'CookSpark AI';
      case 'ok': return isVietnamese ? 'Đồng ý' : 'OK';
      case 'cancel': return isVietnamese ? 'Hủy' : 'Cancel';
      case 'save': return isVietnamese ? 'Lưu' : 'Save';
      case 'delete': return isVietnamese ? 'Xóa' : 'Delete';
      case 'edit': return isVietnamese ? 'Sửa' : 'Edit';
      case 'back': return isVietnamese ? 'Quay lại' : 'Back';
      case 'next': return isVietnamese ? 'Tiếp theo' : 'Next';
      case 'complete': return isVietnamese ? 'Hoàn thành' : 'Complete';
      case 'loading': return isVietnamese ? 'Đang tải...' : 'Loading...';
      case 'error': return isVietnamese ? 'Lỗi' : 'Error';
      case 'success': return isVietnamese ? 'Thành công' : 'Success';

      // Language Selection
      case 'choose_your_language': return isVietnamese ? 'Chọn ngôn ngữ của bạn' : 'Choose your language';
      case 'select_preferred_language': return isVietnamese ? 'Chọn ngôn ngữ bạn muốn sử dụng' : 'Select your preferred language to continue';
      case 'continue_text': return isVietnamese ? 'Tiếp tục' : 'Continue';
      case 'coming_soon': return isVietnamese ? 'Sắp ra mắt' : 'Coming Soon';

      // Authentication
      case 'login': return isVietnamese ? 'Đăng nhập' : 'Login';
      case 'register': return isVietnamese ? 'Đăng ký' : 'Register';
      case 'logout': return isVietnamese ? 'Đăng xuất' : 'Logout';
      case 'email': return isVietnamese ? 'Email' : 'Email';
      case 'password': return isVietnamese ? 'Mật khẩu' : 'Password';
      case 'forgot_password': return isVietnamese ? 'Quên mật khẩu' : 'Forgot Password';
      case 'login_with_google': return isVietnamese ? 'Đăng nhập với Google' : 'Login with Google';

      // Onboarding
      case 'choose_language': return isVietnamese
          ? 'Chọn ngôn ngữ\nChoose Language'
          : 'Choose Language\nChọn ngôn ngữ';
      case 'language_description': return isVietnamese
          ? 'Bạn muốn sử dụng ngôn ngữ nào?\nWhich language would you like to use?'
          : 'Which language would you like to use?\nBạn muốn sử dụng ngôn ngữ nào?';
      case 'vietnamese': return isVietnamese ? '🇻🇳 Tiếng Việt' : '🇻🇳 Vietnamese';
      case 'english': return isVietnamese ? '🇺🇸 English' : '🇺🇸 English';
      case 'whats_your_name': return isVietnamese ? 'Tên bạn là gì?' : 'What\'s your name?';
      case 'name_description': return isVietnamese 
          ? 'Chúng tôi muốn biết nên gọi bạn là gì'
          : 'We\'d like to know what to call you';
      case 'enter_your_name': return isVietnamese ? 'Nhập tên của bạn' : 'Enter your name';
      case 'whats_your_gender': return isVietnamese ? 'Giới tính của bạn?' : 'What\'s your gender?';
      case 'gender_description': return isVietnamese 
          ? 'Điều này giúp chúng tôi đưa ra gợi ý phù hợp hơn'
          : 'This helps us provide better recommendations';
      case 'male': return isVietnamese ? '👨 Nam' : '👨 Male';
      case 'female': return isVietnamese ? '👩 Nữ' : '👩 Female';
      case 'other': return isVietnamese ? '🏳️‍⚧️ Khác' : '🏳️‍⚧️ Other';
      case 'cooking_style': return isVietnamese 
          ? 'Bạn hướng tới sự đơn giản hay cầu kỳ khi nấu ăn?'
          : 'What\'s your cooking style?';
      case 'cooking_style_description': return isVietnamese 
          ? 'Chọn phong cách nấu ăn phù hợp với bạn'
          : 'Choose your preferred cooking approach';
      case 'simple': return isVietnamese ? '🍳 Đơn giản' : '🍳 Simple & Quick';
      case 'elaborate': return isVietnamese ? '👨‍🍳 Cầu kỳ' : '👨‍🍳 Elaborate & Detailed';

      // Home Screen
      case 'home': return isVietnamese ? 'Trang chủ' : 'Home';
      case 'meal_suggestion': return isVietnamese ? 'Gợi ý món ăn' : 'Meal Suggestion';
      case 'weekly_menu': return isVietnamese ? 'Thực đơn tuần' : 'Weekly Menu';
      case 'saved_videos': return isVietnamese ? 'Video đã lưu' : 'Saved Videos';
      case 'settings': return isVietnamese ? 'Cài đặt' : 'Settings';

      // Meal Suggestion
      case 'what_to_eat_today': return isVietnamese ? 'Hôm nay ăn gì?' : 'What to eat today?';
      case 'get_suggestion': return isVietnamese ? 'Lấy gợi ý' : 'Get Suggestion';
      case 'search_recipes': return isVietnamese ? 'Tìm công thức' : 'Search Recipes';
      case 'meal_suggestion_title': return isVietnamese ? 'Gợi ý món ăn' : 'Meal Suggestion';
      case 'what_meal_to_prepare': return isVietnamese ? 'Bạn muốn chuẩn bị bữa nào?' : 'What meal would you like to prepare?';
      case 'choose_meal_type_description': return isVietnamese ? 'Chọn loại bữa ăn để chúng tôi gợi ý món phù hợp' : 'Choose meal type so we can suggest suitable dishes';
      case 'are_you_vegetarian': return isVietnamese ? 'Bạn có ăn chay không?' : 'Are you vegetarian?';
      case 'vegetarian_filter_description': return isVietnamese ? 'Điều này giúp chúng tôi lọc ra những món ăn phù hợp' : 'This helps us filter suitable dishes for you';
      case 'no': return isVietnamese ? 'Không' : 'No';
      case 'yes': return isVietnamese ? 'Có' : 'Yes';
      case 'eat_all_foods': return isVietnamese ? 'Tôi ăn tất cả các loại thực phẩm' : 'I eat all types of food';
      case 'eat_only_vegetarian': return isVietnamese ? 'Tôi chỉ ăn thực phẩm chay' : 'I only eat vegetarian food';
      case 'what_main_ingredients': return isVietnamese ? 'Nguyên liệu chính bạn muốn là gì?' : 'What main ingredients would you like?';
      case 'choose_ingredients_description': return isVietnamese ? 'Chọn một hoặc nhiều nguyên liệu, hoặc để AI tự gợi ý' : 'Choose one or more ingredients, or let AI suggest automatically';
      case 'or_choose_specific_ingredients': return isVietnamese ? 'Hoặc chọn nguyên liệu cụ thể:' : 'Or choose specific ingredients:';
      case 'let_ai_auto_suggest': return isVietnamese ? 'Để AI tự động gợi ý' : 'Let AI auto-suggest';
      case 'system_will_choose_best_ingredients': return isVietnamese ? 'Hệ thống sẽ chọn nguyên liệu phù hợp nhất' : 'System will choose the most suitable ingredients';
      case 'find_dishes': return isVietnamese ? 'Tìm món ăn' : 'Find Dishes';
      case 'please_select_meal_type': return isVietnamese ? 'Vui lòng chọn loại bữa ăn' : 'Please select meal type';

      // Home Screen Greetings
      case 'good_morning': return isVietnamese ? 'Chào buổi sáng' : 'Good morning';
      case 'good_afternoon': return isVietnamese ? 'Chào buổi chiều' : 'Good afternoon';
      case 'good_evening': return isVietnamese ? 'Chào buổi tối' : 'Good evening';
      case 'morning_message': return isVietnamese ? 'Hãy bắt đầu ngày mới với một bữa sáng ngon miệng!' : 'Start your new day with a delicious breakfast!';
      case 'afternoon_message': return isVietnamese ? 'Đã đến giờ nghỉ trưa rồi, bạn muốn ăn gì?' : 'It\'s lunch time, what would you like to eat?';
      case 'evening_message': return isVietnamese ? 'Thời gian cho bữa tối ấm cúng cùng \n gia đình!' : 'Time for a cozy dinner with \n family!';
      case 'quick_access': return isVietnamese ? 'Truy cập nhanh' : 'Quick Access';
      case 'saved_dishes': return isVietnamese ? 'Món ăn đã lưu' : 'Saved Dishes';
      case 'view_meal_plan': return isVietnamese ? 'Xem kế hoạch bữa ăn' : 'View meal plan';
      case 'customize_app': return isVietnamese ? 'Tùy chỉnh ứng dụng' : 'Customize app';
      case 'personal_info': return isVietnamese ? 'Thông tin cá nhân' : 'Personal information';
      case 'ai_will_help_you_find': return isVietnamese ? 'AI sẽ giúp bạn tìm món ăn phù hợp' : 'AI will help you find suitable dishes';
      case 'personal_information': return isVietnamese ? 'Thông tin cá nhân' : 'Personal Information';

      // Validation
      case 'please_select_language': return isVietnamese ? 'Vui lòng chọn ngôn ngữ' : 'Please select a language';
      case 'please_enter_name': return isVietnamese ? 'Vui lòng nhập tên của bạn' : 'Please enter your name';
      case 'please_select_gender': return isVietnamese ? 'Vui lòng chọn giới tính' : 'Please select your gender';
      case 'please_select_cooking_style': return isVietnamese ? 'Vui lòng chọn xu hướng nấu ăn' : 'Please select your cooking style';

      // Weekly Menu
      case 'today': return isVietnamese ? 'Hôm nay' : 'Today';
      case 'no_meals_for_this_day': return isVietnamese ? 'Chưa có món ăn nào cho ngày này' : 'No meals for this day yet';
      case 'add': return isVietnamese ? 'Thêm' : 'Add';

      // Settings
      case 'account': return isVietnamese ? 'Tài khoản' : 'Account';
      case 'sign_out': return isVietnamese ? 'Đăng xuất' : 'Sign Out';
      case 'view_edit_account_info': return isVietnamese ? 'Xem và chỉnh sửa thông tin tài khoản' : 'View and edit account information';
      case 'sign_out_from_current_account': return isVietnamese ? 'Đăng xuất khỏi tài khoản hiện tại' : 'Sign out from current account';
      case 'interface_mode': return isVietnamese ? 'Chế độ giao diện' : 'Interface Mode';
      case 'language': return isVietnamese ? 'Ngôn ngữ' : 'Language';
      case 'dark_mode': return isVietnamese ? 'Chế độ tối' : 'Dark Mode';
      case 'enable_disable_dark_mode': return isVietnamese ? 'Bật/tắt giao diện tối' : 'Enable/disable dark interface';
      case 'primary_colors': return isVietnamese ? 'Màu sắc chính' : 'Primary Colors';
      case 'secondary_colors': return isVietnamese ? 'Màu sắc phụ' : 'Secondary Colors';
      case 'meal_colors': return isVietnamese ? 'Màu sắc bữa ăn' : 'Meal Colors';
      case 'breakfast': return isVietnamese ? 'Bữa sáng' : 'Breakfast';
      case 'lunch': return isVietnamese ? 'Bữa trưa' : 'Lunch';
      case 'dinner': return isVietnamese ? 'Bữa tối' : 'Dinner';
      case 'data_management': return isVietnamese ? 'Quản lý dữ liệu' : 'Data Management';
      case 'clear_cache_refresh_data': return isVietnamese ? 'Xóa cache và làm mới dữ liệu' : 'Clear cache and refresh data';
      case 'update_all_dishes_from_source': return isVietnamese ? 'Cập nhật lại tất cả món ăn và thực đơn từ dữ liệu gốc' : 'Update all dishes and menus from source data';
      case 'app_info': return isVietnamese ? 'Thông tin ứng dụng' : 'App Information';
      case 'about_app': return isVietnamese ? 'Về ứng dụng' : 'About App';
      case 'about_app_description': return isVietnamese ? 'Thông tin về ứng dụng CookSpark AI' : 'Information about CookSpark AI app';
      case 'author_info': return isVietnamese ? 'Thông tin tác giả' : 'Author Information';
      case 'author_info_description': return isVietnamese ? 'Thông tin về Nguyễn Minh Đức' : 'Information about Nguyen Minh Duc';
      case 'app_feedback': return isVietnamese ? 'Góp ý ứng dụng' : 'App Feedback';
      case 'share_feedback_to_improve': return isVietnamese ? 'Chia sẻ ý kiến để cải thiện ứng dụng' : 'Share feedback to improve the app';
      case 'reset_to_default_interface': return isVietnamese ? 'Đặt lại giao diện mặc định' : 'Reset to default interface';
      case 'clear_interface_cache': return isVietnamese ? 'Xóa cache giao diện' : 'Clear interface cache';
      case 'synchronize_data': return isVietnamese ? 'Đồng bộ hóa dữ liệu' : 'Synchronize Data';
      case 'sync_data_between_devices': return isVietnamese ? 'Đồng bộ dữ liệu món ăn của bạn giữa các thiết bị và lưu trữ đám mây' : 'Sync your meal data between devices and cloud storage';
      case 'sync_now': return isVietnamese ? 'Đồng bộ ngay' : 'Sync Now';

      // Dialog Messages
      case 'choose_language_dialog': return isVietnamese ? 'Chọn ngôn ngữ' : 'Choose Language';
      case 'confirm_sign_out': return isVietnamese ? 'Đăng xuất' : 'Sign Out';
      case 'confirm_sign_out_message': return isVietnamese ? 'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?' : 'Are you sure you want to sign out of your account?';
      case 'confirm_clear_cache': return isVietnamese ? 'Xóa cache?' : 'Clear cache?';
      case 'confirm_clear_cache_message': return isVietnamese
          ? 'Thao tác này sẽ xóa tất cả dữ liệu cache và tải lại thực đơn từ dữ liệu mới nhất. Các món ăn tự tạo sẽ bị mất. Bạn có chắc chắn muốn tiếp tục?'
          : 'This operation will clear all cache data and reload menus from the latest data. Custom dishes will be lost. Are you sure you want to continue?';
      case 'confirm_clear_interface_cache': return isVietnamese ? 'Xóa cache giao diện' : 'Clear Interface Cache';
      case 'confirm_clear_interface_cache_message': return isVietnamese
          ? 'Thao tác này sẽ xóa tất cả cài đặt giao diện và khởi động lại ứng dụng. Bạn có chắc chắn muốn tiếp tục?'
          : 'This operation will clear all interface settings and restart the app. Are you sure you want to continue?';

      // Notification Messages
      case 'sign_out_success': return isVietnamese ? 'Đăng xuất thành công' : 'Successfully signed out';
      case 'sign_out_error': return isVietnamese ? 'Đã đăng xuất (có lỗi nhỏ' : 'Signed out (with minor error';
      case 'clear_cache_success': return isVietnamese ? 'Đã xóa cache và làm mới dữ liệu thành công' : 'Successfully cleared cache and refreshed data';
      case 'clear_cache_error': return isVietnamese ? 'Lỗi khi xóa cache' : 'Error clearing cache';
      case 'reset_to_default_success': return isVietnamese ? 'Đã đặt lại về cài đặt mặc định' : 'Reset to default settings';
      case 'clear_interface_cache_success': return isVietnamese ? 'Đã xóa cache giao diện. Vui lòng khởi động lại ứng dụng.' : 'Interface cache cleared. Please restart the app.';
      case 'sync_optimized': return isVietnamese ? 'Chức năng đồng bộ đã được tối ưu hóa' : 'Sync function has been optimized';
      case 'cannot_open_link': return isVietnamese ? 'Không thể mở liên kết' : 'Cannot open link';

      // Fallback Names
      case 'you': return isVietnamese ? 'bạn' : 'you';
      case 'user': return isVietnamese ? 'Người dùng' : 'User';
      case 'brother': return isVietnamese ? 'anh ' : '';
      case 'sister': return isVietnamese ? 'chị ' : '';

      // Weekly Menu Additional
      case 'error_occurred': return isVietnamese ? 'Có lỗi xảy ra' : 'An error occurred';
      case 'cannot_load_menu': return isVietnamese ? 'Không thể tải thực đơn' : 'Cannot load menu';
      case 'try_again': return isVietnamese ? 'Thử lại' : 'Try Again';
      case 'week_menu_empty': return isVietnamese ? 'Thực đơn tuần này còn trống' : 'This week\'s menu is empty';
      case 'add_dishes_to_menu': return isVietnamese ? 'Hãy thêm món ăn vào thực đơn của bạn' : 'Add dishes to your menu';
      case 'add_dish': return isVietnamese ? 'Thêm món ăn' : 'Add Dish';
      case 'total_dishes': return isVietnamese ? 'Tổng món' : 'Total Dishes';
      case 'eaten': return isVietnamese ? 'Đã ăn' : 'Eaten';
      case 'completion': return isVietnamese ? 'Hoàn thành' : 'Completion';
      case 'error_loading_menu': return isVietnamese ? 'Lỗi khi tải thực đơn' : 'Error loading menu';
      case 'error_updating': return isVietnamese ? 'Lỗi khi cập nhật' : 'Error updating';

      // Settings Additional
      case 'personal_information_settings': return isVietnamese ? 'Thông tin cá nhân' : 'Personal Information';
      case 'sign_out_from_account': return isVietnamese ? 'Đăng xuất khỏi tài khoản hiện tại' : 'Sign out from current account';
      case 'confirm_sign_out_title': return isVietnamese ? 'Đăng xuất' : 'Sign Out';
      case 'sign_out_success_message': return isVietnamese ? 'Đăng xuất thành công' : 'Successfully signed out';
      case 'sign_out_error_message': return isVietnamese ? 'Đã đăng xuất (có lỗi nhỏ' : 'Signed out (with minor error';
      case 'reset_to_default_success_message': return isVietnamese ? 'Đã đặt lại về cài đặt mặc định' : 'Reset to default settings';
      case 'clear_interface_cache_success_message': return isVietnamese ? 'Đã xóa cache giao diện. Vui lòng khởi động lại ứng dụng.' : 'Interface cache cleared. Please restart the app.';
      case 'sync_optimized_message': return isVietnamese ? 'Chức năng đồng bộ đã được tối ưu hóa' : 'Sync function has been optimized';
      case 'cannot_open_link_message': return isVietnamese ? 'Không thể mở liên kết' : 'Cannot open link';
      case 'clear_cache_success_message': return isVietnamese ? 'Đã xóa cache và làm mới dữ liệu thành công' : 'Successfully cleared cache and refreshed data';
      case 'clear_cache_error_message': return isVietnamese ? 'Lỗi khi xóa cache' : 'Error clearing cache';

      // Authentication
      case 'login_title': return isVietnamese ? 'Đăng Nhập' : 'Login';
      case 'welcome_back': return isVietnamese ? 'Chào mừng bạn quay trở lại ứng dụng' : 'Welcome back to the app';
      case 'email_address': return isVietnamese ? 'Địa chỉ email' : 'Email address';
      case 'password_field': return isVietnamese ? 'Mật khẩu' : 'Password';
      case 'forgot_password_link': return isVietnamese ? 'Quên mật khẩu?' : 'Forgot password?';
      case 'remember_login': return isVietnamese ? 'Ghi nhớ đăng nhập' : 'Remember login';
      case 'or_login_with': return isVietnamese ? 'hoặc đăng nhập với' : 'or login with';
      case 'continue_with_google': return isVietnamese ? 'Tiếp tục với Google' : 'Continue with Google';
      case 'dont_have_account': return isVietnamese ? 'Chưa có tài khoản? ' : 'Don\'t have an account? ';
      case 'create_account_link': return isVietnamese ? 'Tạo tài khoản' : 'Create account';
      case 'please_enter_email': return isVietnamese ? 'Vui lòng nhập email' : 'Please enter email';
      case 'invalid_email': return isVietnamese ? 'Email không hợp lệ' : 'Invalid email';
      case 'please_enter_password': return isVietnamese ? 'Vui lòng nhập mật khẩu' : 'Please enter password';
      case 'password_min_length': return isVietnamese ? 'Mật khẩu phải có ít nhất 6 ký tự' : 'Password must be at least 6 characters';
      case 'login_failed': return isVietnamese ? 'Đăng nhập thất bại' : 'Login failed';
      case 'error_occurred_try_again': return isVietnamese ? 'Đã xảy ra lỗi. Vui lòng thử lại sau.' : 'An error occurred. Please try again later.';
      case 'opening_google_login': return isVietnamese ? 'Đang mở cửa sổ đăng nhập Google...' : 'Opening Google login window...';
      case 'login_failed_try_again': return isVietnamese ? 'Đăng nhập thất bại. Vui lòng thử lại sau.' : 'Login failed. Please try again later.';
      case 'login_cancelled': return isVietnamese ? 'Đăng nhập đã bị hủy. Vui lòng thử lại.' : 'Login was cancelled. Please try again.';
      case 'try_again_button': return isVietnamese ? 'Thử lại' : 'Try again';
      case 'google_login_error': return isVietnamese ? 'Đã xảy ra lỗi khi đăng nhập với Google. Vui lòng thử lại sau.' : 'An error occurred when logging in with Google. Please try again later.';

      // Register Screen
      case 'create_new_account': return isVietnamese ? 'Tạo tài khoản mới' : 'Create New Account';
      case 'otp_sent_to_email': return isVietnamese ? 'Mã OTP đã được gửi đến email của bạn!' : 'OTP code has been sent to your email!';
      case 'unknown_error_occurred': return isVietnamese ? 'Đã xảy ra lỗi không xác định' : 'An unknown error occurred';
      case 'error_occurred_try_again_later': return isVietnamese ? 'Đã xảy ra lỗi. Vui lòng thử lại sau.' : 'An error occurred. Please try again later.';
      case 'google_login_successful': return isVietnamese ? 'Đăng nhập Google thành công, chuyển màn hình' : 'Google login successful, navigating to home';
      case 'google_login_failed': return isVietnamese ? 'Đăng nhập bằng Google thất bại' : 'Google login failed';
      case 'google_login_error_occurred': return isVietnamese ? 'Đã xảy ra lỗi khi đăng nhập với Google. Vui lòng thử lại sau.' : 'An error occurred when logging in with Google. Please try again later.';
      case 'enter_your_email': return isVietnamese ? 'Nhập email của bạn' : 'Enter your email';
      case 'otp_verification_info': return isVietnamese ? 'Chúng tôi sẽ gửi mã OTP đến email của bạn để xác minh tài khoản.' : 'We will send an OTP code to your email to verify your account.';
      case 'send_otp_code': return isVietnamese ? 'GỬI MÃ OTP' : 'SEND OTP CODE';
      case 'or_text': return isVietnamese ? 'HOẶC' : 'OR';
      case 'sign_in_with_google': return isVietnamese ? 'Đăng nhập với Google' : 'Sign in with Google';
      case 'already_have_account': return isVietnamese ? 'Đã có tài khoản? ' : 'Already have an account? ';
      case 'you_can': return isVietnamese ? 'Bạn có thể:' : 'You can:';
      case 'forgot_password_short': return isVietnamese ? 'Quên MK?' : 'Forgot PW?';
      case 'please_wait_seconds': return isVietnamese ? 'Vui lòng đợi' : 'Please wait';
      case 'percent_complete': return isVietnamese ? 'hoàn thành' : 'complete';
      case 'please_wait_before_retry': return isVietnamese ? 'Vui lòng đợi' : 'Please wait';

      // Feature Development Notifications
      case 'feature_in_development': return isVietnamese ? 'Tính năng đang\u00A0phát\u00A0triển' : 'Feature in\u00A0Development';
      case 'weekly_menu_coming_soon': return isVietnamese ? 'Tính năng thực đơn tuần đang được\u00A0phát\u00A0triển và sẽ sớm có mặt trong phiên bản tiếp theo!' : 'Weekly menu feature is under\u00A0development and will be available in the next version!';
      case 'add_to_weekly_menu_coming_soon': return isVietnamese ? 'Tính năng thêm vào thực đơn tuần đang được\u00A0phát\u00A0triển. Hãy theo dõi để cập nhật sớm nhất!' : 'Add to weekly menu feature is under\u00A0development. Stay tuned for updates!';
      case 'stay_tuned': return isVietnamese ? 'Hãy theo dõi để cập nhật!' : 'Stay tuned for updates!';
      case 'understood': return isVietnamese ? 'Đã hiểu' : 'Understood';

      // Edit Profile Screen
      case 'editProfile': return isVietnamese ? 'Chỉnh sửa thông tin' : 'Edit Profile';
      case 'basicInformation': return isVietnamese ? 'Thông tin cơ bản' : 'Basic Information';
      case 'displayName': return isVietnamese ? 'Tên hiển thị' : 'Display Name';
      case 'pleaseEnterDisplayName': return isVietnamese ? 'Vui lòng nhập tên hiển thị' : 'Please enter display name';
      case 'selectLanguage': return isVietnamese ? 'Chọn ngôn ngữ' : 'Select Language';
      case 'selectGender': return isVietnamese ? 'Chọn giới tính' : 'Select Gender';
      case 'cookingPreference': return isVietnamese ? 'Sở thích nấu ăn' : 'Cooking Preference';
      case 'selectCookingPreference': return isVietnamese ? 'Chọn sở thích nấu ăn' : 'Select Cooking Preference';
      case 'dietaryPreferences': return isVietnamese ? 'Sở thích ăn uống' : 'Dietary Preferences';
      case 'vegetarianDescription': return isVietnamese ? 'Tôi ăn chay' : 'I am vegetarian';

      // Welcome Dialog
      case 'welcome_to_app': return isVietnamese ? 'Chào mừng đến với CookSpark!' : 'Welcome to CookSpark!';
      case 'upcoming_features': return isVietnamese ? 'Tính năng sắp có' : 'Upcoming Features';
      case 'upcoming_features_description': return isVietnamese ? 'Chúng tôi đang\u00A0phát\u00A0triển nhiều tính năng thú vị:\n\n• Thực đơn tuần cá\u00A0nhân\u00A0hóa\n• Lưu và quản lý công thức\n• Gợi ý món ăn thông minh\n• Chia sẻ với bạn bè\n\nHãy theo dõi để cập nhật sớm nhất!' : 'We are developing many exciting features:\n\n• Personalized weekly menu\n• Save and manage recipes\n• Smart meal\u00A0suggestions\n• Share with friends\n\nStay tuned for updates!';
      case 'dont_show_again': return isVietnamese ? 'Không hiện lại' : 'Don\'t show again';
      case 'got_it': return isVietnamese ? 'Đã hiểu' : 'Got it';

      // Delete Dialog
      case 'delete_video_saved': return isVietnamese ? 'Xóa video đã lưu' : 'Delete saved video';
      case 'confirm_delete_video': return isVietnamese ? 'Bạn có chắc muốn xóa video này khỏi danh sách yêu thích?\n\nBạn có thể hoàn tác sau khi xóa.' : 'Are you sure you want to delete this video from your favorites?\n\nYou can undo after deletion.';
      case 'delete_now': return isVietnamese ? 'Xóa ngay' : 'Delete now';
      case 'cancel_action': return isVietnamese ? 'Hủy bỏ' : 'Cancel';
      case 'delete_all_videos': return isVietnamese ? 'Xóa tất cả video' : 'Delete all videos';
      case 'confirm_delete_all_videos': return isVietnamese ? 'Bạn có chắc muốn xóa tất cả video đã lưu?\n\nHành động này không thể hoàn tác.' : 'Are you sure you want to delete all saved videos?\n\nThis action cannot be undone.';
      case 'delete_all': return isVietnamese ? 'Xóa tất cả' : 'Delete all';
      case 'logout_dialog': return isVietnamese ? 'Đăng xuất' : 'Logout';
      case 'confirm_logout': return isVietnamese ? 'Bạn có chắc muốn đăng xuất khỏi tài khoản?\n\nDữ liệu đã lưu sẽ được giữ nguyên.' : 'Are you sure you want to logout from your account?\n\nSaved data will be preserved.';
      case 'stay_logged_in': return isVietnamese ? 'Ở lại' : 'Stay logged in';
      case 'delete_data': return isVietnamese ? 'Xóa dữ liệu' : 'Delete data';
      case 'confirm_delete_data': return isVietnamese ? 'Bạn có chắc muốn xóa tất cả dữ liệu ứng dụng?\n\nHành động này không thể hoàn tác.' : 'Are you sure you want to delete all app data?\n\nThis action cannot be undone.';
      case 'delete_data_action': return isVietnamese ? 'Xóa dữ liệu' : 'Delete data';

      // Notification Messages
      case 'view_list': return isVietnamese ? 'Xem danh sách' : 'View list';
      case 'close': return isVietnamese ? 'Đóng' : 'Close';
      case 'undo': return isVietnamese ? 'Hoàn tác' : 'Undo';
      case 'saved_successfully': return isVietnamese ? 'Đã lưu thành công!' : 'Saved successfully!';
      case 'save_error': return isVietnamese ? 'Lỗi lưu video' : 'Save error';
      case 'deleted_successfully': return isVietnamese ? 'Đã xóa thành công!' : 'Deleted successfully!';
      case 'delete_error': return isVietnamese ? 'Lỗi xóa video' : 'Delete error';

      // Date and Time
      case 'just_now': return isVietnamese ? 'Vừa xong' : 'Just now';
      case 'yesterday': return isVietnamese ? 'Hôm qua' : 'Yesterday';
      case 'tomorrow': return isVietnamese ? 'Ngày mai' : 'Tomorrow';

      // Saved Videos Screen
      case 'saved_dishes_title': return isVietnamese ? 'Món ăn đã lưu' : 'Saved Dishes';
      case 'no_saved_dishes': return isVietnamese ? 'Chưa có món ăn nào được lưu' : 'No saved dishes yet';
      case 'find_and_save_favorites': return isVietnamese ? 'Hãy tìm kiếm và lưu những món ăn yêu thích của bạn!' : 'Find and save your favorite dishes!';
      case 'search_for_dishes': return isVietnamese ? 'Tìm kiếm món ăn' : 'Search for dishes';
      case 'saved_on': return isVietnamese ? 'Đã lưu' : 'Saved on';
      case 'deleting_video': return isVietnamese ? 'Đang xóa video' : 'Deleting video';
      case 'video_deleted_from_favorites': return isVietnamese ? 'đã được xóa khỏi danh sách yêu thích' : 'has been removed from favorites';
      case 'cannot_delete_video': return isVietnamese ? 'Không thể xóa video. Vui lòng kiểm tra kết nối mạng và thử lại.' : 'Cannot delete video. Please check your network connection and try again.';
      case 'restoring_video': return isVietnamese ? 'Đang khôi phục video' : 'Restoring video';
      case 'video_restored': return isVietnamese ? 'đã được khôi phục' : 'has been restored';
      case 'cannot_restore_video': return isVietnamese ? 'Không thể khôi phục video. Vui lòng thử lại.' : 'Cannot restore video. Please try again.';

      default: return key; // Fallback to key if not found
    }
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['vi', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
