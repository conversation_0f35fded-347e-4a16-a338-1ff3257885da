import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Core imports
import 'providers/theme_provider.dart';
import 'providers/user_profile_provider.dart';
import 'providers/language_provider.dart';
import 'utils/app_init.dart';
import 'widgets/auth_error_wrapper.dart';
import 'l10n/app_localizations.dart';

// Screen imports
import 'screens/auth_wrapper.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/forgot_password_otp_screen.dart';
import 'screens/reset_password_screen.dart';
import 'screens/otp_verification_screen.dart';
import 'screens/new_onboarding_screen.dart';
import 'screens/language_selection_screen.dart';
import 'screens/new_home_screen.dart';
import 'screens/meal_suggestion_screen.dart';
import 'screens/meal_suggestion_results_screen.dart';
import 'screens/youtube_player_screen.dart';
import 'screens/weekly_menu_screen.dart';
import 'screens/saved_videos_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/about_screen.dart';
import 'screens/author_screen.dart';
import 'screens/user_profile_screen.dart';
import 'screens/edit_profile_screen.dart';
import 'screens/debug_oauth_screen.dart';
import 'screens/test_components_screen.dart';


void main() async {
  // Khởi tạo ứng dụng với AppInit (chỉ khởi tạo Supabase, không load dishes)
  await AppInit.initialize();

  // Khởi tạo LanguageProvider
  final languageProvider = LanguageProvider();
  await languageProvider.initialize();

  // Vô hiệu hóa các cảnh báo OpenGL
  FlutterError.onError = (FlutterErrorDetails details) {
    final exception = details.exception;
    if (exception is String && exception.contains('OpenGL ES API')) {
      // Bỏ qua lỗi OpenGL
      print('Bỏ qua lỗi OpenGL ES API');
      return;
    }
    FlutterError.presentError(details);
  };

  // Không load dishes ở đây - sẽ load sau khi đăng nhập
  print('✅ App khởi tạo thành công, sẵn sàng hiển thị UI');

  // Đặt hướng màn hình
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (context) => ThemeProvider()),
      ChangeNotifierProvider(create: (context) => UserProfileProvider()),
      ChangeNotifierProvider.value(value: languageProvider),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, LanguageProvider>(
      builder: (context, themeProvider, languageProvider, child) {
        return MaterialApp(
          title: 'CookSpark AI',
          debugShowCheckedModeBanner: false,
          theme: themeProvider.themeData,
          themeMode: themeProvider.darkMode ? ThemeMode.dark : ThemeMode.light,

          // Localization support
          locale: languageProvider.currentLocale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: AppLocalizations.supportedLocales,
          home: AuthErrorWrapper(
            child: const AuthWrapper(),
            onAuthError: () {
              print('🔍 Main: Phát hiện lỗi authentication từ AuthErrorWrapper');
            },
          ),
          onGenerateRoute: (settings) {
            switch (settings.name) {
              case AuthWrapper.routeName:
                return MaterialPageRoute(builder: (_) => const AuthWrapper());
              case LanguageSelectionScreen.routeName:
                return MaterialPageRoute(builder: (_) => const LanguageSelectionScreen());
              case LoginScreen.routeName:
                return MaterialPageRoute(builder: (_) => const LoginScreen());
              case RegisterScreen.routeName:
                return MaterialPageRoute(builder: (_) => const RegisterScreen());
              case ForgotPasswordOtpScreen.routeName:
                return MaterialPageRoute(builder: (_) => const ForgotPasswordOtpScreen());
              case ResetPasswordScreen.routeName:
                final email = settings.arguments as String? ?? '';
                return MaterialPageRoute(builder: (_) => ResetPasswordScreen(email: email));
              case OtpVerificationScreen.routeName:
                final email = settings.arguments as String? ?? '';
                return MaterialPageRoute(builder: (_) => OtpVerificationScreen(email: email));
              case NewHomeScreen.routeName:
                return MaterialPageRoute(builder: (_) => const NewHomeScreen());
              case NewOnboardingScreen.routeName:
                return MaterialPageRoute(builder: (_) => const NewOnboardingScreen());
              case MealSuggestionScreen.routeName:
                return MaterialPageRoute(builder: (_) => const MealSuggestionScreen());
              case MealSuggestionResultsScreen.routeName:
                return MaterialPageRoute(
                  builder: (_) => const MealSuggestionResultsScreen(),
                  settings: settings,
                );
              case YouTubePlayerScreen.routeName:
                return MaterialPageRoute(
                  builder: (_) => const YouTubePlayerScreen(),
                  settings: settings,
                );
              case WeeklyMenuScreen.routeName:
                return MaterialPageRoute(builder: (_) => const WeeklyMenuScreen());
              case SavedVideosScreen.routeName:
                return MaterialPageRoute(builder: (_) => const SavedVideosScreen());
              case SettingsScreen.routeName:
                return MaterialPageRoute(builder: (_) => const SettingsScreen());
              case AboutScreen.routeName:
                return MaterialPageRoute(builder: (_) => const AboutScreen());
              case AuthorScreen.routeName:
                return MaterialPageRoute(builder: (_) => const AuthorScreen());

              case DebugOAuthScreen.routeName:
                return MaterialPageRoute(builder: (_) => const DebugOAuthScreen());
              case UserProfileScreen.routeName:
                return MaterialPageRoute(builder: (_) => const UserProfileScreen());
              case EditProfileScreen.routeName:
                return MaterialPageRoute(builder: (_) => const EditProfileScreen());

              case TestComponentsScreen.routeName:
                return MaterialPageRoute(builder: (_) => const TestComponentsScreen());
              default:
                // If the route is unknown, or it's a deep link callback,
                // just show the AuthWrapper and let it handle the auth state.
                return MaterialPageRoute(builder: (_) => const AuthWrapper());
            }
          },
        );
      },
    );
  }
}


