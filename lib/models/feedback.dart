/// Model cho feedback từ người dùng
class FeedbackModel {
  final String id;
  final String userId;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FeedbackModel({
    required this.id,
    required this.userId,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Tạo FeedbackModel từ JSON
  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Chuyển FeedbackModel thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content': content,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Tạo bản sao với một số thuộc tính được thay đổi
  FeedbackModel copyWith({
    String? id,
    String? userId,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FeedbackModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// So sánh hai FeedbackModel
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FeedbackModel &&
        other.id == id &&
        other.userId == userId &&
        other.content == content &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        content.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  @override
  String toString() {
    return 'FeedbackModel(id: $id, userId: $userId, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  /// Lấy preview ngắn của nội dung feedback
  String get preview {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }

  /// Kiểm tra xem feedback có được tạo hôm nay không
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final feedbackDate = DateTime(createdAt.year, createdAt.month, createdAt.day);
    return feedbackDate == today;
  }

  /// Lấy thời gian tương đối (ví dụ: "2 giờ trước")
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }
}
