import 'package:uuid/uuid.dart';
import '../services/youtube_service.dart';

/// Model cho video đã lưu của người dùng
class SavedVideo {
  final String id;
  final String userId;
  final YouTubeVideo video;
  final List<String> tags;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  SavedVideo({
    String? id,
    required this.userId,
    required this.video,
    this.tags = const [],
    this.notes = '',
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Tạo SavedVideo từ JSON (Supabase response)
  factory SavedVideo.fromJson(Map<String, dynamic> json) {
    return SavedVideo(
      id: json['id'],
      userId: json['user_id'],
      video: YouTubeVideo(
        id: json['video_id'],
        title: json['video_title'] ?? json['title'] ?? '',
        description: json['video_description'] ?? json['description'] ?? '',
        thumbnailUrl: json['video_thumbnail_url'] ?? json['thumbnail_url'] ?? '',
        channelTitle: json['video_channel_title'] ?? json['channel_name'] ?? '',
        duration: Duration(seconds: json['video_duration_seconds'] ?? 0),
        viewCount: json['video_view_count'] ?? 0,
        publishedAt: DateTime.tryParse(json['video_published_at'] ?? json['published_at'] ?? '') ?? DateTime.now(),
      ),
      tags: List<String>.from(json['tags'] ?? []),
      notes: json['notes'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Chuyển SavedVideo thành JSON để lưu vào Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'video_id': video.id,
      'video_title': video.title,
      'video_description': video.description,
      'video_thumbnail_url': video.thumbnailUrl,
      'video_channel_title': video.channelTitle,
      'video_duration_seconds': video.duration.inSeconds,
      'video_view_count': video.viewCount,
      'video_published_at': video.publishedAt.toIso8601String(),
      'tags': tags,
      // Tạm thời comment notes để tránh lỗi cache
      // 'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Tạo bản sao với thông tin cập nhật
  SavedVideo copyWith({
    String? id,
    String? userId,
    YouTubeVideo? video,
    List<String>? tags,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SavedVideo(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      video: video ?? this.video,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'SavedVideo(id: $id, userId: $userId, video: ${video.title}, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SavedVideo &&
        other.id == id &&
        other.userId == userId &&
        other.video.id == video.id;
  }

  @override
  int get hashCode {
    return id.hashCode ^ userId.hashCode ^ video.id.hashCode;
  }
}
