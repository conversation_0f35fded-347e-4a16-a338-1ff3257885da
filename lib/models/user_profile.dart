import 'package:uuid/uuid.dart';

/// Model cho thông tin profile người dùng trong hệ thống onboarding mới
class UserProfile {
  final String id;
  final String userId; // Supabase auth user ID
  final String? displayName;
  final String? email;
  final String? photoUrl;
  
  // Thông tin từ onboarding
  final String? language; // 'vi' hoặc 'en'
  final Gender? gender;
  final CookingPreference? cookingPreference;
  final List<String> favoriteIngredients;
  final List<String> dietaryRestrictions;
  final bool isVegetarian;
  
  // Metadata
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isOnboardingCompleted;

  UserProfile({
    String? id,
    required this.userId,
    this.displayName,
    this.email,
    this.photoUrl,
    this.language,
    this.gender,
    this.cookingPreference,
    this.favoriteIngredients = const [],
    this.dietaryRestrictions = const [],
    this.isVegetarian = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isOnboardingCompleted = false,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Tạo UserProfile từ JSON (Supabase response)
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      userId: json['user_id'],
      displayName: json['display_name'],
      email: json['email'],
      photoUrl: json['photo_url'],
      language: json['language'],
      gender: json['gender'] != null ? Gender.fromString(json['gender']) : null,
      cookingPreference: json['cooking_preference'] != null
          ? CookingPreference.fromString(json['cooking_preference'])
          : null,
      favoriteIngredients: List<String>.from(json['favorite_ingredients'] ?? []),
      dietaryRestrictions: List<String>.from(json['dietary_restrictions'] ?? []),
      isVegetarian: json['is_vegetarian'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isOnboardingCompleted: json['is_onboarding_completed'] ?? false,
    );
  }

  /// Chuyển UserProfile thành JSON để lưu vào Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'display_name': displayName,
      'email': email,
      'photo_url': photoUrl,
      'language': language,
      'gender': gender?.value,
      'cooking_preference': cookingPreference?.value,
      'favorite_ingredients': favoriteIngredients,
      'dietary_restrictions': dietaryRestrictions,
      'is_vegetarian': isVegetarian,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_onboarding_completed': isOnboardingCompleted,
    };
  }

  /// Tạo bản copy với các thay đổi
  UserProfile copyWith({
    String? displayName,
    String? email,
    String? photoUrl,
    String? language,
    Gender? gender,
    CookingPreference? cookingPreference,
    List<String>? favoriteIngredients,
    List<String>? dietaryRestrictions,
    bool? isVegetarian,
    bool? isOnboardingCompleted,
  }) {
    return UserProfile(
      id: id,
      userId: userId,
      displayName: displayName ?? this.displayName,
      email: email ?? this.email,
      photoUrl: photoUrl ?? this.photoUrl,
      language: language ?? this.language,
      gender: gender ?? this.gender,
      cookingPreference: cookingPreference ?? this.cookingPreference,
      favoriteIngredients: favoriteIngredients ?? this.favoriteIngredients,
      dietaryRestrictions: dietaryRestrictions ?? this.dietaryRestrictions,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      isOnboardingCompleted: isOnboardingCompleted ?? this.isOnboardingCompleted,
    );
  }
}

/// Enum cho giới tính
enum Gender {
  male('male', 'Nam'),
  female('female', 'Nữ'),
  other('other', 'Khác');

  const Gender(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static Gender fromString(String value) {
    return Gender.values.firstWhere(
      (gender) => gender.value == value,
      orElse: () => Gender.other,
    );
  }
}

/// Enum cho xu hướng nấu ăn
enum CookingPreference {
  simple('simple', 'Đơn giản', 'Tôi thích những món ăn dễ làm, ít nguyên liệu'),
  elaborate('elaborate', 'Cầu kỳ', 'Tôi thích thử nghiệm những món ăn phức tạp, đa dạng');

  const CookingPreference(this.value, this.displayName, this.description);
  
  final String value;
  final String displayName;
  final String description;

  static CookingPreference fromString(String value) {
    return CookingPreference.values.firstWhere(
      (preference) => preference.value == value,
      orElse: () => CookingPreference.simple,
    );
  }
}

/// Enum cho các nguyên liệu chính
enum MainIngredient {
  pork('pork', 'Thịt lợn', '🐷'),
  chicken('chicken', 'Thịt gà', '🐔'),
  beef('beef', 'Thịt bò', '🐄'),
  fish('fish', 'Cá', '🐟'),
  seafood('seafood', 'Hải sản', '🦐'),
  vegetables('vegetables', 'Rau củ', '🥬'),
  tofu('tofu', 'Đậu phụ', '🧈'),
  eggs('eggs', 'Trứng', '🥚'),
  noodles('noodles', 'Mì/Bún/Phở', '🍜'),
  rice('rice', 'Cơm', '🍚');

  const MainIngredient(this.value, this.displayName, this.emoji);
  
  final String value;
  final String displayName;
  final String emoji;

  static MainIngredient fromString(String value) {
    return MainIngredient.values.firstWhere(
      (ingredient) => ingredient.value == value,
      orElse: () => MainIngredient.vegetables,
    );
  }
}

/// Enum cho loại bữa ăn
enum MealType {
  breakfast('breakfast', 'Bữa sáng', '🌅'),
  lunch('lunch', 'Bữa trưa', '☀️'),
  dinner('dinner', 'Bữa tối', '🌙');

  const MealType(this.value, this.displayName, this.emoji);

  final String value;
  final String displayName;
  final String emoji;

  static MealType fromString(String value) {
    return MealType.values.firstWhere(
      (mealType) => mealType.value == value,
      orElse: () => MealType.lunch,
    );
  }
}

/// Model cho câu hỏi gợi ý món ăn
class MealSuggestionQuery {
  final MealType mealType;
  final bool isVegetarian;
  final List<MainIngredient> preferredIngredients;
  final bool autoSuggest;

  MealSuggestionQuery({
    required this.mealType,
    this.isVegetarian = false,
    this.preferredIngredients = const [],
    this.autoSuggest = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'meal_type': mealType.value,
      'is_vegetarian': isVegetarian,
      'preferred_ingredients': preferredIngredients.map((e) => e.value).toList(),
      'auto_suggest': autoSuggest,
    };
  }

  /// Tạo search query cho YouTube API
  String toYouTubeSearchQuery() {
    String query = 'cách nấu ${mealType.displayName}';

    if (isVegetarian) {
      query += ' chay';
    }

    if (preferredIngredients.isNotEmpty && !autoSuggest) {
      final ingredients = preferredIngredients.map((e) => e.displayName).join(' ');
      query += ' $ingredients';
    }

    query += ' đơn giản ngon';

    return query;
  }
}
