import 'package:uuid/uuid.dart';
import '../services/youtube_service.dart';
import 'user_profile.dart';

/// Model cho món ăn trong thực đơn tuần
class WeeklyMenuItem {
  final String id;
  final String userId;
  final DateTime date;
  final MealType mealType;
  final YouTubeVideo video;
  final String notes;
  final bool isCompleted; // Đã ăn hay chưa
  final DateTime createdAt;
  final DateTime updatedAt;

  WeeklyMenuItem({
    String? id,
    required this.userId,
    required this.date,
    required this.mealType,
    required this.video,
    this.notes = '',
    this.isCompleted = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Tạo WeeklyMenuItem từ JSON (Supabase response)
  factory WeeklyMenuItem.fromJson(Map<String, dynamic> json) {
    return WeeklyMenuItem(
      id: json['id'],
      userId: json['user_id'],
      date: DateTime.parse(json['date']),
      mealType: MealType.fromString(json['meal_type']),
      video: YouTubeVideo(
        id: json['video_id'],
        title: json['video_title'],
        description: json['video_description'] ?? '',
        thumbnailUrl: json['video_thumbnail_url'] ?? '',
        channelTitle: json['video_channel_title'] ?? '',
        duration: Duration(seconds: json['video_duration_seconds'] ?? 0),
        viewCount: json['video_view_count'] ?? 0,
        publishedAt: DateTime.tryParse(json['video_published_at'] ?? '') ?? DateTime.now(),
      ),
      notes: json['notes'] ?? '',
      isCompleted: json['is_completed'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Chuyển WeeklyMenuItem thành JSON để lưu vào Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String().split('T')[0], // Chỉ lấy phần ngày
      'meal_type': mealType.value,
      'video_id': video.id,
      'video_title': video.title,
      'video_description': video.description,
      'video_thumbnail_url': video.thumbnailUrl,
      'video_channel_title': video.channelTitle,
      'video_duration_seconds': video.duration.inSeconds,
      'video_view_count': video.viewCount,
      'video_published_at': video.publishedAt.toIso8601String(),
      'notes': notes,
      'is_completed': isCompleted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Tạo bản copy với các thay đổi
  WeeklyMenuItem copyWith({
    DateTime? date,
    MealType? mealType,
    YouTubeVideo? video,
    String? notes,
    bool? isCompleted,
  }) {
    return WeeklyMenuItem(
      id: id,
      userId: userId,
      date: date ?? this.date,
      mealType: mealType ?? this.mealType,
      video: video ?? this.video,
      notes: notes ?? this.notes,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  /// Lấy key để group theo ngày và loại bữa ăn
  String get dateKey => '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  String get mealKey => '${dateKey}_${mealType.value}';
}

/// Model cho thực đơn tuần
class WeeklyMenu {
  final String id;
  final String userId;
  final DateTime weekStartDate;
  final List<WeeklyMenuItem> items;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;

  WeeklyMenu({
    String? id,
    required this.userId,
    required this.weekStartDate,
    this.items = const [],
    this.name = '',
    this.description = '',
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Lấy ngày kết thúc tuần
  DateTime get weekEndDate => weekStartDate.add(const Duration(days: 6));

  /// Lấy tên tuần
  String get weekName {
    final start = weekStartDate;
    final end = weekEndDate;
    return 'Tuần ${start.day}/${start.month} - ${end.day}/${end.month}';
  }

  /// Lấy items theo ngày
  List<WeeklyMenuItem> getItemsForDate(DateTime date) {
    final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return items.where((item) => item.dateKey == dateKey).toList();
  }

  /// Lấy item cho ngày và loại bữa ăn cụ thể
  WeeklyMenuItem? getItemForMeal(DateTime date, MealType mealType) {
    final mealKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}_${mealType.value}';
    try {
      return items.firstWhere((item) => item.mealKey == mealKey);
    } catch (e) {
      return null;
    }
  }

  /// Kiểm tra xem có món ăn nào cho ngày cụ thể không
  bool hasItemsForDate(DateTime date) {
    return getItemsForDate(date).isNotEmpty;
  }

  /// Lấy tổng số món ăn đã hoàn thành
  int get completedItemsCount {
    return items.where((item) => item.isCompleted).length;
  }

  /// Lấy tỷ lệ hoàn thành (%)
  double get completionPercentage {
    if (items.isEmpty) return 0.0;
    return (completedItemsCount / items.length) * 100;
  }

  /// Lấy danh sách 7 ngày trong tuần
  List<DateTime> get weekDays {
    return List.generate(7, (index) => weekStartDate.add(Duration(days: index)));
  }

  /// Tạo bản copy với items mới
  WeeklyMenu copyWith({
    List<WeeklyMenuItem>? items,
    String? name,
    String? description,
  }) {
    return WeeklyMenu(
      id: id,
      userId: userId,
      weekStartDate: weekStartDate,
      items: items ?? this.items,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  /// Thêm item mới
  WeeklyMenu addItem(WeeklyMenuItem item) {
    final newItems = List<WeeklyMenuItem>.from(items);
    
    // Xóa item cũ nếu có (cùng ngày và loại bữa ăn)
    newItems.removeWhere((existingItem) => 
        existingItem.dateKey == item.dateKey && 
        existingItem.mealType == item.mealType);
    
    newItems.add(item);
    return copyWith(items: newItems);
  }

  /// Xóa item
  WeeklyMenu removeItem(String itemId) {
    final newItems = items.where((item) => item.id != itemId).toList();
    return copyWith(items: newItems);
  }

  /// Cập nhật item
  WeeklyMenu updateItem(WeeklyMenuItem updatedItem) {
    final newItems = items.map((item) {
      return item.id == updatedItem.id ? updatedItem : item;
    }).toList();
    return copyWith(items: newItems);
  }

  /// Đánh dấu item đã hoàn thành
  WeeklyMenu markItemCompleted(String itemId, bool isCompleted) {
    final newItems = items.map((item) {
      return item.id == itemId ? item.copyWith(isCompleted: isCompleted) : item;
    }).toList();
    return copyWith(items: newItems);
  }

  /// Lấy thống kê thực đơn
  Map<String, dynamic> getStatistics() {
    final totalItems = items.length;
    final completedItems = completedItemsCount;
    final pendingItems = totalItems - completedItems;
    
    // Thống kê theo loại bữa ăn
    final breakfastCount = items.where((item) => item.mealType == MealType.breakfast).length;
    final lunchCount = items.where((item) => item.mealType == MealType.lunch).length;
    final dinnerCount = items.where((item) => item.mealType == MealType.dinner).length;
    
    return {
      'total_items': totalItems,
      'completed_items': completedItems,
      'pending_items': pendingItems,
      'completion_percentage': completionPercentage,
      'breakfast_count': breakfastCount,
      'lunch_count': lunchCount,
      'dinner_count': dinnerCount,
      'week_name': weekName,
    };
  }
}

/// Utility class để làm việc với tuần
class WeekUtils {
  /// Lấy ngày đầu tuần (Thứ 2)
  static DateTime getWeekStartDate(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  /// Lấy ngày cuối tuần (Chủ nhật)
  static DateTime getWeekEndDate(DateTime date) {
    final weekStartDate = getWeekStartDate(date);
    return weekStartDate.add(const Duration(days: 6));
  }

  /// Kiểm tra xem hai ngày có cùng tuần không
  static bool isSameWeek(DateTime date1, DateTime date2) {
    final week1Start = getWeekStartDate(date1);
    final week2Start = getWeekStartDate(date2);
    return week1Start.isAtSameMomentAs(week2Start);
  }

  /// Lấy tên ngày trong tuần
  static String getDayName(DateTime date) {
    const dayNames = [
      'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'
    ];
    return dayNames[date.weekday - 1];
  }

  /// Lấy tên ngày ngắn
  static String getShortDayName(DateTime date) {
    const shortDayNames = [
      'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'
    ];
    return shortDayNames[date.weekday - 1];
  }
}
