import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

/// Provider quản lý ngôn ngữ ứng dụng
/// Hỗ trợ Tiếng Việt (vi) và English (en)
class LanguageProvider extends ChangeNotifier {
  static const String _languageKey = 'app_language';
  static const String _languageSelectedKey = 'language_selected_before';
  static const String _defaultLanguage = 'en'; // Mặc định English cho first-time

  Locale _currentLocale = const Locale(_defaultLanguage);
  bool _hasSelectedLanguageBefore = false;
  
  /// Ngôn ngữ hiện tại
  Locale get currentLocale => _currentLocale;

  /// Kiểm tra đã chọn ngôn ngữ trước đó chưa
  bool get hasSelectedLanguageBefore => _hasSelectedLanguageBefore;
  
  /// Mã ngôn ngữ hiện tại (vi, en)
  String get currentLanguageCode => _currentLocale.languageCode;
  
  /// <PERSON><PERSON><PERSON> tra có phải Tiếng Việt không
  bool get isVietnamese => _currentLocale.languageCode == 'vi';
  
  /// Kiểm tra có phải English không
  bool get isEnglish => _currentLocale.languageCode == 'en';

  /// Khởi tạo provider và load ngôn ngữ đã lưu
  Future<void> initialize() async {
    await _loadSavedLanguage();
  }

  /// Load ngôn ngữ đã lưu từ SharedPreferences với auto-detection
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load trạng thái đã chọn ngôn ngữ trước đó
      _hasSelectedLanguageBefore = prefs.getBool(_languageSelectedKey) ?? false;

      String? savedLanguage = prefs.getString(_languageKey);

      // Nếu chưa có ngôn ngữ đã lưu, thử auto-detect
      if (savedLanguage == null) {
        savedLanguage = _detectSystemLanguage();
      }

      if (_isValidLanguageCode(savedLanguage)) {
        _currentLocale = Locale(savedLanguage);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading saved language: $e');
      // Fallback to default language
      _currentLocale = const Locale(_defaultLanguage);
    }
  }

  /// Auto-detect system language với fallback cho thị trường Việt Nam
  String _detectSystemLanguage() {
    try {
      // Lấy system locale
      final systemLocale = Platform.localeName; // 'vi_VN', 'en_US', etc.
      final languageCode = systemLocale.split('_')[0]; // 'vi', 'en'

      // Kiểm tra có support không
      if (_isValidLanguageCode(languageCode)) {
        return languageCode;
      }

      // Fallback to Vietnamese cho thị trường chính
      return _defaultLanguage;
    } catch (e) {
      print('Error detecting system language: $e');
      return _defaultLanguage;
    }
  }

  /// Thay đổi ngôn ngữ
  Future<void> changeLanguage(String languageCode) async {
    if (!_isValidLanguageCode(languageCode)) {
      print('Invalid language code: $languageCode');
      return;
    }

    if (_currentLocale.languageCode == languageCode) {
      return; // Không thay đổi nếu đã là ngôn ngữ hiện tại
    }

    try {
      // Cập nhật locale
      _currentLocale = Locale(languageCode);

      // Lưu vào SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      await prefs.setBool(_languageSelectedKey, true); // Đánh dấu đã chọn ngôn ngữ

      // Cập nhật trạng thái
      _hasSelectedLanguageBefore = true;

      // Thông báo thay đổi
      notifyListeners();
      
      print('Language changed to: $languageCode');
    } catch (e) {
      print('Error changing language: $e');
    }
  }

  /// Thay đổi sang Tiếng Việt
  Future<void> changeToVietnamese() async {
    await changeLanguage('vi');
  }

  /// Thay đổi sang English
  Future<void> changeToEnglish() async {
    await changeLanguage('en');
  }

  /// Kiểm tra mã ngôn ngữ có hợp lệ không
  bool _isValidLanguageCode(String languageCode) {
    return ['vi', 'en'].contains(languageCode);
  }

  /// Lấy tên ngôn ngữ hiển thị
  String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'vi':
        return '🇻🇳 Tiếng Việt';
      case 'en':
        return '🇺🇸 English';
      default:
        return languageCode;
    }
  }

  /// Lấy danh sách ngôn ngữ hỗ trợ
  List<Map<String, String>> getSupportedLanguages() {
    return [
      {
        'code': 'vi',
        'name': '🇻🇳 Tiếng Việt',
        'nativeName': 'Tiếng Việt'
      },
      {
        'code': 'en',
        'name': '🇺🇸 English',
        'nativeName': 'English'
      },
    ];
  }

  /// Reset về ngôn ngữ mặc định
  Future<void> resetToDefault() async {
    await changeLanguage(_defaultLanguage);
  }

  /// Xóa ngôn ngữ đã lưu (sẽ về mặc định khi restart)
  Future<void> clearSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_languageKey);
      print('Saved language cleared');
    } catch (e) {
      print('Error clearing saved language: $e');
    }
  }

  /// Lấy ngôn ngữ từ user profile (nếu có)
  Future<void> setLanguageFromProfile(String? profileLanguage) async {
    if (profileLanguage != null && _isValidLanguageCode(profileLanguage)) {
      await changeLanguage(profileLanguage);
    }
  }

  /// Debug: In thông tin ngôn ngữ hiện tại
  void debugPrintCurrentLanguage() {
    print('Current Language: ${_currentLocale.languageCode}');
    print('Display Name: ${getLanguageDisplayName(_currentLocale.languageCode)}');
    print('Is Vietnamese: $isVietnamese');
    print('Is English: $isEnglish');
  }
}
