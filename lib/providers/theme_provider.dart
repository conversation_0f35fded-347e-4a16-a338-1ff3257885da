import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  bool _darkMode = false;
  int _primaryColorIndex = 0;
  int _secondaryColorIndex = 2;

  Color _breakfastColor = Colors.orange;
  Color _lunchColor = Colors.teal;
  Color _dinnerColor = Colors.indigo;

  // Danh sách các màu chính có sẵn
  final List<Color> availablePrimaryColors = [
    Colors.blue,
    Colors.purple,
    Colors.green,
    Colors.deepOrange,
    Colors.red,
    Colors.teal,
    Colors.indigo,
    Colors.brown,
    Colors.blueGrey,
    Colors.pink,
  ];

  // Danh sách các màu phụ có sẵn
  final List<Color> availableSecondaryColors = [
    Colors.pinkAccent,
    Colors.purpleAccent,
    Colors.orangeAccent,
    Colors.deepOrangeAccent,
    Colors.redAccent,
    Colors.tealAccent,
    Colors.indigoAccent,
    Colors.blueAccent,
    Colors.greenAccent,
    Colors.amberAccent,
  ];

  // Keys for SharedPreferences
  static const String _darkModeKey = 'dark_mode';
  static const String _primaryColorKey = 'primary_color_index';
  static const String _secondaryColorKey = 'secondary_color_index';
  static const String _breakfastColorKey = 'breakfast_color';
  static const String _lunchColorKey = 'lunch_color';
  static const String _dinnerColorKey = 'dinner_color';

  ThemeProvider() {
    _loadSettings();
  }

  // Getters
  bool get darkMode => _darkMode;
  int get primaryColorIndex => _primaryColorIndex;
  int get secondaryColorIndex => _secondaryColorIndex;
  Color get primaryColor => availablePrimaryColors[_primaryColorIndex];
  Color get secondaryColor => availableSecondaryColors[_secondaryColorIndex];
  Color get breakfastColor => _breakfastColor;
  Color get lunchColor => _lunchColor;
  Color get dinnerColor => _dinnerColor;

  ThemeData get themeData {
    final ColorScheme colorScheme = _darkMode
        ? _createDarkColorScheme()
        : _createLightColorScheme();

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: _darkMode ? getElevatedSurfaceColor(5) : colorScheme.surface,
        foregroundColor: _darkMode ? getActiveColor() : colorScheme.primary,
        elevation: _darkMode ? 4 : 0,
        shadowColor: _darkMode ? Colors.black54 : null,
      ),
      cardTheme: CardThemeData(
        color: _darkMode ? getElevatedSurfaceColor(2) : colorScheme.surface,
        elevation: _darkMode ? 6 : 2,
        shadowColor: _darkMode ? Colors.black87 : Colors.grey.withValues(alpha: 0.15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _darkMode ? getActiveColor() : colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          elevation: _darkMode ? 8 : 4,
          shadowColor: _darkMode
              ? getActiveColor().withValues(alpha: 0.4)
              : colorScheme.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            letterSpacing: 0.25,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          side: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.5,
          ),
        ),
      ),
      tabBarTheme: TabBarThemeData(
        labelColor: colorScheme.primary,
        // ignore: deprecated_member_use
        unselectedLabelColor: colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: colorScheme.primary,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return null;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary.withValues(alpha: 0.5);
          }
          return null;
        }),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
      ),
    );
  }

  // Setters with persistence
  void toggleDarkMode() {
    _darkMode = !_darkMode;
    _saveSettings();
    notifyListeners();
  }

  void setPrimaryColorIndex(int index) {
    if (index >= 0 && index < availablePrimaryColors.length) {
      _primaryColorIndex = index;
      _saveSettings();
      notifyListeners();
    }
  }

  void setSecondaryColorIndex(int index) {
    if (index >= 0 && index < availableSecondaryColors.length) {
      _secondaryColorIndex = index;
      _saveSettings();
      notifyListeners();
    }
  }

  void setBreakfastColor(Color color) {
    _breakfastColor = color;
    _saveSettings();
    notifyListeners();
  }

  void setLunchColor(Color color) {
    _lunchColor = color;
    _saveSettings();
    notifyListeners();
  }

  void setDinnerColor(Color color) {
    _dinnerColor = color;
    _saveSettings();
    notifyListeners();
  }

  // Reset to defaults
  void resetToDefaults() {
    _darkMode = false;
    _primaryColorIndex = 0;
    _secondaryColorIndex = 2;
    _breakfastColor = Colors.orange;
    _lunchColor = Colors.teal;
    _dinnerColor = Colors.indigo;
    _saveSettings();
    notifyListeners();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _darkMode = prefs.getBool(_darkModeKey) ?? false;
      _primaryColorIndex = prefs.getInt(_primaryColorKey) ?? 0;
      _secondaryColorIndex = prefs.getInt(_secondaryColorKey) ?? 2;

      final breakfastColorValue = prefs.getInt(_breakfastColorKey);
      if (breakfastColorValue != null) {
        _breakfastColor = Color(breakfastColorValue);
      }

      final lunchColorValue = prefs.getInt(_lunchColorKey);
      if (lunchColorValue != null) {
        _lunchColor = Color(lunchColorValue);
      }

      final dinnerColorValue = prefs.getInt(_dinnerColorKey);
      if (dinnerColorValue != null) {
        _dinnerColor = Color(dinnerColorValue);
      }

      notifyListeners();
    } catch (e) {
      // If there's an error, use default values
      debugPrint('Lỗi khi tải cài đặt giao diện: $e');
    }
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool(_darkModeKey, _darkMode);
      await prefs.setInt(_primaryColorKey, _primaryColorIndex);
      await prefs.setInt(_secondaryColorKey, _secondaryColorIndex);

      await prefs.setInt(_breakfastColorKey, _breakfastColor.toARGB32());
      await prefs.setInt(_lunchColorKey, _lunchColor.toARGB32());
      await prefs.setInt(_dinnerColorKey, _dinnerColor.toARGB32());
    } catch (e) {
      debugPrint('Lỗi khi lưu cài đặt giao diện: $e');
    }
  }

  /// Tạo color scheme cho chế độ sáng
  ColorScheme _createLightColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: primaryColor,
      secondary: secondaryColor,
      brightness: Brightness.light,
    );
  }

  /// Tạo color scheme cho chế độ tối với màu sắc hiện đại 2024
  ColorScheme _createDarkColorScheme() {
    final baseScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      secondary: secondaryColor,
      brightness: Brightness.dark,
    );

    // Bộ màu hiện đại 2024 cho chế độ tối
    return baseScheme.copyWith(
      // === NỀN VÀ SURFACES ===
      // Rich dark cho nền chính (xu hướng 2024 - không pure black)
      surface: const Color(0xFF0F0F0F),
      onSurface: const Color(0xFFE8E8E8),

      // Elevated surfaces với tông xám hiện đại và subtle warmth
      surfaceContainerLowest: const Color(0xFF141414),
      surfaceContainerLow: const Color(0xFF1A1A1A),
      surfaceContainer: const Color(0xFF202020),
      surfaceContainerHigh: const Color(0xFF262626),
      surfaceContainerHighest: const Color(0xFF2C2C2C),

      // === COLORS CHÍNH ===
      // Primary với độ sáng tối ưu cho dark mode
      primary: _getModernPrimaryForDark(),
      onPrimary: const Color(0xFF000000),

      // Secondary với tông màu bổ sung
      secondary: _getModernSecondaryForDark(),
      onSecondary: const Color(0xFF000000),

      // === CONTAINERS ===
      primaryContainer: const Color(0xFF1A1A2E),
      onPrimaryContainer: _getModernPrimaryForDark(),

      secondaryContainer: const Color(0xFF1A1A1A),
      onSecondaryContainer: _getModernSecondaryForDark(),

      // === OUTLINES VÀ BORDERS ===
      outline: const Color(0xFF484848),
      outlineVariant: const Color(0xFF323232),

      // === STATES ===
      // Error với màu đỏ hiện đại 2024
      error: const Color(0xFFEF4444),
      onError: const Color(0xFFFFFFFF),
      errorContainer: const Color(0xFF2A1A1A),
      onErrorContainer: const Color(0xFFF87171),

      // Success (custom) với màu xanh hiện đại 2024
      tertiary: const Color(0xFF10B981),
      onTertiary: const Color(0xFFFFFFFF),
      tertiaryContainer: const Color(0xFF1A2A22),
      onTertiaryContainer: const Color(0xFF34D399),

      // Warning (custom)
      surfaceTint: const Color(0xFFFF9800),

      // === INVERSE COLORS ===
      inverseSurface: const Color(0xFFE6E6E6),
      onInverseSurface: const Color(0xFF1A1A1A),
      inversePrimary: primaryColor,

      // === SHADOWS ===
      shadow: const Color(0xFF000000),
      scrim: const Color(0xFF000000),
    );
  }

  /// Lấy màu primary hiện đại cho chế độ tối 2024
  Color _getModernPrimaryForDark() {
    // Sử dụng bảng màu hiện đại 2024 dựa trên primary color index
    switch (_primaryColorIndex) {
      case 0: // Blue -> Modern Electric Blue
        return const Color(0xFF3B82F6);
      case 1: // Purple -> Modern Violet
        return const Color(0xFF8B5CF6);
      case 2: // Green -> Modern Emerald
        return const Color(0xFF10B981);
      case 3: // Deep Orange -> Modern Coral
        return const Color(0xFFFF6B6B);
      case 4: // Red -> Modern Rose
        return const Color(0xFFEF4444);
      case 5: // Teal -> Modern Cyan
        return const Color(0xFF06B6D4);
      case 6: // Indigo -> Modern Indigo (default)
        return const Color(0xFF6366F1);
      case 7: // Brown -> Modern Amber
        return const Color(0xFFF59E0B);
      case 8: // Blue Grey -> Modern Slate
        return const Color(0xFF64748B);
      case 9: // Pink -> Modern Pink
        return const Color(0xFFEC4899);
      default:
        return const Color(0xFF6366F1); // Modern Indigo as fallback
    }
  }

  /// Lấy màu secondary hiện đại cho chế độ tối 2024
  Color _getModernSecondaryForDark() {
    // Sử dụng bảng màu secondary hiện đại 2024 dựa trên secondary color index
    switch (_secondaryColorIndex) {
      case 0: // Pink Accent -> Modern Rose
        return const Color(0xFFF472B6);
      case 1: // Purple Accent -> Modern Lavender
        return const Color(0xFFA78BFA);
      case 2: // Orange Accent -> Modern Peach
        return const Color(0xFFFB923C);
      case 3: // Deep Orange Accent -> Modern Coral
        return const Color(0xFFFF7F7F);
      case 4: // Red Accent -> Modern Cherry
        return const Color(0xFFF87171);
      case 5: // Teal Accent -> Modern Mint
        return const Color(0xFF34D399);
      case 6: // Indigo Accent -> Modern Periwinkle
        return const Color(0xFF818CF8);
      case 7: // Blue Accent -> Modern Sky
        return const Color(0xFF60A5FA);
      case 8: // Green Accent -> Modern Lime
        return const Color(0xFF4ADE80);
      case 9: // Amber Accent -> Modern Gold
        return const Color(0xFFFBBF24);
      default:
        return const Color(0xFFA78BFA); // Modern Lavender as fallback
    }
  }



  /// Lấy màu active/selected phù hợp cho chế độ hiện tại
  Color getActiveColor() {
    if (_darkMode) {
      return _getModernPrimaryForDark();
    } else {
      return primaryColor;
    }
  }

  /// Lấy màu background cho active state (hiện đại hơn)
  Color getActiveBackgroundColor() {
    if (_darkMode) {
      // Sử dụng gradient subtle cho background
      return _getModernPrimaryForDark().withValues(alpha: 0.12);
    } else {
      return primaryColor.withValues(alpha: 0.08);
    }
  }

  /// Lấy màu border cho active state (tương phản cao hơn)
  Color getActiveBorderColor() {
    if (_darkMode) {
      return _getModernPrimaryForDark().withValues(alpha: 0.8);
    } else {
      return primaryColor.withValues(alpha: 0.9);
    }
  }

  /// Lấy màu text cho active state
  Color getActiveTextColor() {
    if (_darkMode) {
      return _getModernPrimaryForDark();
    } else {
      return primaryColor;
    }
  }

  /// Lấy màu cho elevated surfaces trong dark mode
  Color getElevatedSurfaceColor([int elevation = 1]) {
    if (!_darkMode) return Colors.white;

    switch (elevation) {
      case 0: return const Color(0xFF000000); // True black
      case 1: return const Color(0xFF0A0A0A); // Slightly elevated
      case 2: return const Color(0xFF121212); // Cards
      case 3: return const Color(0xFF1A1A1A); // Modals
      case 4: return const Color(0xFF222222); // Navigation
      case 5: return const Color(0xFF2A2A2A); // App bars
      default: return const Color(0xFF2A2A2A);
    }
  }

  /// Lấy màu accent hiện đại cho highlights
  Color getAccentColor() {
    if (_darkMode) {
      // Sử dụng màu accent sáng cho dark mode
      final hsl = HSLColor.fromColor(secondaryColor);
      return hsl.withLightness(0.7).withSaturation(0.9).toColor();
    } else {
      return secondaryColor;
    }
  }
}
