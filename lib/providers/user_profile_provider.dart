import 'package:flutter/foundation.dart';
import '../models/user_profile.dart';
import '../services/auth_service.dart';
import '../services/new_user_profile_service.dart';

/// Provider quản lý state của user profile và onboarding
class UserProfileProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  final NewUserProfileService _userProfileService = NewUserProfileService();
  
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isOnboardingCompleted => _userProfile?.isOnboardingCompleted ?? false;

  /// Khởi tạo và load user profile
  Future<void> initialize() async {
    await loadUserProfile();
  }

  /// Load user profile từ database
  Future<void> loadUserProfile() async {
    _setLoading(true);
    _setError(null);

    try {
      // Lấy thông tin user hiện tại
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        _setError('Không thể lấy thông tin người dùng');
        return;
      }

      final userId = userData['userData']['uid'];
      
      // Load profile từ database
      final result = await _userProfileService.getUserProfile(userId);
      
      if (result['success']) {
        _userProfile = result['profile'];
      } else {
        // Nếu chưa có profile, tạo profile mới với thông tin cơ bản
        await _createInitialProfile(userData['userData']);
      }
    } catch (e) {
      _setError('Lỗi khi tải thông tin người dùng: $e');
      print('Error loading user profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Tạo profile ban đầu cho user mới
  Future<void> _createInitialProfile(Map<String, dynamic> userData) async {
    try {
      _userProfile = UserProfile(
        userId: userData['uid'],
        displayName: userData['displayName'],
        email: userData['email'],
        photoUrl: userData['photoURL'],
        isOnboardingCompleted: false,
      );

      // Lưu vào database
      await _userProfileService.createUserProfile(_userProfile!);
    } catch (e) {
      _setError('Lỗi khi tạo profile người dùng: $e');
      print('Error creating initial profile: $e');
    }
  }

  /// Cập nhật profile với thông tin từ onboarding
  Future<void> updateProfile({
    String? language,
    String? displayName,
    Gender? gender,
    CookingPreference? cookingPreference,
    List<String>? favoriteIngredients,
    List<String>? dietaryRestrictions,
    bool? isVegetarian,
    bool? isOnboardingCompleted,
  }) async {
    if (_userProfile == null) {
      _setError('Không có thông tin người dùng để cập nhật');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      // Tạo profile mới với thông tin cập nhật
      final updatedProfile = _userProfile!.copyWith(
        language: language,
        displayName: displayName,
        gender: gender,
        cookingPreference: cookingPreference,
        favoriteIngredients: favoriteIngredients,
        dietaryRestrictions: dietaryRestrictions,
        isVegetarian: isVegetarian,
        isOnboardingCompleted: isOnboardingCompleted,
      );

      // Lưu vào database
      final result = await _userProfileService.updateUserProfile(updatedProfile);
      
      if (result['success']) {
        _userProfile = updatedProfile;
        notifyListeners();
      } else {
        _setError(result['message'] ?? 'Lỗi khi cập nhật thông tin');
      }
    } catch (e) {
      _setError('Lỗi khi cập nhật thông tin: $e');
      print('Error updating profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Cập nhật thông tin cơ bản
  Future<void> updateBasicInfo({
    String? displayName,
    String? photoUrl,
  }) async {
    if (_userProfile == null) return;

    _setLoading(true);
    _setError(null);

    try {
      final updatedProfile = _userProfile!.copyWith(
        displayName: displayName,
        photoUrl: photoUrl,
      );

      final result = await _userProfileService.updateUserProfile(updatedProfile);
      
      if (result['success']) {
        _userProfile = updatedProfile;
        notifyListeners();
      } else {
        _setError(result['message'] ?? 'Lỗi khi cập nhật thông tin');
      }
    } catch (e) {
      _setError('Lỗi khi cập nhật thông tin: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Thêm nguyên liệu yêu thích
  Future<void> addFavoriteIngredient(String ingredient) async {
    if (_userProfile == null) return;

    final currentIngredients = List<String>.from(_userProfile!.favoriteIngredients);
    if (!currentIngredients.contains(ingredient)) {
      currentIngredients.add(ingredient);
      await updateProfile(favoriteIngredients: currentIngredients);
    }
  }

  /// Xóa nguyên liệu yêu thích
  Future<void> removeFavoriteIngredient(String ingredient) async {
    if (_userProfile == null) return;

    final currentIngredients = List<String>.from(_userProfile!.favoriteIngredients);
    if (currentIngredients.contains(ingredient)) {
      currentIngredients.remove(ingredient);
      await updateProfile(favoriteIngredients: currentIngredients);
    }
  }

  /// Thêm hạn chế ăn uống
  Future<void> addDietaryRestriction(String restriction) async {
    if (_userProfile == null) return;

    final currentRestrictions = List<String>.from(_userProfile!.dietaryRestrictions);
    if (!currentRestrictions.contains(restriction)) {
      currentRestrictions.add(restriction);
      await updateProfile(dietaryRestrictions: currentRestrictions);
    }
  }

  /// Xóa hạn chế ăn uống
  Future<void> removeDietaryRestriction(String restriction) async {
    if (_userProfile == null) return;

    final currentRestrictions = List<String>.from(_userProfile!.dietaryRestrictions);
    if (currentRestrictions.contains(restriction)) {
      currentRestrictions.remove(restriction);
      await updateProfile(dietaryRestrictions: currentRestrictions);
    }
  }

  /// Reset profile (dùng khi đăng xuất)
  void resetProfile() {
    _userProfile = null;
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  /// Kiểm tra xem có cần onboarding không
  bool needsOnboarding() {
    return _userProfile == null || !_userProfile!.isOnboardingCompleted;
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    if (error != null) {
      notifyListeners();
    }
  }

  /// Lấy thông tin tóm tắt cho hiển thị
  Map<String, String> getProfileSummary() {
    if (_userProfile == null) return {};

    return {
      'Tên': _userProfile!.displayName ?? 'Chưa cập nhật',
      'Email': _userProfile!.email ?? 'Chưa cập nhật',
      'Giới tính': _userProfile!.gender?.displayName ?? 'Chưa cập nhật',
      'Xu hướng nấu ăn': _userProfile!.cookingPreference?.displayName ?? 'Chưa cập nhật',
      'Ăn chay': _userProfile!.isVegetarian ? 'Có' : 'Không',
      'Nguyên liệu yêu thích': _userProfile!.favoriteIngredients.isEmpty 
          ? 'Chưa có' 
          : _userProfile!.favoriteIngredients.join(', '),
    };
  }

  /// Lấy gợi ý dựa trên profile
  List<String> getPersonalizedSuggestions() {
    if (_userProfile == null) return [];

    List<String> suggestions = [];

    // Gợi ý dựa trên giới tính
    if (_userProfile!.gender == Gender.female) {
      suggestions.addAll([
        'Món ăn giảm cân',
        'Món ăn đẹp da',
        'Món ăn bổ dưỡng cho phụ nữ',
      ]);
    }

    // Gợi ý dựa trên xu hướng nấu ăn
    if (_userProfile!.cookingPreference == CookingPreference.simple) {
      suggestions.addAll([
        'Món ăn 15 phút',
        'Món ăn ít nguyên liệu',
        'Món ăn đơn giản',
      ]);
    } else {
      suggestions.addAll([
        'Món ăn cầu kỳ',
        'Món ăn đặc biệt',
        'Món ăn sáng tạo',
      ]);
    }

    // Gợi ý dựa trên ăn chay
    if (_userProfile!.isVegetarian) {
      suggestions.addAll([
        'Món chay ngon',
        'Món chay bổ dưỡng',
        'Món chay đơn giản',
      ]);
    }

    return suggestions;
  }
}
