import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import '../services/deep_link_service.dart';
import '../providers/user_profile_provider.dart';
import '../providers/language_provider.dart';
import 'login_screen.dart';
import 'language_selection_screen.dart';
import 'new_onboarding_screen.dart';
import 'new_home_screen.dart';

/// AuthWrapper kiểm tra trạng thái authentication khi app khởi động
/// và chuyển hướng đến màn hình phù hợp.
/// <PERSON><PERSON> cũng lắng nghe các thay đổi trạng thái auth để xử lý deep link callbacks.
class AuthWrapper extends StatefulWidget {
  static const routeName = '/auth-wrapper';

  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late final StreamSubscription<AuthState> _authSubscription;
  StreamSubscription<bool>? _deepLinkSubscription;

  @override
  void initState() {
    super.initState();
    _setupAuthListener();
    _setupDeepLinkListener();
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    _deepLinkSubscription?.cancel();
    super.dispose();
  }

  Future<void> _checkOnboardingAndNavigate() async {
    try {
      final userProfileProvider = Provider.of<UserProfileProvider>(context, listen: false);
      await userProfileProvider.initialize();

      if (!mounted) return;

      if (userProfileProvider.needsOnboarding()) {
        Navigator.of(context).pushReplacementNamed(NewOnboardingScreen.routeName);
      } else {
        Navigator.of(context).pushReplacementNamed(NewHomeScreen.routeName);
      }
    } catch (e) {
      print('Error checking onboarding status: $e');
      // Fallback to onboarding if there's an error
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(NewOnboardingScreen.routeName);
      }
    }
  }

  void _setupAuthListener() {
    // Lắng nghe các thay đổi trạng thái xác thực
    _authSubscription =
        Supabase.instance.client.auth.onAuthStateChange.listen((data) {
      final AuthChangeEvent event = data.event;
      final Session? session = data.session;

      print('AuthWrapper: Received auth event: $event');

      if (session != null) {
        // Người dùng đã đăng nhập hoặc session được phục hồi
        print('AuthWrapper: User is signed in. Checking onboarding status.');
        if (mounted) {
          _checkOnboardingAndNavigate();
        }
      } else {
        // Người dùng đã đăng xuất hoặc chưa đăng nhập
        // Kiểm tra đã chọn ngôn ngữ chưa
        final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

        if (!languageProvider.hasSelectedLanguageBefore) {
          print('AuthWrapper: User has not selected language. Navigating to LanguageSelectionScreen.');
          if (mounted) {
            Navigator.of(context).pushReplacementNamed(LanguageSelectionScreen.routeName);
          }
        } else {
          print('AuthWrapper: User is signed out. Navigating to LoginScreen.');
          if (mounted) {
            Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
          }
        }
      }
    });

    // Kiểm tra trạng thái ban đầu
    final initialSession = Supabase.instance.client.auth.currentSession;
    if (initialSession == null) {
       print('AuthWrapper: Initial session is null. Navigating to LoginScreen.');
       // Đảm bảo điều hướng xảy ra sau khi frame đầu tiên được build
       WidgetsBinding.instance.addPostFrameCallback((_) {
         if (mounted) {
            Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
         }
       });
    }
  }

  void _setupDeepLinkListener() {
    // Listen for deep link auth success events
    _deepLinkSubscription = DeepLinkService().authSuccessStream.listen((success) {
      if (success && mounted) {
        print('AuthWrapper: Deep link auth success detected');
        // Auth state change listener sẽ tự động handle navigation
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Hiển thị màn hình loading trong khi chờ trạng thái auth được xác định
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
