import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AuthorScreen extends StatelessWidget {
  static const routeName = '/author';

  const AuthorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
              Theme.of(context).colorScheme.secondary.withOpacity(0.5),
            ],
          ),
        ),
        child: SafeArea(
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                leading: IconButton(
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                flexibleSpace: FlexibleSpaceBar(
                  centerTitle: true,
                  title: Text(
                    '<PERSON><PERSON><PERSON><PERSON>',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      Container(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const Center(
                        child: Icon(
                          Icons.person,
                          size: 100,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoCard(
                        context,
                        'Giới thiệu',
                        'Là một lập trình viên đam mê ẩm thực, tôi tạo ra \'Nấu gì đây\' để giải quyết nỗi băn khoăn hàng ngày về bữa ăn. Kết hợp kiến thức lập trình với tình yêu dành cho món Việt, tôi mong muốn ứng dụng này sẽ giúp mọi người tiết kiệm thời gian lên kế hoạch và khám phá niềm vui trong việc nấu nướng đa dạng.',
                        Icons.info_outline,
                      ),
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        context,
                        'Liên hệ',
                        '',
                        Icons.contact_mail,
                        contactInfo: [
                          _buildContactItem(
                              context,
                              Icons.facebook,
                              'Facebook: @nmductech',
                              'https://www.facebook.com/nmductech'),
                          _buildContactItem(
                              context,
                              Icons.email,
                              'Email: <EMAIL>',
                              'mailto:<EMAIL>'),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        context,
                        'Dự án khác',
                        'Xem thêm các dự án khác của tôi trên GitHub và các nền tảng khác.',
                        Icons.work_outline,
                      ),
                      const SizedBox(height: 24),
                      Center(
                        child: Text(
                          'Cảm ơn bạn đã sử dụng ứng dụng!',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    fontStyle: FontStyle.italic,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.7),
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context,
    String title,
    String content,
    IconData icon, {
    List<Widget>? contactInfo,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.tertiary,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (content.isNotEmpty)
              Text(
                content,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            if (contactInfo != null) ...contactInfo,
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(
      BuildContext context, IconData icon, String text, String url) {
    return InkWell(
      onTap: () async {
        try {
          if (await canLaunch(url)) {
            await launch(url);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Không thể mở liên kết: $url'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Đã xảy ra lỗi khi mở liên kết'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
