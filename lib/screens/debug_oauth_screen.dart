import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/supabase_auth_service.dart';
import '../config/google_config.dart';
import '../supabase_options.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DebugOAuthScreen extends StatefulWidget {
  static const routeName = '/debug-oauth';
  
  const DebugOAuthScreen({Key? key}) : super(key: key);

  @override
  State<DebugOAuthScreen> createState() => _DebugOAuthScreenState();
}

class _DebugOAuthScreenState extends State<DebugOAuthScreen> {
  final _authService = SupabaseAuthService();

  String _lastResult = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('OAuth Debug'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('OAuth Tests', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testNormalOAuth,
                      child: Text('Test Normal OAuth'),
                    ),
                    SizedBox(height: 8),
                    
                    ElevatedButton(
                      onPressed: _testGoogleConfig,
                      child: Text('Test Google Config'),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Debug Info', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: () => _showDebugInfo(context),
                      child: Text('Show Debug Info'),
                    ),
                    SizedBox(height: 8),
                    
                    ElevatedButton(
                      onPressed: _copyADBCommand,
                      child: Text('Copy ADB Test Command'),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Last Result:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      SizedBox(height: 8),
                      
                      if (_isLoading)
                        Center(child: CircularProgressIndicator())
                      else
                        Expanded(
                          child: SingleChildScrollView(
                            child: SelectableText(
                              _lastResult.isEmpty ? 'No results yet' : _lastResult,
                              style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testNormalOAuth() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing normal OAuth...\n';
    });

    try {
      final result = await _authService.signInWithGoogle();
      setState(() {
        _lastResult += 'Normal OAuth Result:\n${result.toString()}\n\n';
      });
    } catch (e) {
      setState(() {
        _lastResult += 'Normal OAuth Error:\n$e\n\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testGoogleConfig() {
    setState(() {
      _lastResult = 'Testing Google Configuration...\n\n';
    });

    setState(() {
      _lastResult += 'Google OAuth Configuration:\n';
      _lastResult += '================================\n';
      _lastResult += 'Web Client ID: ${GoogleConfig.webClientId}\n';
      _lastResult += 'iOS Client ID: ${GoogleConfig.iosClientId}\n';
      _lastResult += 'Is Configured: ${GoogleConfig.isConfigured}\n\n';

      if (!GoogleConfig.isConfigured) {
        _lastResult += 'Configuration Error:\n';
        _lastResult += GoogleConfig.configurationError;
      } else {
        _lastResult += '✅ Google OAuth is properly configured!\n';
        _lastResult += 'You can now test Google Sign In.\n';
      }
    });
  }

  void _showDebugInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('OAuth Debug Info'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Supabase URL:', style: TextStyle(fontWeight: FontWeight.bold)),
              SelectableText(SupabaseConfig.url),
              SizedBox(height: 8),

              Text('Redirect URI:', style: TextStyle(fontWeight: FontWeight.bold)),
              SelectableText('com.minhduc.naugiday://callback'),
              SizedBox(height: 8),

              Text('Current User:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(Supabase.instance.client.auth.currentUser?.email ?? 'None'),
              SizedBox(height: 16),

              Text('ADB Test Command:', style: TextStyle(fontWeight: FontWeight.bold)),
              SelectableText('adb shell am start -W -a android.intent.action.VIEW -d "com.minhduc.naugiday://callback?test=1" com.minhduc.naugiday'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _copyADBCommand() {
    const command = 'adb shell am start -W -a android.intent.action.VIEW -d "com.minhduc.naugiday://callback?test=1" com.minhduc.naugiday';

    Clipboard.setData(ClipboardData(text: command));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ADB command copied to clipboard!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
