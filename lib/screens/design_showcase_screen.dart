import 'package:flutter/material.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../widgets/modern_button.dart';
import '../widgets/modern_card.dart';

/// Màn hình showcase các component UI hiện đại
class DesignShowcaseScreen extends StatefulWidget {
  static const String routeName = '/design-showcase';

  const DesignShowcaseScreen({super.key});

  @override
  State<DesignShowcaseScreen> createState() => _DesignShowcaseScreenState();
}

class _DesignShowcaseScreenState extends State<DesignShowcaseScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Design Showcase',
          style: AppTextStyles.titleLarge(context).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTypographySection(context),
            const SizedBox(height: 32),
            _buildButtonsSection(context),
            const SizedBox(height: 32),
            _buildCardsSection(context),
            const SizedBox(height: 32),
            _buildColorsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTypographySection(BuildContext context) {
    return ModernCard.elevated(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Typography System',
            style: AppTextStyles.headlineSmall(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildTypographyExample('Display Large', AppTextStyles.displayLarge(context)),
          _buildTypographyExample('Display Medium', AppTextStyles.displayMedium(context)),
          _buildTypographyExample('Display Small', AppTextStyles.displaySmall(context)),
          _buildTypographyExample('Headline Large', AppTextStyles.headlineLarge(context)),
          _buildTypographyExample('Headline Medium', AppTextStyles.headlineMedium(context)),
          _buildTypographyExample('Headline Small', AppTextStyles.headlineSmall(context)),
          _buildTypographyExample('Title Large', AppTextStyles.titleLarge(context)),
          _buildTypographyExample('Title Medium', AppTextStyles.titleMedium(context)),
          _buildTypographyExample('Title Small', AppTextStyles.titleSmall(context)),
          _buildTypographyExample('Body Large', AppTextStyles.bodyLarge(context)),
          _buildTypographyExample('Body Medium', AppTextStyles.bodyMedium(context)),
          _buildTypographyExample('Body Small', AppTextStyles.bodySmall(context)),
          _buildTypographyExample('Label Large', AppTextStyles.labelLarge(context)),
          _buildTypographyExample('Label Medium', AppTextStyles.labelMedium(context)),
          _buildTypographyExample('Label Small', AppTextStyles.labelSmall(context)),
        ],
      ),
    );
  }

  Widget _buildTypographyExample(String name, TextStyle style) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              name,
              style: AppTextStyles.labelSmall(context).copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
          Expanded(
            child: Text(
              'Sample Text',
              style: style,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsSection(BuildContext context) {
    return ModernCard.elevated(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Modern Buttons',
            style: AppTextStyles.headlineSmall(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Primary Buttons
          Text(
            'Primary Buttons',
            style: AppTextStyles.titleMedium(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ModernButton.primary(
                text: 'Primary',
                onPressed: () {},
                size: ModernButtonSize.small,
              ),
              ModernButton.primary(
                text: 'Primary',
                onPressed: () {},
                icon: Icons.star,
              ),
              ModernButton.primary(
                text: _isLoading ? 'Loading...' : 'Primary',
                onPressed: _isLoading ? null : () {
                  setState(() => _isLoading = true);
                  Future.delayed(const Duration(seconds: 2), () {
                    setState(() => _isLoading = false);
                  });
                },
                isLoading: _isLoading,
                size: ModernButtonSize.large,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Secondary Buttons
          Text(
            'Secondary Buttons',
            style: AppTextStyles.titleMedium(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ModernButton.secondary(
                text: 'Secondary',
                onPressed: () {},
              ),
              ModernButton.outline(
                text: 'Outline',
                onPressed: () {},
                icon: Icons.favorite_border,
              ),
              ModernButton.ghost(
                text: 'Ghost',
                onPressed: () {},
              ),
              ModernButton.glass(
                text: 'Glass',
                onPressed: () {},
                icon: Icons.auto_awesome,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Modern Cards',
          style: AppTextStyles.headlineSmall(context).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // Elevated Card
        ModernCard.elevated(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Elevated Card',
                style: AppTextStyles.titleMedium(context).copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This is an elevated card with shadow and modern styling.',
                style: AppTextStyles.bodyMedium(context),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Outlined Card
        ModernCard.outlined(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Outlined Card',
                style: AppTextStyles.titleMedium(context).copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This is an outlined card with border styling.',
                style: AppTextStyles.bodyMedium(context),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Glassmorphism Card
        ModernCard.glass(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Glassmorphism Card',
                style: AppTextStyles.titleMedium(context).copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This is a glassmorphism card with translucent background.',
                style: AppTextStyles.bodyMedium(context),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Card with Header
        ModernCardWithHeader(
          title: 'Card with Header',
          subtitle: 'This card has a header section',
          leading: const Icon(Icons.star),
          trailing: IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {},
          ),
          actions: [
            ModernButton.ghost(
              text: 'Cancel',
              onPressed: () {},
              size: ModernButtonSize.small,
            ),
            ModernButton.primary(
              text: 'Save',
              onPressed: () {},
              size: ModernButtonSize.small,
            ),
          ],
          child: Text(
            'This is the main content of the card with header and actions.',
            style: AppTextStyles.bodyMedium(context),
          ),
        ),
      ],
    );
  }

  Widget _buildColorsSection(BuildContext context) {
    return ModernCard.elevated(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Modern Color Palette',
            style: AppTextStyles.headlineSmall(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildColorRow('Brand Colors', [
            ('Primary', ModernColors.primaryBrand),
            ('Secondary', ModernColors.secondaryBrand),
            ('Accent', ModernColors.accentBrand),
          ]),
          
          _buildColorRow('Semantic Colors', [
            ('Success', ModernColors.success),
            ('Warning', ModernColors.warning),
            ('Error', ModernColors.error),
            ('Info', ModernColors.info),
          ]),
          
          _buildColorRow('Meal Colors', [
            ('Breakfast', ModernColors.breakfastPrimary),
            ('Lunch', ModernColors.lunchPrimary),
            ('Dinner', ModernColors.dinnerPrimary),
          ]),
        ],
      ),
    );
  }

  Widget _buildColorRow(String title, List<(String, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.titleSmall(context).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: colors.map((colorData) {
            final (name, color) = colorData;
            return Expanded(
              child: Column(
                children: [
                  Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    name,
                    style: AppTextStyles.labelSmall(context),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
