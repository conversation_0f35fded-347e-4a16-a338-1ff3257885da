import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_profile.dart';
import '../providers/user_profile_provider.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';

/// Screen cho phép chỉnh sửa thông tin profile từ onboarding
class EditProfileScreen extends StatefulWidget {
  static const String routeName = '/edit-profile';

  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  
  // Các biến state cho form
  String? _selectedLanguage;
  Gender? _selectedGender;
  CookingPreference? _selectedCookingPreference;
  bool _isVegetarian = false;
  List<String> _favoriteIngredients = [];
  List<String> _dietaryRestrictions = [];
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    super.dispose();
  }

  void _loadCurrentProfile() {
    final provider = Provider.of<UserProfileProvider>(context, listen: false);
    final profile = provider.userProfile;
    
    if (profile != null) {
      _displayNameController.text = profile.displayName ?? '';
      _selectedLanguage = profile.language;
      _selectedGender = profile.gender;
      _selectedCookingPreference = profile.cookingPreference;
      _isVegetarian = profile.isVegetarian;
      _favoriteIngredients = List.from(profile.favoriteIngredients);
      _dietaryRestrictions = List.from(profile.dietaryRestrictions);
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final provider = Provider.of<UserProfileProvider>(context, listen: false);
      
      await provider.updateProfile(
        displayName: _displayNameController.text.trim(),
        language: _selectedLanguage,
        gender: _selectedGender,
        cookingPreference: _selectedCookingPreference,
        isVegetarian: _isVegetarian,
        favoriteIngredients: _favoriteIngredients,
        dietaryRestrictions: _dietaryRestrictions,
      );

      if (provider.error == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cập nhật thông tin thành công!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(provider.error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const SafeText('Chỉnh sửa thông tin'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveProfile,
              child: SafeText(
                'Lưu',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(l10n, isDarkMode),
              const SizedBox(height: 24),
              _buildLanguageSection(l10n, isDarkMode),
              const SizedBox(height: 24),
              _buildGenderSection(l10n, isDarkMode),
              const SizedBox(height: 24),
              _buildCookingPreferenceSection(l10n, isDarkMode),
              const SizedBox(height: 24),
              _buildDietarySection(l10n, isDarkMode),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(AppLocalizations? l10n, bool isDarkMode) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin cơ bản',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Tên hiển thị',
                prefixIcon: Icon(Icons.person_outline),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập tên hiển thị';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSection(AppLocalizations? l10n, bool isDarkMode) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ngôn ngữ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedLanguage,
              decoration: const InputDecoration(
                labelText: 'Chọn ngôn ngữ',
                prefixIcon: Icon(Icons.language),
                border: OutlineInputBorder(),
              ),
              items: [
                DropdownMenuItem(
                  value: 'vi',
                  child: Row(
                    children: [
                      Image.asset('assets/images/vietnam_flag.png', width: 24, height: 16),
                      const SizedBox(width: 8),
                      const Text('Tiếng Việt'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'en',
                  child: Row(
                    children: [
                      Image.asset('assets/images/uk_flag.png', width: 24, height: 16),
                      const SizedBox(width: 8),
                      const Text('English'),
                    ],
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() => _selectedLanguage = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderSection(AppLocalizations? l10n, bool isDarkMode) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Giới tính',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Gender>(
              value: _selectedGender,
              decoration: const InputDecoration(
                labelText: 'Chọn giới tính',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(
                  value: Gender.male,
                  child: Text('Nam'),
                ),
                DropdownMenuItem(
                  value: Gender.female,
                  child: Text('Nữ'),
                ),
                DropdownMenuItem(
                  value: Gender.other,
                  child: Text('Khác'),
                ),
              ],
              onChanged: (value) {
                setState(() => _selectedGender = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCookingPreferenceSection(AppLocalizations? l10n, bool isDarkMode) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sở thích nấu ăn',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CookingPreference>(
              value: _selectedCookingPreference,
              decoration: const InputDecoration(
                labelText: 'Chọn sở thích nấu ăn',
                prefixIcon: Icon(Icons.restaurant),
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(
                  value: CookingPreference.simple,
                  child: Text('Đơn giản'),
                ),
                DropdownMenuItem(
                  value: CookingPreference.elaborate,
                  child: Text('Cầu kỳ'),
                ),
              ],
              onChanged: (value) {
                setState(() => _selectedCookingPreference = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDietarySection(AppLocalizations? l10n, bool isDarkMode) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sở thích ăn uống',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Ăn chay'),
              subtitle: const Text('Tôi ăn chay'),
              value: _isVegetarian,
              onChanged: (value) {
                setState(() => _isVegetarian = value);
              },
              secondary: const Icon(Icons.eco),
            ),
          ],
        ),
      ),
    );
  }
}
