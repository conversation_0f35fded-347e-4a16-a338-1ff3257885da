import 'package:flutter/material.dart';
import '../services/supabase_auth_service.dart';
import '../services/rate_limit_service.dart';
import '../widgets/rate_limit_widget.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import 'login_screen.dart';
import 'otp_verification_reset_screen.dart';

class ForgotPasswordOtpScreen extends StatefulWidget {
  static const routeName = '/forgot-password-otp';

  const ForgotPasswordOtpScreen({super.key});

  @override
  State<ForgotPasswordOtpScreen> createState() => _ForgotPasswordOtpScreenState();
}

class _ForgotPasswordOtpScreenState extends State<ForgotPasswordOtpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _authService = SupabaseAuthService();
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;
  
  // Rate limiting states
  bool _isRateLimited = false;
  int _rateLimitMinutes = 0;
  int _remainingAttempts = 3;
  final int _maxAttempts = 3;

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_onEmailChanged);
  }

  @override
  void dispose() {
    _emailController.removeListener(_onEmailChanged);
    _emailController.dispose();
    super.dispose();
  }

  /// Xử lý khi email thay đổi - kiểm tra rate limit
  void _onEmailChanged() {
    if (_emailController.text.isNotEmpty && _emailController.text.contains('@')) {
      _checkRateLimit();
    }
  }

  /// Kiểm tra rate limit cho email hiện tại
  Future<void> _checkRateLimit() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) return;

    try {
      final rateLimitStatus = await RateLimitService.canSendEmail(email);
      
      if (mounted) {
        setState(() {
          _isRateLimited = !rateLimitStatus['canSend'];
          _rateLimitMinutes = rateLimitStatus['cooldownMinutes'] ?? 0;
          _remainingAttempts = rateLimitStatus['remainingAttempts'] ?? 3;
        });
      }
    } catch (e) {
      print('Lỗi kiểm tra rate limit: $e');
    }
  }

  /// Reset rate limit countdown
  void _onRateLimitComplete() {
    if (mounted) {
      setState(() {
        _isRateLimited = false;
        _rateLimitMinutes = 0;
        _remainingAttempts = _maxAttempts;
      });
    }
  }

  /// Gửi OTP đến email để đặt lại mật khẩu
  Future<void> _sendOtpForPasswordReset() async {
    if (!_formKey.currentState!.validate()) return;

    final email = _emailController.text.trim();
    
    // Kiểm tra rate limit trước khi gửi
    final rateLimitCheck = await RateLimitService.canSendEmail(email);
    if (!rateLimitCheck['canSend']) {
      setState(() {
        _isRateLimited = true;
        _rateLimitMinutes = rateLimitCheck['cooldownMinutes'] ?? 0;
        _errorMessage = rateLimitCheck['message'];
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      print('=== BẮT ĐẦU GỬI OTP ĐẶT LẠI MẬT KHẨU TỪ UI ===');
      print('Email: $email');

      final result = await _authService.sendOtpForPasswordReset(email);

      print('Kết quả gửi OTP đặt lại mật khẩu: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Gửi OTP đặt lại mật khẩu thành công');

        // Ghi nhận việc gửi email thành công
        await RateLimitService.recordEmailSent(email);
        
        // Cập nhật rate limit state
        await _checkRateLimit();

        // Hiển thị thông báo thành công
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Mã OTP đã được gửi đến email của bạn!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Chuyển đến màn hình xác minh OTP
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => OtpVerificationResetScreen(
              email: email,
            ),
          ),
        );
      } else {
        print('UI: Gửi OTP đặt lại mật khẩu thất bại');
        
        // Kiểm tra xem có phải lỗi rate limit từ server không
        final errorMessage = result['message'] ?? 'Đã xảy ra lỗi không xác định';
        if (errorMessage.toLowerCase().contains('rate limit') || 
            errorMessage.toLowerCase().contains('too many requests')) {
          // Ghi nhận rate limit từ server
          await RateLimitService.recordRateLimit(email);
          await _checkRateLimit();
        }
        
        setState(() {
          _errorMessage = errorMessage;
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi gửi OTP đặt lại mật khẩu exception: $e');

      if (mounted) {
        setState(() {
          _errorMessage = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('=== KẾT THÚC GỬI OTP ĐẶT LẠI MẬT KHẨU TỪ UI ===');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      // Loại bỏ AppBar để có thiết kế hiện đại hơn
      body: Stack(
        children: [
          // Gradient background
          Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
              ),
            ),
          ),

          // Logo và hình ảnh trang trí ở trên cùng
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.22,
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Image.asset(
                      'assets/images/logo.png',
                      width: 100,
                      height: 100,
                    ),
                  ),
                  SizedBox(height: 8),
                  SafeText(
                    'Quên mật khẩu',
                    style: AppTextStyles.title1(context)
                        .withWeight(FontWeight.bold)
                        .withColor(Colors.white),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 8,
            left: 8,
            child: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),

          // Nội dung form quên mật khẩu
          Positioned.fill(
            top: MediaQuery.of(context).size.height * 0.22,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                left: 24,
                right: 24,
                bottom: 24,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Card chứa form quên mật khẩu
                  Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Hiển thị lỗi nếu có
                            if (_errorMessage != null)
                              Container(
                                padding: const EdgeInsets.all(12),
                                margin: const EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.red.shade200),
                                ),
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(color: Colors.red.shade700),
                                  textAlign: TextAlign.center,
                                ),
                              ),

                            // Hiển thị thông báo thành công nếu có
                            if (_successMessage != null)
                              Container(
                                padding: const EdgeInsets.all(12),
                                margin: const EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.green.shade200),
                                ),
                                child: Text(
                                  _successMessage!,
                                  style: TextStyle(color: Colors.green.shade700),
                                  textAlign: TextAlign.center,
                                ),
                              ),

                            // Rate Limit Widget (hiển thị khi bị rate limit)
                            if (_isRateLimited && _rateLimitMinutes > 0)
                              RateLimitWidget(
                                initialMinutes: _rateLimitMinutes,
                                onCountdownComplete: _onRateLimitComplete,
                                customMessage: 'Bạn đã gửi quá nhiều yêu cầu OTP.',
                              ),

                            // Rate Limit Info (hiển thị số lần gửi còn lại)
                            if (!_isRateLimited && _remainingAttempts < _maxAttempts)
                              RateLimitInfo(
                                remainingAttempts: _remainingAttempts,
                                maxAttempts: _maxAttempts,
                              ),

                            // Hướng dẫn
                            SafeText(
                              'Nhập email của bạn để nhận mã OTP đặt lại mật khẩu.',
                              style: AppTextStyles.callout(context)
                                  .withColor(theme.colorScheme.onSurface.withOpacity(0.7)),
                              maxLines: 2,
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 24),

                            // Email field
                            TextFormField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              style: TextStyle(fontSize: 16),
                              decoration: InputDecoration(
                                labelText: 'Email',
                                hintText: 'Nhập email của bạn',
                                prefixIcon: Icon(Icons.email_outlined),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(width: 1),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 16, horizontal: 16),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Vui lòng nhập email';
                                }
                                if (!value.contains('@') ||
                                    !value.contains('.')) {
                                  return 'Email không hợp lệ';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            // Thông báo về OTP
                            Container(
                              padding: const EdgeInsets.all(12),
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Text(
                                'Chúng tôi sẽ gửi mã OTP 6 chữ số đến email của bạn để xác minh danh tính.',
                                style: TextStyle(color: Colors.blue.shade700),
                                textAlign: TextAlign.center,
                              ),
                            ),

                            SizedBox(height: 24),

                            // Send OTP button
                            ElevatedButton(
                              onPressed: (_isLoading || _isRateLimited) ? null : _sendOtpForPasswordReset,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: Colors.white,
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                padding: EdgeInsets.symmetric(vertical: 14),
                              ),
                              child: _isLoading
                                  ? SizedBox(
                                      height: 24,
                                      width: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : Text(
                                      'GỬI MÃ OTP',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 1,
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 24),

                  // Quay lại đăng nhập
                  Center(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context)
                            .pushReplacementNamed(LoginScreen.routeName);
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: SafeText(
                        'Quay lại đăng nhập',
                        style: AppTextStyles.callout(context)
                            .withWeight(FontWeight.w500)
                            .withColor(colorScheme.primary),
                        maxLines: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Loading indicator overlay
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.4),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
