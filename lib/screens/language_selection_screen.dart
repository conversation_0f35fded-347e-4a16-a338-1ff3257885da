import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../providers/language_provider.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import 'login_screen.dart';

/// Màn hình chọn ngôn ngữ đầu tiên - trước khi login
/// Hỗ trợ thị trường Việt Nam và Châu Âu
class LanguageSelectionScreen extends StatefulWidget {
  static const String routeName = '/language-selection';

  const LanguageSelectionScreen({super.key});

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  String _selectedLanguage = 'vi'; // Mặc định Tiếng Việt
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Mặc định English cho first-time users
    _selectedLanguage = 'en';
  }

  Future<void> _selectLanguage(String languageCode) async {
    if (_selectedLanguage == languageCode) return;

    setState(() {
      _selectedLanguage = languageCode;
    });

    // Thay đổi ngôn ngữ app ngay lập tức
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    await languageProvider.changeLanguage(languageCode);
  }

  Future<void> _continueToLogin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Đảm bảo ngôn ngữ đã được lưu
      final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
      await languageProvider.changeLanguage(_selectedLanguage);

      if (mounted) {
        Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Có lỗi xảy ra: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _clearSession() async {
    try {
      // Clear Supabase session
      await Supabase.instance.client.auth.signOut();

      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Session cleared! App will restart.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getTitle() {
    switch (_selectedLanguage) {
      case 'vi':
        return 'Chọn ngôn ngữ của bạn';
      case 'en':
      default:
        return 'Choose your language';
    }
  }

  String _getSubtitle() {
    switch (_selectedLanguage) {
      case 'vi':
        return 'Chọn ngôn ngữ bạn muốn sử dụng';
      case 'en':
      default:
        return 'Select your preferred language to continue';
    }
  }

  String _getContinueText() {
    switch (_selectedLanguage) {
      case 'vi':
        return 'Tiếp tục';
      case 'en':
      default:
        return 'Continue';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight - 32,
                ),
                child: Column(
                  children: [
                    // Top section - Flexible để tự động điều chỉnh
                    Flexible(
                      flex: 3,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // App Logo và Title (compact)
                          _buildCompactHeader(),

                          const SizedBox(height: 20),

                          // Language Title
                          _buildLanguageTitle(),

                          const SizedBox(height: 16),

                          // Language Options
                          _buildLanguageOptions(),
                        ],
                      ),
                    ),

                    // Bottom section - Fixed height
                    Column(
                      children: [
                        // Continue Button
                        _buildContinueButton(),

                        // Debug: Clear session button (compact)
                        if (_isLoading == false) // Chỉ hiển thị khi không loading
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: TextButton(
                              onPressed: _clearSession,
                              child: const Text('Debug: Clear Session',
                                style: TextStyle(fontSize: 9, color: Colors.grey)),
                            ),
                          ),

                        const SizedBox(height: 12),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.restaurant,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),

        // App Name
        SafeText(
          'CookSpark',
          style: AppTextStyles.headlineLarge(context).copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactHeader() {
    return Column(
      children: [
        // App Icon (smaller)
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Icon(
            Icons.restaurant,
            size: 30,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),

        // App Name (smaller)
        SafeText(
          'CookSpark',
          style: AppTextStyles.headlineMedium(context).copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageTitle() {
    return Column(
      children: [
        SafeText(
          _getTitle(),
          style: AppTextStyles.headlineMedium(context).copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        SafeText(
          _getSubtitle(),
          style: AppTextStyles.bodyLarge(context).copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLanguageOptions() {
    return Column(
      children: [
        // Vietnamese
        _buildCompactLanguageOption(
          'vi',
          '🇻🇳',
          'Tiếng Việt',
          'Vietnamese',
        ),

        const SizedBox(height: 12),

        // English
        _buildCompactLanguageOption(
          'en',
          '🇺🇸',
          'English',
          'English',
        ),

        const SizedBox(height: 16),

        // Coming Soon Languages (compact) - Ẩn để tiết kiệm không gian
        // _buildComingSoonOption('🇫🇷', 'Français', 'Coming Soon'),
        // const SizedBox(height: 12),
        // _buildComingSoonOption('🇩🇪', 'Deutsch', 'Coming Soon'),
        // const SizedBox(height: 12),
        // _buildComingSoonOption('🇪🇸', 'Español', 'Coming Soon'),
      ],
    );
  }

  Widget _buildCompactLanguageOption(String code, String flag, String name, String englishName) {
    final isSelected = _selectedLanguage == code;

    return GestureDetector(
      onTap: () => _selectLanguage(code),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // Flag (to hơn)
            Text(
              flag,
              style: const TextStyle(fontSize: 28),
            ),
            const SizedBox(width: 12),

            // Language Name
            Expanded(
              child: SafeText(
                name,
                style: AppTextStyles.titleMedium(context).copyWith(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected ? Theme.of(context).primaryColor : null,
                ),
              ),
            ),

            // Selection Indicator
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(String code, String flag, String name, String englishName) {
    final isSelected = _selectedLanguage == code;

    return GestureDetector(
      onTap: () => _selectLanguage(code),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            // Flag (to hơn)
            Text(
              flag,
              style: const TextStyle(fontSize: 32),
            ),
            const SizedBox(width: 16),

            // Language Name
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SafeText(
                    name,
                    style: AppTextStyles.titleMedium(context).copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected ? Theme.of(context).primaryColor : null,
                    ),
                  ),
                  if (name != englishName)
                    SafeText(
                      englishName,
                      style: AppTextStyles.bodyMedium(context).copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),

            // Selection Indicator
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonOption(String flag, String name, String status) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // Flag (to hơn)
          Text(
            flag,
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(width: 16),
          
          // Language Name
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SafeText(
                  name,
                  style: AppTextStyles.titleMedium(context).copyWith(
                    color: Colors.grey[500],
                  ),
                ),
                SafeText(
                  status,
                  style: AppTextStyles.bodySmall(context).copyWith(
                    color: Colors.grey[400],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          
          // Disabled Indicator
          Icon(
            Icons.radio_button_unchecked,
            color: Colors.grey[400],
            size: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _continueToLogin,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : SafeText(
                _getContinueText(),
                style: AppTextStyles.labelLarge(context).copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}
