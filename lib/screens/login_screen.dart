import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../services/supabase_auth_service.dart';
import '../providers/theme_provider.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';
import 'register_screen.dart';
import 'new_home_screen.dart';
import 'forgot_password_otp_screen.dart';
import 'debug_oauth_screen.dart';
import 'dart:math' as math;

class LoginScreen extends StatefulWidget {
  static const routeName = '/login';

  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authService = SupabaseAuthService();
  bool _isLoading = false;
  String? _errorMessage;
  bool _rememberMe = false;
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    // Không cần kiểm tra trạng thái đăng nhập ở đây nữa
    // vì AuthWrapper đã xử lý logic này
    print('LoginScreen khởi tạo - sẵn sàng để đăng nhập');
  }

  void _navigateToHome() {
    if (mounted) {
      Navigator.of(context).pushReplacementNamed(NewHomeScreen.routeName);
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('=== ĐĂNG NHẬP TỪ MÀN HÌNH ĐĂNG NHẬP ===');
      print('Email: ${_emailController.text.trim()}');
      print('Độ dài mật khẩu: ${_passwordController.text.length}');

      final result = await _authService.signInWithEmailAndPassword(
        _emailController.text.trim(),
        _passwordController.text,
      );

      print('Kết quả đăng nhập: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('Đăng nhập thành công, lưu trạng thái và chuyển màn hình');

        // Lưu trạng thái ghi nhớ đăng nhập
        await _authService.rememberLogin(_rememberMe);

        // Lấy thông tin người dùng
        await _authService.getCurrentUserData();

        // Đăng nhập thành công, chuyển đến màn hình chính
        _navigateToHome();
      } else {
        print('Đăng nhập thất bại, hiển thị lỗi: ${result['message']}');

        setState(() {
          _errorMessage = result['message'];
        });

        // Hiển thị thông báo lỗi dạng snackbar
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage ?? (l10n?.loginFailed ?? 'Đăng nhập thất bại')),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('Lỗi đăng nhập tại UI: $e');
      print('Loại lỗi: ${e.runtimeType}');
      print('Chi tiết: ${e.toString()}');

      if (mounted) {
        final l10n = AppLocalizations.of(context);
        setState(() {
          _errorMessage = l10n?.errorOccurredTryAgain ?? 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
        });

        // Hiển thị thông báo lỗi dạng snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('=== KẾT THÚC ĐĂNG NHẬP TỪ MÀN HÌNH ĐĂNG NHẬP ===');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loginWithGoogle() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('Đang chuẩn bị đăng nhập Google từ UI');

      // Thêm thông báo cho người dùng
      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n?.openingGoogleLogin ?? 'Đang mở cửa sổ đăng nhập Google...'),
          duration: Duration(seconds: 3),
          backgroundColor: Colors.blue,
        ),
      );

      final result = await _authService.signInWithGoogle();

      if (mounted) {
        if (result['success']) {
          // Đăng nhập thành công - navigate đến home screen
          print('Google sign in successful, navigating to home...');
          Navigator.of(context).pushReplacementNamed(NewHomeScreen.routeName);
        } else {
          // Đăng nhập thất bại, hiển thị thông báo lỗi
          final l10n = AppLocalizations.of(context);
          setState(() {
            _errorMessage = result['message'] ??
                (l10n?.loginFailedTryAgain ?? 'Đăng nhập thất bại. Vui lòng thử lại sau.');
          });

          // Hiển thị thông báo lỗi dạng snackbar với action thử lại
          String errorMessage = _errorMessage!;
          if (errorMessage.contains('cancelled') || errorMessage.contains('hủy')) {
            errorMessage = l10n?.loginCancelled ?? 'Đăng nhập đã bị hủy. Vui lòng thử lại.';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: errorMessage.contains('hủy') ? Colors.orange : Colors.red,
              duration: Duration(seconds: 5),
              action: SnackBarAction(
                label: l10n?.tryAgainButton ?? 'Thử lại',
                textColor: Colors.white,
                onPressed: () => _loginWithGoogle(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      print('Lỗi đăng nhập Google tại UI: $e');

      if (mounted) {
        final l10n = AppLocalizations.of(context);
        setState(() {
          _errorMessage = l10n?.googleLoginError ??
              'Đã xảy ra lỗi khi đăng nhập với Google. Vui lòng thử lại sau.';
        });

        // Hiển thị thông báo lỗi dạng snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final Size screenSize = MediaQuery.of(context).size;
    final isDarkMode = themeProvider.darkMode;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            mini: true,
            heroTag: "debug_screen",
            onPressed: () => Navigator.pushNamed(context, DebugOAuthScreen.routeName),
            child: Icon(Icons.settings),
            tooltip: 'Debug Screen',
          ),
          SizedBox(height: 8),
          FloatingActionButton(
            mini: true,
            heroTag: "debug_info",
            onPressed: () => Navigator.pushNamed(context, DebugOAuthScreen.routeName),
            child: Icon(Icons.bug_report),
            tooltip: 'OAuth Debug',
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: screenSize.height * 0.04),

                // Logo và tiêu đề
                Align(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Container(
                        width: screenSize.width * 0.28,
                        height: screenSize.width * 0.28,
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: isDarkMode
                              ? []
                              : [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    blurRadius: 10,
                                    spreadRadius: 0,
                                    offset: Offset(0, 4),
                                  ),
                                ],
                        ),
                        padding: EdgeInsets.all(8),
                        child: Image.asset(
                          'assets/images/logo.png',
                          width: 120,
                          height: 120,
                        ),
                      ),
                      SizedBox(height: 24),
                      SafeText(
                        l10n?.loginTitle ?? 'Đăng Nhập',
                        style: AppTextStyles.title1(context).withWeight(FontWeight.w700).withColor(colorScheme.onSurface),
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      SafeText(
                        l10n?.welcomeBack ?? 'Chào mừng bạn quay trở lại ứng dụng',
                        style: AppTextStyles.callout(context).withColor(colorScheme.onSurface.withValues(alpha: 0.7)),
                        maxLines: 2,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: screenSize.height * 0.04),

                // Form đăng nhập
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Email field label
                      SafeText(
                        l10n?.emailAddress ?? 'Địa chỉ email',
                        style: AppTextStyles.footnote(context).withWeight(FontWeight.w500).withColor(colorScheme.onSurface),
                        maxLines: 1,
                      ),
                      SizedBox(height: 8),
                      // Email field
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        style: TextStyle(fontSize: 16),
                        decoration: InputDecoration(
                          hintText: '<EMAIL>',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          filled: false,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 16,
                            horizontal: 16,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: colorScheme.primary, width: 1.5),
                          ),
                          // Hiển thị cảnh báo lỗi bên dưới trường nhập liệu
                          errorStyle: TextStyle(
                            color: Colors.red[700],
                            fontSize: 13,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n?.pleaseEnterEmail ?? 'Vui lòng nhập email';
                          }
                          if (!value.contains('@') || !value.contains('.')) {
                            return l10n?.invalidEmail ?? 'Email không hợp lệ';
                          }
                          return null;
                        },
                      ),

                      SizedBox(height: 20),

                      // Password field label and forgot password
                      SafeRow(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SafeText(
                            l10n?.passwordField ?? 'Mật khẩu',
                            style: AppTextStyles.footnote(context).withWeight(FontWeight.w500).withColor(colorScheme.onSurface),
                            maxLines: 1,
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context)
                                  .pushNamed(ForgotPasswordOtpScreen.routeName);
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: colorScheme.primary,
                              padding: EdgeInsets.zero,
                              minimumSize: Size(0, 0),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: SafeText(
                              l10n?.forgotPasswordLink ?? 'Quên mật khẩu?',
                              style: AppTextStyles.footnote(context).withWeight(FontWeight.w500).withColor(colorScheme.primary),
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      // Password field
                      TextFormField(
                        controller: _passwordController,
                        obscureText: !_isPasswordVisible,
                        style: TextStyle(fontSize: 16),
                        decoration: InputDecoration(
                          hintText: '••••••••••',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          filled: false,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 16,
                            horizontal: 16,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[300]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: colorScheme.primary, width: 1.5),
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _isPasswordVisible
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: Colors.grey[500],
                              size: 20,
                            ),
                            onPressed: () {
                              setState(() {
                                _isPasswordVisible = !_isPasswordVisible;
                              });
                            },
                            splashRadius: 20,
                          ),
                          // Hiển thị cảnh báo lỗi bên dưới trường nhập liệu
                          errorStyle: TextStyle(
                            color: Colors.red[700],
                            fontSize: 13,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return l10n?.pleaseEnterPassword ?? 'Vui lòng nhập mật khẩu';
                          }
                          if (value.length < 6) {
                            return l10n?.passwordMinLength ?? 'Mật khẩu phải có ít nhất 6 ký tự';
                          }
                          return null;
                        },
                      ),

                      // Hiển thị lỗi chung nếu có (đặt sau các trường nhập liệu)
                      if (_errorMessage != null)
                        Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 10, horizontal: 12),
                          margin: EdgeInsets.only(top: 16),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: Colors.red.shade200, width: 1),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: Colors.red.shade700,
                                size: 18,
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      SizedBox(height: 16),

                      // Remember Me
                      SafeRow(
                        children: [
                          SizedBox(
                            height: 24,
                            width: 24,
                            child: Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                              activeColor: colorScheme.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          SizedBox(width: 8),
                          SafeText(
                            l10n?.rememberLogin ?? 'Ghi nhớ đăng nhập',
                            style: AppTextStyles.footnote(context).withColor(colorScheme.onSurface.withValues(alpha: 0.7)),
                            maxLines: 1,
                          ),
                        ],
                      ),

                      SizedBox(height: 30),

                      // Login Button
                      Container(
                        height: 52,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(26),
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.primary.withOpacity(0.3),
                              spreadRadius: 0,
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFF4870FF),
                              Color(0xFF3461FF),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _login,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(26),
                            ),
                            elevation: 0,
                            shadowColor: Colors.transparent,
                            minimumSize: Size(double.infinity, 52),
                            padding: EdgeInsets.zero,
                          ),
                          child: Ink(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(26),
                            ),
                            child: Container(
                              width: double.infinity,
                              height: 52,
                              alignment: Alignment.center,
                              child: _isLoading
                                  ? SizedBox(
                                      height: 24,
                                      width: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : SafeText(
                                      l10n?.loginTitle ?? 'Đăng nhập',
                                      style: AppTextStyles.callout(context).withWeight(FontWeight.w600).withColor(Colors.white),
                                      maxLines: 1,
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24),

                // Or sign in with
                Row(
                  children: [
                    Expanded(child: Divider(color: Colors.grey[300])),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: SafeText(
                        l10n?.orLoginWith ?? 'hoặc đăng nhập với',
                        style: AppTextStyles.footnote(context).withColor(Color(0xFF9E9E9E)),
                        maxLines: 1,
                      ),
                    ),
                    Expanded(child: Divider(color: Colors.grey[300])),
                  ],
                ),

                SizedBox(height: 24),

                // Google login button
                Container(
                  height: 52,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(26),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        spreadRadius: 0,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _loginWithGoogle,
                    icon: SizedBox(
                      width: 24,
                      height: 24,
                      child: SvgPicture.asset(
                        'assets/images/google.svg',
                        width: 24,
                        height: 24,
                        fit: BoxFit.contain,
                      ),
                    ),
                    label: SafeText(
                      l10n?.continueWithGoogle ?? 'Tiếp tục với Google',
                      style: AppTextStyles.callout(context).withWeight(FontWeight.w500).withColor(Color(0xFF212121)),
                      maxLines: 1,
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[300]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(26),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: Colors.white,
                      minimumSize: Size(double.infinity, 52),
                    ),
                  ),
                ),

                SizedBox(height: 24),

                // Create Account
                SafeRow(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SafeText(
                      l10n?.dontHaveAccount ?? 'Chưa có tài khoản? ',
                      style: AppTextStyles.footnote(context).withColor(colorScheme.onSurface.withValues(alpha: 0.7)),
                      maxLines: 1,
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context)
                            .pushNamed(RegisterScreen.routeName);
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: Size(0, 0),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        foregroundColor: colorScheme.primary,
                      ),
                      child: SafeText(
                        l10n?.createAccountLink ?? 'Tạo tài khoản',
                        style: AppTextStyles.footnote(context).withWeight(FontWeight.bold).withColor(colorScheme.primary),
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: screenSize.height * 0.05),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Lớp hiệu ứng được giữ lại nhưng không sử dụng trong thiết kế mới
class StarfieldPainter extends CustomPainter {
  final bool isDarkMode;

  const StarfieldPainter({this.isDarkMode = false});

  @override
  void paint(Canvas canvas, Size size) {
    final random = math.Random(42); // Seed cố định để hiệu ứng nhất quán
    final paint = Paint()
      ..color = (isDarkMode ? Colors.white : Colors.white).withOpacity(0.6)
      ..style = PaintingStyle.fill;

    // Vẽ khoảng 50 điểm sao nhỏ ngẫu nhiên
    for (int i = 0; i < 50; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 1.5 + 0.5;
      final opacity = random.nextDouble() * 0.5 + 0.3;

      paint.color =
          (isDarkMode ? Colors.white : Colors.white).withOpacity(opacity);
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(StarfieldPainter oldDelegate) =>
      oldDelegate.isDarkMode != isDarkMode;
}
