import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../services/youtube_service.dart';
import '../services/saved_video_service.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../widgets/save_video_notification.dart';
import '../widgets/feature_development_dialog.dart';
import 'youtube_player_screen.dart';
import 'weekly_menu_screen.dart';

/// Màn hình hiển thị kết quả gợi ý món ăn từ YouTube
class MealSuggestionResultsScreen extends StatefulWidget {
  static const String routeName = '/meal-suggestion-results';

  const MealSuggestionResultsScreen({super.key});

  @override
  State<MealSuggestionResultsScreen> createState() => _MealSuggestionResultsScreenState();
}

class _MealSuggestionResultsScreenState extends State<MealSuggestionResultsScreen> {
  final YouTubeService _youtubeService = YouTubeService();
  final SavedVideoService _savedVideoService = SavedVideoService();

  List<YouTubeVideo> _videos = [];
  bool _isLoading = true;
  String? _error;
  MealSuggestionQuery? _query;
  String _searchQuery = '';

  // YouTube pagination
  String? _nextPageToken; // Token để load trang tiếp theo
  int _currentPage = 1; // Trang hiện tại (bắt đầu từ 1)

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    print('🔍🔍🔍 didChangeDependencies được gọi');

    // Lấy query từ arguments
    final args = ModalRoute.of(context)?.settings.arguments;
    print('🔍 Arguments: $args');
    print('🔍 Arguments type: ${args.runtimeType}');
    print('🔍 Current _query: $_query');

    if (args is MealSuggestionQuery && _query == null) {
      _query = args;
      print('🔍 Đã set query, bắt đầu search videos...');
      _searchVideos();
    } else {
      print('🔍 Không search videos - args is MealSuggestionQuery: ${args is MealSuggestionQuery}, _query == null: ${_query == null}');
    }
  }

  Future<void> _searchVideos() async {
    if (_query == null) return;

    print('🔍 Bắt đầu tìm kiếm video với query: $_query');

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      print('🔍 Đang gọi YouTube service...');
      final result = await _youtubeService.searchVideos(_query!).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ YouTube API timeout sau 30 giây');
          return {
            'success': false,
            'message': 'Timeout khi tìm kiếm video. Vui lòng thử lại.',
            'videos': <YouTubeVideo>[],
          };
        },
      );

      print('🔍 Kết quả từ YouTube service: ${result['success']}');
      if (result['success']) {
        print('🔍 Số video tìm được: ${(result['videos'] as List).length}');
      } else {
        print('❌ Lỗi: ${result['message']}');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          if (result['success']) {
            final videos = result['videos'] as List<YouTubeVideo>;
            _videos = videos;
            _searchQuery = result['query'];

            // Lưu nextPageToken để load trang tiếp theo
            _nextPageToken = result['nextPageToken'];
            _currentPage = 1;
            print('🔍 Trang 1: ${videos.length} videos, nextPageToken: $_nextPageToken');
            print('📄 YouTube Pagination: Sẵn sàng load trang 2 với pageToken');
            print('🔍 DEBUG: result keys = ${result.keys.toList()}');
            print('🔍 DEBUG: nextPageToken type = ${result['nextPageToken'].runtimeType}');
          } else {
            _error = result['message'];
          }
        });
      }
    } catch (e) {
      print('❌ Exception trong _searchVideos: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = 'Lỗi khi tải video: $e';
        });
      }
    }
  }

  /// Refresh để load trang tiếp theo từ YouTube (pagination thực sự)
  Future<void> _refreshWithNewVideos() async {
    if (_query == null) return;

    // Kiểm tra có nextPageToken không
    if (_nextPageToken == null) {
      print('📄 Không có trang tiếp theo, đã hết video');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = 'Đã hết video cho chủ đề này. Hãy thử tìm kiếm món ăn khác hoặc thay đổi nguyên liệu.';
        });
      }
      return;
    }

    print('📄 Bắt đầu load trang ${_currentPage + 1} với YouTube pageToken: $_nextPageToken');
    print('📄 SẼ SỬ DỤNG YOUTUBE PAGINATION THỰC SỰ (không phải keyword expansion)');

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Gọi YouTube service với pageToken (pagination thực sự)
      final result = await _youtubeService.searchVideosWithPagination(_query!, _nextPageToken!).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ YouTube API timeout sau 30 giây');
          return {
            'success': false,
            'message': 'Timeout khi tìm kiếm video. Vui lòng thử lại.',
            'videos': <YouTubeVideo>[],
          };
        },
      );

      print('📄 Kết quả từ YouTube pagination: ${result['success']}');

      if (result['success']) {
        final videos = result['videos'] as List<YouTubeVideo>;
        print('📄 Tìm được ${videos.length} video từ trang ${_currentPage + 1}');

        if (mounted) {
          setState(() {
            _isLoading = false;
            _videos = videos;
            _currentPage++;

            // Cập nhật nextPageToken cho lần refresh tiếp theo
            _nextPageToken = result['nextPageToken'];

            print('✅ Load trang $_currentPage thành công');
            print('📄 NextPageToken cho trang tiếp theo: ${_nextPageToken ?? "null (hết trang)"}');
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _error = result['message'];
          });
        }
      }
    } catch (e) {
      print('❌ Exception trong _refreshWithNewVideos: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = 'Lỗi khi tải trang tiếp theo: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).colorScheme.surface
          : const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const SafeText('Gợi ý món ăn'),
        backgroundColor: isDarkMode
            ? Theme.of(context).colorScheme.surfaceContainerHigh
            : Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDarkMode
                ? Theme.of(context).colorScheme.onSurface
                : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isDarkMode
                  ? Theme.of(context).colorScheme.onSurface
                  : Colors.black87,
            ),
            onPressed: _refreshWithNewVideos,
            tooltip: 'Tìm video mới',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }
    
    if (_error != null) {
      return _buildErrorState();
    }
    
    if (_videos.isEmpty) {
      return _buildEmptyState();
    }
    
    return _buildVideoList();
  }

  Widget _buildLoadingState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Theme.of(context).colorScheme.surfaceContainerHigh
                : Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI Icon với animation
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.2),
                      Theme.of(context).primaryColor.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.auto_awesome,
                  size: 48,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 24),

              // Main loading text
              SafeText(
                'AI đang tìm kiếm món ăn phù hợp...',
                style: AppTextStyles.title3(context).copyWith(
                  color: isDarkMode
                      ? Theme.of(context).colorScheme.onSurface
                      : Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Subtitle
              SafeText(
                'Vui lòng đợi trong giây lát',
                style: AppTextStyles.body(context).copyWith(
                  color: isDarkMode
                      ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)
                      : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Progress indicator
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.grey[900]?.withOpacity(0.8)
                : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color: Colors.red.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error Icon
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.red.withOpacity(0.2),
                      Colors.red.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red[400],
                ),
              ),
              const SizedBox(height: 24),

              // Error title
              SafeText(
                'Có lỗi xảy ra',
                style: AppTextStyles.title3(context).copyWith(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Error message
              SafeText(
                _error ?? 'Không thể tải danh sách video',
                style: AppTextStyles.body(context).copyWith(
                  color: isDarkMode ? Colors.white70 : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Retry button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _searchVideos,
                  icon: const Icon(Icons.refresh),
                  label: const SafeText('Thử lại'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.grey[900]?.withOpacity(0.8)
                : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color: (isDarkMode ? Colors.white54 : Colors.grey[400])!.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Empty Icon
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      (isDarkMode ? Colors.white54 : Colors.grey[400])!.withOpacity(0.2),
                      (isDarkMode ? Colors.white54 : Colors.grey[400])!.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.search_off,
                  size: 48,
                  color: isDarkMode ? Colors.white54 : Colors.grey[400],
                ),
              ),
              const SizedBox(height: 24),

              // Empty title
              SafeText(
                'Không tìm thấy video phù hợp',
                style: AppTextStyles.title3(context).copyWith(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Empty message
              SafeText(
                'Hãy thử thay đổi tiêu chí tìm kiếm',
                style: AppTextStyles.body(context).copyWith(
                  color: isDarkMode ? Colors.white70 : Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Back button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: const SafeText('Tìm kiếm lại'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return CustomScrollView(
      slivers: [
        // Header với thông tin tìm kiếm
        SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SafeText(
                        'AI đã tìm thấy ${_videos.length} video phù hợp${_currentPage > 1 ? ' (trang $_currentPage)' : ''}',
                        style: AppTextStyles.subhead(context).copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SafeText(
                        'Dựa trên sở thích và yêu cầu của bạn',
                        style: AppTextStyles.footnote(context).copyWith(
                          color: Theme.of(context).primaryColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Danh sách video
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final video = _videos[index];
              return _buildVideoCard(video, isDarkMode);
            },
            childCount: _videos.length,
          ),
        ),
        
        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 20),
        ),
      ],
    );
  }

  Widget _buildVideoCard(YouTubeVideo video, bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Theme.of(context).colorScheme.surfaceContainer
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isDarkMode
            ? Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openVideo(video),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 120,
                    height: 90,
                    color: Colors.grey[300],
                    child: Stack(
                      children: [
                        Image.network(
                          video.thumbnailUrl,
                          width: 120,
                          height: 90,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 90,
                              color: Colors.grey[300],
                              child: Icon(
                                Icons.play_circle_outline,
                                size: 32,
                                color: Colors.grey[600],
                              ),
                            );
                          },
                        ),
                        Positioned(
                          bottom: 4,
                          right: 4,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: SafeText(
                              video.formattedDuration,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Video info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SafeText(
                        video.title,
                        style: AppTextStyles.subhead(context).copyWith(
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      SafeText(
                        video.channelTitle,
                        style: AppTextStyles.footnote(context).copyWith(
                          color: isDarkMode ? Colors.white60 : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      SafeText(
                        video.formattedViewCount,
                        style: AppTextStyles.footnote(context).copyWith(
                          color: isDarkMode ? Colors.white60 : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _openVideo(video),
                              icon: const Icon(Icons.play_arrow, size: 16),
                              label: const SafeText('Xem'),
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                minimumSize: const Size(0, 32),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => FeatureDevelopmentDialog.showAddToWeeklyMenuDialog(context),
                              icon: const Icon(Icons.add, size: 16),
                              label: const SafeText('Lưu'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                minimumSize: const Size(0, 32),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openVideo(YouTubeVideo video) {
    print('🎬🎬🎬 _openVideo được gọi');
    print('🎬 Video ID: ${video.id}');
    print('🎬 Video title: ${video.title}');
    print('🎬 Video thumbnail: ${video.thumbnailUrl}');
    print('🎬 Navigating to YouTubePlayerScreen...');

    Navigator.of(context).pushNamed(
      YouTubePlayerScreen.routeName,
      arguments: video,
    );

    print('🎬 Navigation completed');
  }

  void _addToWeeklyMenu(YouTubeVideo video) async {
    print('💾💾💾 _addToWeeklyMenu được gọi');
    print('💾 Video ID: ${video.id}');
    print('💾 Video title: ${video.title}');

    try {
      // Hiển thị loading với custom notification
      SaveVideoNotificationHelper.show(
        context,
        message: 'Đang lưu video "${video.title}"...',
        isSuccess: true,
      );

      print('💾 Đang gọi _savedVideoService.saveVideo...');
      // Lưu video vào database
      final result = await _savedVideoService.saveVideo(video);
      print('💾 Kết quả lưu video: $result');

      if (mounted) {
        // Ẩn loading notification
        SaveVideoNotificationHelper.hide();

        // Hiển thị kết quả với custom notification
        SaveVideoNotificationHelper.show(
          context,
          message: result['message'] ?? 'Video đã được thêm vào danh sách yêu thích',
          isSuccess: result['success'] ?? false,
          onViewList: result['success'] == true ? () {
            Navigator.of(context).pushNamed('/saved-videos');
          } : null,
        );
      }
    } catch (e) {
      print('❌ Lỗi lưu video: $e');
      if (mounted) {
        // Ẩn loading notification
        SaveVideoNotificationHelper.hide();

        // Hiển thị lỗi với custom notification
        SaveVideoNotificationHelper.show(
          context,
          message: 'Không thể lưu video. Vui lòng kiểm tra kết nối mạng và thử lại.',
          isSuccess: false,
        );
      }
    }
  }
}
