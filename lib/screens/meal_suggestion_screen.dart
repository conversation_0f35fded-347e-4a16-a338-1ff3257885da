import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_profile.dart';
import '../providers/user_profile_provider.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';
import 'meal_suggestion_results_screen.dart';

/// <PERSON><PERSON>n hình hệ thống câu hỏi gợi ý món ăn
class MealSuggestionScreen extends StatefulWidget {
  static const String routeName = '/meal-suggestion';

  const MealSuggestionScreen({super.key});

  @override
  State<MealSuggestionScreen> createState() => _MealSuggestionScreenState();
}

class _MealSuggestionScreenState extends State<MealSuggestionScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  // Dữ liệu câu hỏi
  MealType? _selectedMealType;
  bool _isVegetarian = false;
  final Set<MainIngredient> _selectedIngredients = {};
  bool _autoSuggest = true; // Mặc định tích để AI tự động gợi ý

  @override
  void initState() {
    super.initState();
    _setDefaultMealType();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Tự động chọn meal type dựa vào thời gian hiện tại
  void _setDefaultMealType() {
    final now = DateTime.now();
    final hour = now.hour;

    if (hour >= 5 && hour < 11) {
      // 5:00 - 10:59: Bữa sáng
      _selectedMealType = MealType.breakfast;
    } else if (hour >= 11 && hour < 17) {
      // 11:00 - 16:59: Bữa trưa
      _selectedMealType = MealType.lunch;
    } else {
      // 17:00 - 4:59: Bữa tối
      _selectedMealType = MealType.dinner;
    }
  }

  void _nextPage() {
    // Nếu đang ở trang vegetarian và chọn ăn chay, skip trang ingredients
    if (_currentPage == 1 && _isVegetarian) {
      _generateSuggestions();
      return;
    }

    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _generateSuggestions();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _generateSuggestions() {
    print('🔍🔍🔍 _generateSuggestions được gọi');

    if (_selectedMealType == null) {
      print('❌ Chưa chọn loại bữa ăn');
      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(l10n?.pleaseSelectMealType ?? 'Vui lòng chọn loại bữa ăn'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final query = MealSuggestionQuery(
      mealType: _selectedMealType!,
      isVegetarian: _isVegetarian,
      preferredIngredients: _selectedIngredients.toList(),
      autoSuggest: _autoSuggest,
    );

    print('🔍 Tạo query: ${query.toJson()}');
    print('🔍 Đang navigate đến MealSuggestionResultsScreen...');

    Navigator.of(context).pushNamed(
      MealSuggestionResultsScreen.routeName,
      arguments: query,
    );

    print('🔍 Navigation completed');
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).colorScheme.surface
          : Colors.white,
      appBar: AppBar(
        title: SafeText(l10n?.mealSuggestionTitle ?? 'Gợi ý món ăn'),
        backgroundColor: isDarkMode
            ? Theme.of(context).colorScheme.surfaceContainerHigh
            : Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDarkMode
                ? Theme.of(context).colorScheme.onSurface
                : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: (_currentPage + 1) / 3,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                SafeText(
                  '${_currentPage + 1}/3',
                  style: AppTextStyles.body(context).copyWith(
                    color: isDarkMode ? Colors.white70 : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Page content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              children: [
                _buildMealTypePage(),
                _buildVegetarianPage(),
                _buildIngredientsPage(),
              ],
            ),
          ),
          
          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                if (_currentPage > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousPage,
                      child: SafeText(l10n?.back ?? 'Quay lại'),
                    ),
                  ),
                if (_currentPage > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _nextPage,
                    child: SafeText(
                      (_currentPage == 2 || (_currentPage == 1 && _isVegetarian))
                        ? (l10n?.findDishes ?? 'Tìm món ăn')
                        : (l10n?.next ?? 'Tiếp theo')
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealTypePage() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          SafeText(
            l10n?.whatMealToPrepare ?? 'Bạn muốn chuẩn bị bữa nào?',
            style: AppTextStyles.title2(context).copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          SafeText(
            l10n?.chooseMealTypeDescription ?? 'Chọn loại bữa ăn để chúng tôi gợi ý món phù hợp',
            style: AppTextStyles.body(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          ...MealType.values.map((mealType) => _buildMealTypeOption(mealType)),
        ],
      ),
    );
  }

  Widget _buildMealTypeOption(MealType mealType) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = _selectedMealType == mealType;
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    // Modern colors for meal type options
    Color getBorderColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.outline : const Color(0xFFE0E0E0));
    }

    Color getBackgroundColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primaryContainer.withValues(alpha: 0.3) : colorScheme.primary.withValues(alpha: 0.08))
          : (isDarkMode ? colorScheme.surfaceContainer : const Color(0xFFF8F9FA));
    }

    Color getEmojiBackgroundColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primaryContainer.withValues(alpha: 0.5) : colorScheme.primary.withValues(alpha: 0.12))
          : (isDarkMode ? colorScheme.surfaceContainerHigh : const Color(0xFFEEEEEE));
    }

    Color getTextColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.onSurface : const Color(0xFF1A1A1A));
    }

    Color getIconColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.onSurface.withValues(alpha: 0.6) : const Color(0xFF666666));
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedMealType = mealType;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: getBorderColor(),
              width: isSelected ? 2 : 1,
            ),
            color: getBackgroundColor(),
            boxShadow: isDarkMode && isSelected ? [
              BoxShadow(
                color: const Color(0xFF6366F1).withValues(alpha: 0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ] : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: getEmojiBackgroundColor(),
                  borderRadius: BorderRadius.circular(12),
                  border: isSelected && isDarkMode ? Border.all(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                    width: 1,
                  ) : null,
                ),
                child: SafeText(
                  mealType.emoji,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SafeText(
                  _getMealTypeName(context, mealType),
                  style: AppTextStyles.body(context).copyWith(
                    color: getTextColor(),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: getIconColor(),
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVegetarianPage() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          SafeText(
            l10n?.areYouVegetarian ?? 'Bạn có ăn chay không?',
            style: AppTextStyles.title2(context).copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          SafeText(
            l10n?.vegetarianFilterDescription ?? 'Điều này giúp chúng tôi lọc ra những món ăn phù hợp',
            style: AppTextStyles.body(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          _buildVegetarianOption(false, l10n?.no ?? 'Không', l10n?.eatAllFoods ?? 'Tôi ăn tất cả các loại thực phẩm', '🍖'),
          _buildVegetarianOption(true, l10n?.yes ?? 'Có', l10n?.eatOnlyVegetarian ?? 'Tôi chỉ ăn thực phẩm chay', '🥬'),
        ],
      ),
    );
  }

  Widget _buildVegetarianOption(bool value, String title, String description, String emoji) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = _isVegetarian == value;
    final colorScheme = Theme.of(context).colorScheme;

    // Modern colors for vegetarian options
    Color getBorderColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.outline : const Color(0xFFE0E0E0));
    }

    Color getBackgroundColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primaryContainer.withValues(alpha: 0.3) : colorScheme.primary.withValues(alpha: 0.08))
          : (isDarkMode ? colorScheme.surfaceContainer : const Color(0xFFF8F9FA));
    }

    Color getEmojiBackgroundColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primaryContainer.withValues(alpha: 0.5) : colorScheme.primary.withValues(alpha: 0.12))
          : (isDarkMode ? colorScheme.surfaceContainerHigh : const Color(0xFFEEEEEE));
    }

    Color getTitleColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.onSurface : const Color(0xFF1A1A1A));
    }

    Color getDescriptionColor() {
      return isDarkMode
          ? colorScheme.onSurface.withValues(alpha: 0.7)
          : const Color(0xFF666666);
    }

    Color getIconColor() {
      return isSelected
          ? (isDarkMode ? colorScheme.primary : colorScheme.primary)
          : (isDarkMode ? colorScheme.onSurface.withValues(alpha: 0.6) : const Color(0xFF666666));
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _isVegetarian = value;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: getBorderColor(),
              width: isSelected ? 2 : 1,
            ),
            color: getBackgroundColor(),
            boxShadow: isDarkMode && isSelected ? [
              BoxShadow(
                color: const Color(0xFF6366F1).withValues(alpha: 0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ] : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: getEmojiBackgroundColor(),
                  borderRadius: BorderRadius.circular(12),
                  border: isSelected && isDarkMode ? Border.all(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                    width: 1,
                  ) : null,
                ),
                child: SafeText(
                  emoji,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SafeText(
                      title,
                      style: AppTextStyles.body(context).copyWith(
                        color: getTitleColor(),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    SafeText(
                      description,
                      style: AppTextStyles.subhead(context).copyWith(
                        color: getDescriptionColor(),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: getIconColor(),
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIngredientsPage() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          SafeText(
            l10n?.whatMainIngredients ?? 'Nguyên liệu chính bạn muốn là gì?',
            style: AppTextStyles.title2(context).copyWith(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          SafeText(
            l10n?.chooseIngredientsDescription ?? 'Chọn một hoặc nhiều nguyên liệu, hoặc để AI tự gợi ý',
            style: AppTextStyles.body(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Auto suggest button
          _buildAutoSuggestButton(),
          const SizedBox(height: 24),

          SafeText(
            l10n?.orChooseSpecificIngredients ?? 'Hoặc chọn nguyên liệu cụ thể:',
            style: AppTextStyles.subhead(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          
          // Ingredients grid
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: MainIngredient.values.length,
              itemBuilder: (context, index) {
                final ingredient = MainIngredient.values[index];
                return _buildIngredientCard(ingredient);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoSuggestButton() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);
    
    return Container(
      width: double.infinity,
      child: InkWell(
        onTap: () {
          setState(() {
            _autoSuggest = !_autoSuggest;
            if (_autoSuggest) {
              _selectedIngredients.clear();
            }
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _autoSuggest
                  ? (isDarkMode ? const Color(0xFF6366F1) : Theme.of(context).primaryColor)
                  : (isDarkMode ? const Color(0xFF404040) : const Color(0xFFE0E0E0)),
              width: _autoSuggest ? 2 : 1,
            ),
            color: _autoSuggest
                ? (isDarkMode ? const Color(0xFF1A1A2E) : Theme.of(context).primaryColor.withValues(alpha: 0.08))
                : (isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFF8F9FA)),
            boxShadow: isDarkMode && _autoSuggest ? [
              BoxShadow(
                color: const Color(0xFF6366F1).withValues(alpha: 0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ] : null,
          ),
          child: Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: _autoSuggest
                    ? (isDarkMode ? const Color(0xFF8B8BF1) : Theme.of(context).primaryColor)
                    : (isDarkMode ? const Color(0xFF999999) : const Color(0xFF666666)),
                size: 28,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SafeText(
                      l10n?.letAiAutoSuggest ?? 'Để AI tự động gợi ý',
                      style: AppTextStyles.body(context).copyWith(
                        color: _autoSuggest
                            ? (isDarkMode ? const Color(0xFF8B8BF1) : Theme.of(context).primaryColor)
                            : (isDarkMode ? const Color(0xFFE6E6E6) : const Color(0xFF1A1A1A)),
                        fontWeight: _autoSuggest ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    SafeText(
                      l10n?.systemWillChooseBestIngredients ?? 'Hệ thống sẽ chọn nguyên liệu phù hợp nhất',
                      style: AppTextStyles.subhead(context).copyWith(
                        color: isDarkMode ? const Color(0xFFB3B3B3) : const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _autoSuggest ? Icons.check_circle : Icons.radio_button_unchecked,
                color: _autoSuggest
                    ? (isDarkMode ? const Color(0xFF8B8BF1) : Theme.of(context).primaryColor)
                    : (isDarkMode ? const Color(0xFF999999) : const Color(0xFF666666)),
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIngredientCard(MainIngredient ingredient) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSelected = _selectedIngredients.contains(ingredient);
    final isDisabled = _autoSuggest;
    final colorScheme = Theme.of(context).colorScheme;

    // Modern colors for dark mode
    Color getBorderColor() {
      if (isSelected) {
        return isDarkMode
            ? colorScheme.primary
            : colorScheme.primary;
      }
      return isDarkMode
          ? colorScheme.outline
          : const Color(0xFFE0E0E0);
    }

    Color getBackgroundColor() {
      if (isDisabled) {
        return isDarkMode
            ? colorScheme.surfaceContainerLowest
            : const Color(0xFFF5F5F5);
      }
      if (isSelected) {
        return isDarkMode
            ? colorScheme.primaryContainer.withValues(alpha: 0.3)
            : colorScheme.primary.withValues(alpha: 0.08);
      }
      return isDarkMode
          ? colorScheme.surfaceContainer
          : Colors.white;
    }

    Color getTextColor() {
      if (isDisabled) {
        return isDarkMode
            ? colorScheme.onSurface.withValues(alpha: 0.4)
            : const Color(0xFF999999);
      }
      if (isSelected) {
        return isDarkMode
            ? colorScheme.primary
            : colorScheme.primary;
      }
      return isDarkMode
          ? colorScheme.onSurface
          : const Color(0xFF1A1A1A);
    }

    return InkWell(
      onTap: isDisabled ? null : () {
        setState(() {
          if (isSelected) {
            _selectedIngredients.remove(ingredient);
          } else {
            _selectedIngredients.add(ingredient);
          }
        });
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: getBorderColor(),
            width: isSelected ? 2 : 1,
          ),
          color: getBackgroundColor(),
          boxShadow: isDarkMode && isSelected ? [
            BoxShadow(
              color: const Color(0xFF6366F1).withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SafeText(
              ingredient.emoji,
              style: TextStyle(
                fontSize: 32,
                color: isDisabled
                    ? (isDarkMode ? const Color(0xFF666666) : const Color(0xFF999999))
                    : null,
              ),
            ),
            const SizedBox(height: 8),
            SafeText(
              ingredient.displayName,
              style: AppTextStyles.subhead(context).copyWith(
                color: getTextColor(),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Lấy tên localized cho meal type
  String _getMealTypeName(BuildContext context, MealType mealType) {
    final l10n = AppLocalizations.of(context);
    switch (mealType) {
      case MealType.breakfast:
        return l10n?.breakfast ?? 'Bữa sáng';
      case MealType.lunch:
        return l10n?.lunch ?? 'Bữa trưa';
      case MealType.dinner:
        return l10n?.dinner ?? 'Bữa tối';
    }
  }
}
