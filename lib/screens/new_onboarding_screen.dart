import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_profile.dart';
import '../providers/user_profile_provider.dart';
import '../providers/language_provider.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';
import 'new_home_screen.dart';

/// <PERSON><PERSON>n hình onboarding mới với 4 bước: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> h<PERSON>ớng nấu ăn
class NewOnboardingScreen extends StatefulWidget {
  static const String routeName = '/new-onboarding';

  const NewOnboardingScreen({super.key});

  @override
  State<NewOnboardingScreen> createState() => _NewOnboardingScreenState();
}

class _NewOnboardingScreenState extends State<NewOnboardingScreen> {
  final PageController _pageController = PageController();
  final TextEditingController _nameController = TextEditingController();
  int _currentPage = 0;
  
  // Dữ liệu onboarding
  String? _selectedLanguage;
  String? _userName;
  Gender? _selectedGender;
  CookingPreference? _selectedCookingPreference;
  bool _isLoading = false;

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 3) {
      // Validate current step
      if (!_canProceedToNextStep()) {
        _showValidationError();
        return;
      }
      
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceedToNextStep() {
    switch (_currentPage) {
      case 0: // Language step
        return _selectedLanguage != null;
      case 1: // Name step
        return _nameController.text.trim().isNotEmpty;
      case 2: // Gender step
        return _selectedGender != null;
      case 3: // Cooking preference step
        return _selectedCookingPreference != null;
      default:
        return false;
    }
  }

  void _showValidationError() {
    final l10n = AppLocalizations.of(context);
    String message;
    switch (_currentPage) {
      case 0:
        message = l10n?.pleaseSelectLanguage ?? 'Vui lòng chọn ngôn ngữ';
        break;
      case 1:
        message = l10n?.pleaseEnterName ?? 'Vui lòng nhập tên của bạn';
        break;
      case 2:
        message = l10n?.pleaseSelectGender ?? 'Vui lòng chọn giới tính';
        break;
      case 3:
        message = l10n?.pleaseSelectCookingStyle ?? 'Vui lòng chọn xu hướng nấu ăn';
        break;
      default:
        message = 'Vui lòng hoàn thành thông tin';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
      ),
    );
  }

  Future<void> _completeOnboarding() async {
    if (!_canProceedToNextStep()) {
      _showValidationError();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userProfileProvider = Provider.of<UserProfileProvider>(context, listen: false);
      
      await userProfileProvider.updateProfile(
        language: _selectedLanguage!,
        displayName: _nameController.text.trim(),
        gender: _selectedGender!,
        cookingPreference: _selectedCookingPreference!,
        isOnboardingCompleted: true,
      );

      if (mounted) {
        Navigator.of(context).pushReplacementNamed(NewHomeScreen.routeName);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Có lỗi xảy ra: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            
            // Content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  _buildLanguageStep(),
                  _buildNameStep(),
                  _buildGenderStep(),
                  _buildCookingPreferenceStep(),
                ],
              ),
            ),
            
            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(4, (index) {
          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < 3 ? 8 : 0),
              decoration: BoxDecoration(
                color: index <= _currentPage 
                    ? Theme.of(context).primaryColor 
                    : Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildLanguageStep() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.language,
            size: 80,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 32),
          SafeText(
            l10n?.chooseLanguage ?? 'Chọn ngôn ngữ / Choose Language',
            style: AppTextStyles.headlineMedium(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SafeText(
            l10n?.languageDescription ?? 'Bạn muốn sử dụng ngôn ngữ nào?\nWhich language would you like to use?',
            style: AppTextStyles.bodyLarge(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          _buildLanguageOption('vi', '🇻🇳 Tiếng Việt'),
          const SizedBox(height: 16),
          _buildLanguageOption('en', '🇺🇸 English'),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String value, String label) {
    final isSelected = _selectedLanguage == value;
    return GestureDetector(
      onTap: () async {
        setState(() {
          _selectedLanguage = value;
        });

        // Thay đổi ngôn ngữ app ngay lập tức
        final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
        await languageProvider.changeLanguage(value);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).primaryColor
                : Colors.grey.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
            ),
            const SizedBox(width: 12),
            SafeText(
              label,
              style: AppTextStyles.bodyLarge(context).copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? Theme.of(context).primaryColor : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNameStep() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 80,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 32),
          SafeText(
            l10n?.whatsYourName ?? (_selectedLanguage == 'en' ? 'What\'s your name?' : 'Tên bạn là gì?'),
            style: AppTextStyles.headlineMedium(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SafeText(
            l10n?.nameDescription ?? (_selectedLanguage == 'en'
                ? 'We\'d like to know what to call you'
                : 'Chúng tôi muốn biết nên gọi bạn là gì'),
            style: AppTextStyles.bodyLarge(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              hintText: l10n?.enterYourName ?? (_selectedLanguage == 'en' ? 'Enter your name' : 'Nhập tên của bạn'),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.person_outline),
            ),
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyLarge(context),
            onChanged: (value) {
              setState(() {
                _userName = value.trim();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGenderStep() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 80,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 32),
          SafeText(
            l10n?.whatsYourGender ?? (_selectedLanguage == 'en' ? 'What\'s your gender?' : 'Giới tính của bạn?'),
            style: AppTextStyles.headlineMedium(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SafeText(
            l10n?.genderDescription ?? (_selectedLanguage == 'en'
                ? 'This helps us provide better recommendations'
                : 'Điều này giúp chúng tôi đưa ra gợi ý phù hợp hơn'),
            style: AppTextStyles.bodyLarge(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          _buildGenderOption(Gender.male,
            l10n?.male ?? (_selectedLanguage == 'en' ? '👨 Male' : '👨 Nam')),
          const SizedBox(height: 16),
          _buildGenderOption(Gender.female,
            l10n?.female ?? (_selectedLanguage == 'en' ? '👩 Female' : '👩 Nữ')),
          const SizedBox(height: 16),
          _buildGenderOption(Gender.other,
            l10n?.other ?? (_selectedLanguage == 'en' ? '🏳️‍⚧️ Other' : '🏳️‍⚧️ Khác')),
        ],
      ),
    );
  }

  Widget _buildGenderOption(Gender gender, String label) {
    final isSelected = _selectedGender == gender;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedGender = gender;
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
            ),
            const SizedBox(width: 12),
            SafeText(
              label,
              style: AppTextStyles.bodyLarge(context).copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? Theme.of(context).primaryColor : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCookingPreferenceStep() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant,
            size: 80,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 32),
          SafeText(
            l10n?.cookingStyle ?? (_selectedLanguage == 'en'
                ? 'What\'s your cooking style?'
                : 'Bạn hướng tới sự đơn giản hay cầu kỳ khi nấu ăn?'),
            style: AppTextStyles.headlineMedium(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SafeText(
            l10n?.cookingStyleDescription ?? (_selectedLanguage == 'en'
                ? 'Choose your preferred cooking approach'
                : 'Chọn phong cách nấu ăn phù hợp với bạn'),
            style: AppTextStyles.bodyLarge(context),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          _buildCookingOption(CookingPreference.simple,
            l10n?.simple ?? (_selectedLanguage == 'en' ? '🍳 Simple & Quick' : '🍳 Đơn giản')),
          const SizedBox(height: 16),
          _buildCookingOption(CookingPreference.elaborate,
            l10n?.elaborate ?? (_selectedLanguage == 'en' ? '👨‍🍳 Elaborate & Detailed' : '👨‍🍳 Cầu kỳ')),
        ],
      ),
    );
  }

  Widget _buildCookingOption(CookingPreference preference, String label) {
    final isSelected = _selectedCookingPreference == preference;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCookingPreference = preference;
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
            ),
            const SizedBox(width: 12),
            SafeText(
              label,
              style: AppTextStyles.bodyLarge(context).copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? Theme.of(context).primaryColor : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: SafeText(
                  l10n?.back ?? (_selectedLanguage == 'en' ? 'Back' : 'Quay lại'),
                  style: AppTextStyles.labelLarge(context),
                ),
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextPage,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : SafeText(
                      _currentPage == 3
                          ? (l10n?.complete ?? (_selectedLanguage == 'en' ? 'Complete' : 'Hoàn thành'))
                          : (l10n?.next ?? (_selectedLanguage == 'en' ? 'Next' : 'Tiếp theo')),
                      style: AppTextStyles.labelLarge(context).copyWith(color: Colors.white),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
