import 'package:flutter/material.dart';
import '../services/supabase_auth_service.dart';
import '../widgets/otp_input_widget.dart';
import 'new_password_screen.dart';
import 'forgot_password_otp_screen.dart';

class OtpVerificationResetScreen extends StatefulWidget {
  static const routeName = '/otp-verification-reset';
  
  final String email;
  
  const OtpVerificationResetScreen({
    super.key,
    required this.email,
  });

  @override
  State<OtpVerificationResetScreen> createState() => _OtpVerificationResetScreenState();
}

class _OtpVerificationResetScreenState extends State<OtpVerificationResetScreen> {
  final _otpController = TextEditingController();
  final _authService = SupabaseAuthService();
  
  bool _isLoading = false;
  bool _isResending = false;
  String? _errorMessage;
  int _resendCountdown = 0;
  
  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  /// Validate OTP và chuyển màn hình (không verify ngay)
  Future<void> _validateAndProceed() async {
    // Validate format OTP
    final validation = _authService.validateOtpInput(widget.email, _otpController.text.trim());
    
    if (!validation['isValid']) {
      setState(() {
        _errorMessage = validation['message'];
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Simulation delay để user thấy loading (tạo cảm giác đang verify)
    await Future.delayed(Duration(milliseconds: 500));

    if (!mounted) return;

    print('UI: OTP format hợp lệ, chuyển đến màn hình đặt mật khẩu mới');
    print('Email: ${widget.email}');
    print('OTP: ${_otpController.text.trim()}');

    // Chuyển đến màn hình đặt mật khẩu mới với OTP chưa verify
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => NewPasswordScreen(
          email: widget.email,
          verifiedOtp: _otpController.text.trim(),
        ),
      ),
    );

    setState(() {
      _isLoading = false;
    });
  }

  /// Gửi lại mã OTP
  Future<void> _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      print('=== BẮT ĐẦU GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU ===');
      print('Email: ${widget.email}');

      final result = await _authService.resendOtpForPasswordReset(widget.email);

      print('Kết quả gửi lại OTP: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Gửi lại OTP thành công');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã gửi lại mã OTP đến email của bạn'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Bắt đầu countdown 60 giây
        _startResendCountdown();
      } else {
        print('UI: Gửi lại OTP thất bại');
        setState(() {
          _errorMessage = result['message'] ?? 'Không thể gửi lại mã OTP';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi ngoại lệ khi gửi lại OTP: $e');
      
      if (!mounted) return;
      
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
      print('=== KẾT THÚC GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU ===');
    }
  }

  /// Bắt đầu countdown cho nút gửi lại
  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60;
    });

    // Countdown timer
    Future.doWhile(() async {
      await Future.delayed(Duration(seconds: 1));
      if (!mounted) return false;
      
      setState(() {
        _resendCountdown--;
      });
      
      return _resendCountdown > 0;
    });
  }

  /// Quay lại màn hình nhập email
  void _goBackToForgotPassword() {
    Navigator.of(context).pushReplacementNamed(ForgotPasswordOtpScreen.routeName);
  }

  /// Lấy thông báo lỗi cho OTP input
  String? _getOtpErrorText() {
    final otpText = _otpController.text.trim();
    
    if (otpText.isNotEmpty && otpText.length < 6) {
      return 'Mã OTP phải có 6 chữ số';
    }
    
    // Kiểm tra lỗi từ server response
    if (_errorMessage != null && _errorMessage!.toLowerCase().contains('otp')) {
      return _errorMessage;
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Stack(
        children: [
          // Gradient background
          Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primaryContainer,
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 8,
            left: 8,
            child: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _goBackToForgotPassword,
            ),
          ),

          // Content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Header section
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Icon
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Icon(
                            Icons.mail_lock_outlined,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 24),
                        
                        Text(
                          'Xác minh mã OTP',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 12),
                        
                        Text(
                          'Chúng tôi đã gửi mã xác minh 6 chữ số đến',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withOpacity(0.9),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 4),
                        
                        Text(
                          widget.email,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  // OTP Input Section
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Nhập mã xác minh',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 24),

                          // OTP Input Widget
                          OtpInputWidget(
                            controller: _otpController,
                            enabled: !_isLoading,
                            errorText: _getOtpErrorText(),
                            onChanged: (value) {
                              // Reset error message khi người dùng nhập
                              if (_errorMessage != null) {
                                setState(() {
                                  _errorMessage = null;
                                });
                              }
                            },
                            onCompleted: (otp) {
                              // Auto proceed khi nhập đủ 6 số
                              if (otp.length == 6) {
                                _validateAndProceed();
                              }
                            },
                          ),
                          
                          SizedBox(height: 32),

                          // Verify Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _validateAndProceed,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 2,
                              ),
                              child: _isLoading
                                  ? SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : Text(
                                      'XÁC MINH',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 1,
                                      ),
                                    ),
                            ),
                          ),

                          SizedBox(height: 24),

                          // Resend section
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Không nhận được mã? ',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                              TextButton(
                                onPressed: (_isResending || _resendCountdown > 0) 
                                    ? null 
                                    : _resendOtp,
                                child: _isResending
                                    ? SizedBox(
                                        height: 16,
                                        width: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            theme.colorScheme.primary,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        _resendCountdown > 0
                                            ? 'Gửi lại (${_resendCountdown}s)'
                                            : 'Gửi lại',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: (_resendCountdown > 0)
                                              ? theme.colorScheme.onSurface.withOpacity(0.5)
                                              : theme.colorScheme.primary,
                                        ),
                                      ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
