import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../services/supabase_auth_service.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';
import 'login_screen.dart';
import 'new_home_screen.dart';
import 'otp_verification_screen.dart';
import 'forgot_password_otp_screen.dart';

class RegisterScreen extends StatefulWidget {
  static const routeName = '/register';

  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _authService = SupabaseAuthService();
  bool _isLoading = false;
  String? _errorMessage;
  String? _errorCode;
  bool _showEmailExistsActions = false;
  
  // Rate limit countdown
  int? _rateLimitCountdown;
  Timer? _countdownTimer;
  
  // Removed real-time email validation
  // Timer? _emailCheckTimer;
  // bool _isCheckingEmail = false;
  // String? _emailValidationMessage;
  // bool? _emailExists;
  // String _lastCheckedEmail = '';

  @override
  void dispose() {
    _emailController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _clearError() {
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
        _errorCode = null;
        _showEmailExistsActions = false;
        _rateLimitCountdown = null;
      });
      _countdownTimer?.cancel();
    }
  }

  /// Bắt đầu countdown timer cho rate limit
  void _startRateLimitCountdown() {
    _countdownTimer?.cancel();
    
    print('🕐 Bắt đầu countdown từ $_rateLimitCountdown giây');
    
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      if (_rateLimitCountdown != null && _rateLimitCountdown! > 0) {
        setState(() {
          _rateLimitCountdown = _rateLimitCountdown! - 1;
        });
        print('⏰ Countdown: $_rateLimitCountdown giây còn lại');
      } else {
        print('✅ Countdown hoàn thành');
        timer.cancel();
        if (mounted) {
          setState(() {
            _rateLimitCountdown = null;
            // Clear error message khi countdown xong
            if (_errorCode == 'RATE_LIMIT_EXCEEDED' || _errorCode == 'EMAIL_RATE_LIMIT_EXCEEDED') {
              _errorMessage = null;
              _errorCode = null;
            }
          });
        }
      }
    });
  }

  /// Lấy text hiển thị cho error message với countdown
  String _getErrorMessage() {
    if (_errorMessage == null) return '';
    
    if (_rateLimitCountdown != null && _rateLimitCountdown! > 0) {
      final minutes = _rateLimitCountdown! ~/ 60;
      final seconds = _rateLimitCountdown! % 60;
      final timeText = minutes > 0 
          ? '$minutes:${seconds.toString().padLeft(2, '0')}'
          : '${seconds}s';
      
      // Thêm emoji và text thân thiện hơn
      final emoji = _rateLimitCountdown! > 120 ? '⏳' : _rateLimitCountdown! > 30 ? '⏰' : '🔄';
      return '$emoji $_errorMessage\n\nVui lòng đợi $timeText nữa nhé!';
    }
    
    return _errorMessage!;
  }

  /// Widget hiển thị progress bar cho countdown
  Widget _buildCountdownProgress() {
    if (_rateLimitCountdown == null || _rateLimitCountdown! <= 0) {
      return SizedBox.shrink();
    }
    
    final originalTime = _errorCode == 'EMAIL_RATE_LIMIT_EXCEEDED' ? 300 : 60;
    final progress = (_rateLimitCountdown! / originalTime);
    
    return Column(
      children: [
        SizedBox(height: 8),
        LinearProgressIndicator(
          value: 1 - progress,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            _rateLimitCountdown! > 30 ? Colors.orange : Colors.green
          ),
        ),
        SizedBox(height: 4),
        Text(
          '${(progress * 100).toInt()}% hoàn thành',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Hiển thị snackbar với countdown timer cho rate limit
  void _showRateLimitSnackBar(int retryAfter) {
    int countdown = retryAfter;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: StatefulBuilder(
          builder: (context, setState) {
            return Row(
              children: [
                Icon(Icons.timer, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Vui lòng đợi $countdown giây trước khi thử lại',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        ),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: retryAfter),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Đóng',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );

    // Countdown timer
    Future.doWhile(() async {
      await Future.delayed(Duration(seconds: 1));
      countdown--;
      return countdown > 0 && mounted;
    });
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _errorCode = null;
      _showEmailExistsActions = false;
    });

    try {
      print('=== BẮT ĐẦU ĐĂNG KÝ ===');
      print('Email: ${_emailController.text.trim()}');

      final email = _emailController.text.trim();

      // Bước 1: Kiểm tra email đã tồn tại chưa
      print('🔍 Bước 1: Kiểm tra email tồn tại');
      final emailCheckResult = await _authService.checkEmailExists(email);
      
      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      // Nếu email không hợp lệ
      if (emailCheckResult['valid'] == false) {
        print('❌ Email không hợp lệ: ${emailCheckResult['message']}');
        setState(() {
          _errorMessage = emailCheckResult['message'];
        });
        return;
      }

      // Nếu email đã tồn tại
      if (emailCheckResult['exists'] == true) {
        print('⚠️ Email đã tồn tại: ${emailCheckResult['message']}');
        setState(() {
          _errorMessage = emailCheckResult['message'];
          _errorCode = 'EMAIL_ALREADY_EXISTS';
          _showEmailExistsActions = true;
        });
        return;
      }

      // Nếu không thể kiểm tra email (network error, rate limit, etc.)
      if (emailCheckResult['exists'] == null) {
        print('⚠️ Không thể kiểm tra email: ${emailCheckResult['message']}');
        print('📝 Debug error response: $emailCheckResult');
        
        // Xử lý rate limit gracefully
        if (emailCheckResult['errorCode'] == 'RATE_LIMIT_EXCEEDED' || 
            emailCheckResult['errorCode'] == 'EMAIL_RATE_LIMIT_EXCEEDED') {
          final retryAfter = emailCheckResult['retryAfter'] ?? 60;
          
          setState(() {
            _errorMessage = emailCheckResult['message'];
            _errorCode = emailCheckResult['errorCode'];
            _rateLimitCountdown = retryAfter;
          });
          
          // Bắt đầu countdown timer trong error message
          _startRateLimitCountdown();
        } else {
          setState(() {
            _errorMessage = emailCheckResult['message'];
          });
        }
        return;
      }

      // Bước 2: Gửi OTP đến email
      print('📧 Bước 2: Gửi OTP đến email');
      final result = await _authService.sendOtpForSignUp(email);

      print('Kết quả gửi OTP: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Gửi OTP thành công, chuyển đến màn hình xác minh');

        // Hiển thị thông báo thành công
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n?.otpSentToEmail ?? 'Mã OTP đã được gửi đến email của bạn!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Chuyển đến màn hình xác minh OTP
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              email: email,
            ),
          ),
        );
      } else {
        print('UI: Gửi OTP thất bại, hiển thị thông báo lỗi');
        final l10n = AppLocalizations.of(context);
        setState(() {
          _errorMessage = result['message'] ?? (l10n?.unknownErrorOccurred ?? 'Đã xảy ra lỗi không xác định');
          _errorCode = result['errorCode'];
          _showEmailExistsActions = _errorCode == 'EMAIL_ALREADY_EXISTS';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi đăng ký exception: $e');
      print('UI: Loại exception: ${e.runtimeType}');
      print('UI: Chi tiết: ${e.toString()}');

      if (mounted) {
        final l10n = AppLocalizations.of(context);
        setState(() {
          _errorMessage = l10n?.errorOccurredTryAgainLater ?? 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('=== KẾT THÚC ĐĂNG KÝ ===');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loginWithGoogle() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final result = await _authService.signInWithGoogle();

      if (!mounted) return;

      if (result['success'] == true) {
        print('Đăng nhập Google thành công, chuyển màn hình');
        // Đăng nhập thành công - navigate đến home screen
        Navigator.of(context).pushReplacementNamed(NewHomeScreen.routeName);
      } else {
        print('Đăng nhập Google thất bại: ${result['message']}');

        setState(() {
          _errorMessage = result['message'];
        });

        // Hiển thị thông báo lỗi
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage ?? (l10n?.googleLoginFailed ?? 'Đăng nhập bằng Google thất bại')),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('Lỗi đăng nhập Google tại UI: $e');

      if (mounted) {
        final l10n = AppLocalizations.of(context);
        setState(() {
          _errorMessage = l10n?.googleLoginErrorOccurred ??
              'Đã xảy ra lỗi khi đăng nhập với Google. Vui lòng thử lại sau.';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400;
    final l10n = AppLocalizations.of(context);

    // Responsive dimensions
    final headerHeight = isSmallScreen 
        ? screenHeight * 0.18  // 18% cho màn hình nhỏ
        : screenHeight * 0.22; // 22% cho màn hình bình thường
    
    final logoSize = isSmallScreen ? 80.0 : 100.0;
    final horizontalPadding = isSmallScreen ? 16.0 : 24.0;

    Widget mainContent = Container(
      height: screenHeight,
      width: screenWidth,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            colorScheme.primary,
            colorScheme.primaryContainer,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header với logo và back button
            Container(
              height: headerHeight,
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Stack(
                children: [
                  // Back button
                  Positioned(
                    top: 8,
                    left: 0,
                    child: IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  
                  // Logo và title
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: 8),
                        Image.asset(
                          'assets/images/logo.png',
                          width: logoSize,
                          height: logoSize,
                        ),
                        SizedBox(height: isSmallScreen ? 8 : 12),
                        SafeText(
                          l10n?.createNewAccount ?? 'Tạo tài khoản mới',
                          style: isSmallScreen
                              ? AppTextStyles.title3(context).withWeight(FontWeight.bold).withColor(Colors.white)
                              : AppTextStyles.title2(context).withWeight(FontWeight.bold).withColor(Colors.white),
                          maxLines: 1,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Form content - Expandable để sử dụng không gian còn lại
            Expanded(
              child: SingleChildScrollView(
                physics: ClampingScrollPhysics(),
                padding: EdgeInsets.fromLTRB(
                  horizontalPadding, 
                  isSmallScreen ? 8 : 16, 
                  horizontalPadding, 
                  horizontalPadding
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Card chứa form đăng ký
                    Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 20.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Hiển thị lỗi nếu có
                              if (_errorMessage != null)
                                Container(
                                  padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                                  margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
                                  decoration: BoxDecoration(
                                    color: _showEmailExistsActions ? Colors.orange.shade50 : Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: _showEmailExistsActions ? Colors.orange.shade200 : Colors.red.shade200
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            _showEmailExistsActions ? Icons.info_outline : (_rateLimitCountdown != null ? Icons.timer : Icons.error_outline),
                                            color: _showEmailExistsActions ? Colors.orange.shade700 : Colors.red.shade700,
                                            size: 18,
                                          ),
                                          SizedBox(width: 8),
                                          Expanded(
                                            child: SafeText(
                                              _getErrorMessage(),
                                              style: AppTextStyles.footnote(context).withColor(
                                                _showEmailExistsActions ? Colors.orange.shade700 : Colors.red.shade700
                                              ),
                                              maxLines: 4,
                                            ),
                                          ),
                                        ],
                                      ),
                                      
                                      // Progress bar cho countdown
                                      if (_rateLimitCountdown != null && _rateLimitCountdown! > 0) 
                                        _buildCountdownProgress(),
                                      
                                      // Quick action buttons cho email đã tồn tại
                                      if (_showEmailExistsActions) ...[
                                        SizedBox(height: 12),
                                        SafeText(
                                          l10n?.youCan ?? 'Bạn có thể:',
                                          style: AppTextStyles.caption1(context)
                                              .withColor(Colors.orange.shade600)
                                              .withWeight(FontWeight.w500),
                                          maxLines: 1,
                                        ),
                                        SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: ElevatedButton.icon(
                                                onPressed: () {
                                                  Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: colorScheme.primary,
                                                  foregroundColor: Colors.white,
                                                  elevation: 2,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(8),
                                                  ),
                                                  padding: EdgeInsets.symmetric(
                                                    vertical: isSmallScreen ? 8 : 10
                                                  ),
                                                ),
                                                icon: Icon(Icons.login, size: 16),
                                                label: SafeText(
                                                  l10n?.loginTitle ?? 'Đăng nhập',
                                                  style: AppTextStyles.caption1(context)
                                                      .withWeight(FontWeight.bold)
                                                      .withColor(Colors.white),
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: OutlinedButton.icon(
                                                onPressed: () {
                                                  // Navigate to forgot password screen
                                                  Navigator.of(context).pushNamed(ForgotPasswordOtpScreen.routeName);
                                                },
                                                style: OutlinedButton.styleFrom(
                                                  foregroundColor: colorScheme.primary,
                                                  side: BorderSide(color: colorScheme.primary),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(8),
                                                  ),
                                                  padding: EdgeInsets.symmetric(
                                                    vertical: isSmallScreen ? 8 : 10
                                                  ),
                                                ),
                                                icon: Icon(Icons.lock_reset, size: 16),
                                                label: SafeText(
                                                  l10n?.forgotPasswordShort ?? 'Quên MK?',
                                                  style: AppTextStyles.caption1(context)
                                                      .withWeight(FontWeight.bold)
                                                      .withColor(colorScheme.primary),
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ],
                                  ),
                                ),

                              // Spacing sau error message
                              if (_errorMessage != null)
                                SizedBox(height: isSmallScreen ? 8 : 12),

                              // Email field - basic validation only
                              TextFormField(
                                controller: _emailController,
                                keyboardType: TextInputType.emailAddress,
                                style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                                onChanged: (value) => _clearError(),
                                decoration: InputDecoration(
                                  labelText: 'Email',
                                  hintText: l10n?.enterYourEmail ?? 'Nhập email của bạn',
                                  prefixIcon: Icon(Icons.email_outlined),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide(width: 1),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                    vertical: isSmallScreen ? 12 : 16, 
                                    horizontal: 16
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return l10n?.pleaseEnterEmail ?? 'Vui lòng nhập email';
                                  }
                                  if (!value.contains('@') || !value.contains('.')) {
                                    return l10n?.invalidEmail ?? 'Email không hợp lệ';
                                  }
                                  return null;
                                },
                              ),
                              
                              SizedBox(height: isSmallScreen ? 12 : 16),

                              // Thông báo về OTP
                              Container(
                                padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                                margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.blue.shade200),
                                ),
                                child: SafeText(
                                  l10n?.otpVerificationInfo ?? 'Chúng tôi sẽ gửi mã OTP đến email của bạn để xác minh tài khoản.',
                                  style: AppTextStyles.footnote(context).withColor(Colors.blue.shade700),
                                  textAlign: TextAlign.center,
                                  maxLines: 3,
                                ),
                              ),

                              SizedBox(height: isSmallScreen ? 16 : 24),

                              // Register button
                              ElevatedButton(
                                onPressed: _isLoading ? null : _register,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: EdgeInsets.symmetric(
                                    vertical: isSmallScreen ? 12 : 14
                                  ),
                                ),
                                child: _isLoading
                                    ? SizedBox(
                                        height: 24,
                                        width: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : SafeText(
                                        l10n?.sendOtpCode ?? 'GỬI MÃ OTP',
                                        style: AppTextStyles.callout(context)
                                            .withWeight(FontWeight.bold)
                                            .withColor(Colors.white),
                                        maxLines: 1,
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: isSmallScreen ? 16 : 20),

                    // Divider "Hoặc"
                    Row(
                      children: [
                        Expanded(
                          child: Divider(color: Colors.grey.shade400, thickness: 1),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16),
                          child: SafeText(
                            l10n?.orText ?? 'HOẶC',
                            style: AppTextStyles.footnote(context)
                                .withWeight(FontWeight.w500)
                                .withColor(Colors.grey.shade600),
                            maxLines: 1,
                          ),
                        ),
                        Expanded(
                          child: Divider(color: Colors.grey.shade400, thickness: 1),
                        ),
                      ],
                    ),

                    SizedBox(height: isSmallScreen ? 16 : 20),

                    // Google Sign-In button với design đẹp hơn
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300, width: 1),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _isLoading ? null : _loginWithGoogle,
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: isSmallScreen ? 12 : 14,
                              horizontal: 16,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Google Logo SVG
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: SvgPicture.asset(
                                    'assets/images/google.svg',
                                    width: 20,
                                    height: 20,
                                    fit: BoxFit.contain,
                                    placeholderBuilder: (context) => Container(
                                      width: 20,
                                      height: 20,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade300,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          'G',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                
                                SizedBox(width: 12),
                                
                                // Text
                                Flexible(
                                  child: SafeText(
                                    l10n?.signInWithGoogle ?? 'Đăng nhập với Google',
                                    style: AppTextStyles.callout(context)
                                        .withWeight(FontWeight.w500)
                                        .withColor(Colors.grey.shade700),
                                    maxLines: 1,
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: isSmallScreen ? 20 : 28),

                    // Đăng nhập link với design đẹp hơn
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: isSmallScreen ? 10 : 12,
                        horizontal: isSmallScreen ? 16 : 20,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SafeText(
                            l10n?.alreadyHaveAccount ?? 'Đã có tài khoản? ',
                            style: AppTextStyles.footnote(context)
                                .withColor(Colors.grey.shade600),
                            maxLines: 1,
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: colorScheme.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SafeText(
                                    l10n?.loginTitle ?? 'Đăng nhập',
                                    style: AppTextStyles.footnote(context)
                                        .withWeight(FontWeight.bold)
                                        .withColor(colorScheme.primary),
                                    maxLines: 1,
                                  ),
                                  SizedBox(width: 4),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 12,
                                    color: colorScheme.primary,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Bottom padding để đảm bảo không bị cut off
                    SizedBox(height: isSmallScreen ? 16 : 24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );

    return Scaffold(
      body: _isLoading 
          ? Stack(
              children: [
                mainContent,
                // Loading overlay
                Container(
                  color: Colors.black.withValues(alpha: 0.4),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ],
            )
          : mainContent,
    );
  }
}
