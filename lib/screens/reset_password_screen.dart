import 'package:flutter/material.dart';
import '../services/supabase_auth_service.dart';
import '../widgets/otp_input_widget.dart';
import 'login_screen.dart';
import 'forgot_password_otp_screen.dart';

class ResetPasswordScreen extends StatefulWidget {
  static const routeName = '/reset-password';
  
  final String email;
  
  const ResetPasswordScreen({
    super.key,
    required this.email,
  });

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = SupabaseAuthService();
  
  bool _isLoading = false;
  bool _isResending = false;
  String? _errorMessage;
  int _resendCountdown = 0;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  
  @override
  void dispose() {
    _otpController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Xác minh OTP và đặt lại mật khẩu
  /// Ngày tạo: 2024-12-19
  Future<void> _resetPassword() async {
    // Validate OTP
    if (_otpController.text.trim().length != 6) {
      setState(() {
        _errorMessage = 'Vui lòng nhập đủ 6 chữ số OTP';
      });
      return;
    }
    
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('=== BẮT ĐẦU ĐẶT LẠI MẬT KHẨU TỪ UI ===');
      print('Email: ${widget.email}');
      print('OTP: ${_otpController.text.trim()}');

      final result = await _authService.verifyOtpAndResetPassword(
        widget.email,
        _otpController.text.trim(),
        _passwordController.text.trim(),
      );

      print('Kết quả đặt lại mật khẩu từ Auth Service: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Đặt lại mật khẩu thành công, chuẩn bị chuyển trang');

        // Hiển thị thông báo thành công
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Đặt lại mật khẩu thành công!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Đợi thông báo hiển thị xong trước khi chuyển màn hình
        await Future.delayed(Duration(milliseconds: 3200));

        if (!mounted) {
          print('Widget không còn mounted sau thông báo, dừng chuyển trang');
          return;
        }

        print('UI: Chuyển đến trang đăng nhập');
        // Đặt lại mật khẩu thành công, chuyển đến màn hình đăng nhập
        Navigator.of(context).pushNamedAndRemoveUntil(
          LoginScreen.routeName,
          (route) => false,
        );
      } else {
        print('UI: Đặt lại mật khẩu thất bại, hiển thị thông báo lỗi');
        setState(() {
          _errorMessage = result['message'] ?? 'Đã xảy ra lỗi không xác định';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi ngoại lệ khi đặt lại mật khẩu: $e');
      
      if (!mounted) return;
      
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('=== KẾT THÚC ĐẶT LẠI MẬT KHẨU TỪ UI ===');
    }
  }

  /// Gửi lại mã OTP
  /// Ngày tạo: 2024-12-19
  Future<void> _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      print('=== BẮT ĐẦU GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU TỪ UI ===');
      print('Email: ${widget.email}');

      final result = await _authService.resendOtpForPasswordReset(widget.email);

      print('Kết quả gửi lại OTP đặt lại mật khẩu từ Auth Service: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Gửi lại OTP đặt lại mật khẩu thành công');

        // Hiển thị thông báo thành công
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Đã gửi lại mã OTP'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Bắt đầu countdown 60 giây
        _startResendCountdown();
      } else {
        print('UI: Gửi lại OTP đặt lại mật khẩu thất bại');
        setState(() {
          _errorMessage = result['message'] ?? 'Không thể gửi lại mã OTP';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi ngoại lệ khi gửi lại OTP đặt lại mật khẩu: $e');
      
      if (!mounted) return;
      
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
      print('=== KẾT THÚC GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU TỪ UI ===');
    }
  }

  /// Bắt đầu countdown cho nút gửi lại
  /// Ngày tạo: 2024-12-19
  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60;
    });

    // Countdown timer
    Future.doWhile(() async {
      await Future.delayed(Duration(seconds: 1));
      if (!mounted) return false;
      
      setState(() {
        _resendCountdown--;
      });
      
      return _resendCountdown > 0;
    });
  }

  /// Quay lại màn hình nhập email
  /// Ngày tạo: 2024-12-19
  void _goBackToForgotPassword() {
    Navigator.of(context).pushReplacementNamed(ForgotPasswordOtpScreen.routeName);
  }

  /// Lấy thông báo lỗi cho OTP input
  String? _getOtpErrorText() {
    final otpText = _otpController.text.trim();
    
    if (otpText.isNotEmpty && otpText.length < 6) {
      return 'Mã OTP phải có 6 chữ số';
    }
    
    // Kiểm tra lỗi từ server response
    if (_errorMessage != null && _errorMessage!.toLowerCase().contains('otp')) {
      return _errorMessage;
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Đặt lại mật khẩu',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: _goBackToForgotPassword,
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header section
                  SizedBox(height: 20),
                  Icon(
                    Icons.lock_reset,
                    size: 80,
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(height: 24),
                  
                  Text(
                    'Đặt lại mật khẩu',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 12),
                  
                  Text(
                    'Chúng tôi đã gửi mã xác minh 6 chữ số đến',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 4),
                  
                  Text(
                    widget.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 32),

                  // OTP Input Widget
                  OtpInputWidget(
                    controller: _otpController,
                    enabled: !_isLoading,
                    errorText: _getOtpErrorText(),
                    onChanged: (value) {
                      // Reset error message khi người dùng nhập
                      if (_errorMessage != null) {
                        setState(() {
                          _errorMessage = null;
                        });
                      }
                    },
                    onCompleted: (otp) {
                      // Auto validate và submit khi nhập đủ 6 số
                      if (otp.length == 6) {
                        _resetPassword();
                      }
                    },
                  ),
                  SizedBox(height: 24),

                  // New Password Field
                  TextFormField(
                    controller: _passwordController,
                    obscureText: !_isPasswordVisible,
                    decoration: InputDecoration(
                      labelText: 'Mật khẩu mới',
                      hintText: 'Nhập mật khẩu mới',
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.primary,
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: theme.colorScheme.primary,
                          width: 2,
                        ),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Vui lòng nhập mật khẩu mới';
                      }
                      if (value.length < 6) {
                        return 'Mật khẩu phải có ít nhất 6 ký tự';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16),

                  // Confirm Password Field
                  TextFormField(
                    controller: _confirmPasswordController,
                    obscureText: !_isConfirmPasswordVisible,
                    decoration: InputDecoration(
                      labelText: 'Xác nhận mật khẩu',
                      hintText: 'Nhập lại mật khẩu mới',
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: theme.colorScheme.primary,
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                        onPressed: () {
                          setState(() {
                            _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: theme.colorScheme.primary,
                          width: 2,
                        ),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Vui lòng xác nhận mật khẩu';
                      }
                      if (value != _passwordController.text) {
                        return 'Mật khẩu xác nhận không khớp';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 24),

                  // Error message display
                  if (_errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                        textAlign: TextAlign.center,
                      ),
                    ),

                  // Reset Password Button
                  ElevatedButton(
                    onPressed: _isLoading ? null : _resetPassword,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: _isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.onPrimary,
                              ),
                            ),
                          )
                        : Text(
                            'Đặt lại mật khẩu',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  SizedBox(height: 24),

                  // Resend section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Không nhận được mã? ',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      TextButton(
                        onPressed: (_isResending || _resendCountdown > 0) 
                            ? null 
                            : _resendOtp,
                        child: _isResending
                            ? SizedBox(
                                height: 16,
                                width: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    theme.colorScheme.primary,
                                  ),
                                ),
                              )
                            : Text(
                                _resendCountdown > 0
                                    ? 'Gửi lại (${_resendCountdown}s)'
                                    : 'Gửi lại',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: (_resendCountdown > 0)
                                      ? theme.colorScheme.onSurface.withOpacity(0.5)
                                      : theme.colorScheme.primary,
                                ),
                              ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16),
                  
                  // Back to forgot password button
                  TextButton(
                    onPressed: _goBackToForgotPassword,
                    child: Text(
                      'Quay lại nhập email',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
