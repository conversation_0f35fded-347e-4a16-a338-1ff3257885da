import 'package:flutter/material.dart';
import '../services/saved_video_service.dart';
import '../models/saved_video.dart';
import '../screens/youtube_player_screen.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../widgets/safe_text.dart';
import '../widgets/save_video_notification.dart';
import '../widgets/delete_confirmation_dialog.dart';
import '../utils/date_formatter.dart';
import '../l10n/app_localizations.dart';

/// <PERSON><PERSON>n hình hiển thị tất cả video đã lưu
class SavedVideosScreen extends StatefulWidget {
  static const String routeName = '/saved-videos';
  
  const SavedVideosScreen({Key? key}) : super(key: key);

  @override
  State<SavedVideosScreen> createState() => _SavedVideosScreenState();
}

class _SavedVideosScreenState extends State<SavedVideosScreen> {
  final SavedVideoService _savedVideoService = SavedVideoService();
  List<SavedVideo> _savedVideos = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedVideos();
  }

  Future<void> _loadSavedVideos() async {
    try {
      final videos = await _savedVideoService.getSavedVideos();
      if (mounted) {
        setState(() {
          _savedVideos = videos;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Lỗi tải saved videos: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteSavedVideo(SavedVideo savedVideo) async {
    try {
      // Hiển thị loading notification
      SaveVideoNotificationHelper.show(
        context,
        message: 'Đang xóa video "${savedVideo.video.title}"...',
        isSuccess: true,
      );

      await _savedVideoService.removeSavedVideo(savedVideo.video.id);
      setState(() {
        _savedVideos.removeWhere((v) => v.id == savedVideo.id);
      });

      if (mounted) {
        // Ẩn loading notification
        SaveVideoNotificationHelper.hide();

        // Hiển thị delete success notification với undo option
        SaveVideoNotificationHelper.showDelete(
          context,
          message: 'Video "${savedVideo.video.title}" đã được xóa khỏi danh sách yêu thích',
          isSuccess: true,
          onUndo: () => _undoDelete(savedVideo),
        );
      }
    } catch (e) {
      print('❌ Lỗi xóa saved video: $e');
      if (mounted) {
        // Ẩn loading notification
        SaveVideoNotificationHelper.hide();

        // Hiển thị error notification
        SaveVideoNotificationHelper.show(
          context,
          message: 'Không thể xóa video. Vui lòng kiểm tra kết nối mạng và thử lại.',
          isSuccess: false,
        );
      }
    }
  }

  /// Hoàn tác việc xóa video (thêm lại vào danh sách)
  Future<void> _undoDelete(SavedVideo savedVideo) async {
    try {
      // Hiển thị loading notification
      SaveVideoNotificationHelper.show(
        context,
        message: 'Đang khôi phục video "${savedVideo.video.title}"...',
        isSuccess: true,
      );

      // Lưu lại video vào database
      final result = await _savedVideoService.saveVideo(savedVideo.video);

      if (result['success'] == true) {
        // Thêm lại vào danh sách local
        setState(() {
          _savedVideos.add(savedVideo);
          // Sắp xếp lại theo thời gian tạo
          _savedVideos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        });

        if (mounted) {
          // Ẩn loading notification
          SaveVideoNotificationHelper.hide();

          // Hiển thị success notification
          SaveVideoNotificationHelper.show(
            context,
            message: 'Video "${savedVideo.video.title}" đã được khôi phục',
            isSuccess: true,
          );
        }
      } else {
        throw Exception(result['message'] ?? 'Không thể khôi phục video');
      }
    } catch (e) {
      print('❌ Lỗi undo delete: $e');
      if (mounted) {
        // Ẩn loading notification
        SaveVideoNotificationHelper.hide();

        // Hiển thị error notification
        SaveVideoNotificationHelper.show(
          context,
          message: 'Không thể khôi phục video. Vui lòng thử lại.',
          isSuccess: false,
        );
      }
    }
  }

  void _openVideo(SavedVideo savedVideo) {
    Navigator.of(context).pushNamed(
      YouTubePlayerScreen.routeName,
      arguments: savedVideo.video,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: SafeText(l10n?.savedDishesTitle ?? 'Món ăn đã lưu'),
        backgroundColor: isDarkMode ? ModernColors.darkSurface4 : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black87,
        elevation: 0,
      ),
      backgroundColor: isDarkMode ? ModernColors.darkSurface0 : Colors.grey[50],
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _savedVideos.isEmpty
              ? _buildEmptyState(isDarkMode)
              : _buildVideoList(isDarkMode),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_outline,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            SafeText(
              l10n?.noSavedDishes ?? 'Chưa có món ăn nào được lưu',
              style: AppTextStyles.title3(context).copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            SafeText(
              l10n?.findAndSaveFavorites ?? 'Hãy tìm kiếm và lưu những món ăn yêu thích của bạn!',
              style: AppTextStyles.body(context).copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.search),
              label: SafeText(l10n?.searchForDishes ?? 'Tìm kiếm món ăn'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoList(bool isDarkMode) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _savedVideos.length,
      itemBuilder: (context, index) {
        final savedVideo = _savedVideos[index];
        return _buildVideoCard(savedVideo, isDarkMode);
      },
    );
  }

  Widget _buildVideoCard(SavedVideo savedVideo, bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? ModernColors.darkSurface2 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _openVideo(savedVideo),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail
              Container(
                width: 120,
                height: 90,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: NetworkImage(savedVideo.video.thumbnailUrl),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    // Play button overlay
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                    // Duration badge
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: SafeText(
                          _formatDuration(savedVideo.video.duration),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              
              // Video info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SafeText(
                      savedVideo.video.title,
                      style: AppTextStyles.body(context).copyWith(
                        fontWeight: FontWeight.w600,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    SafeText(
                      savedVideo.video.channelTitle,
                      style: AppTextStyles.caption1(context).copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    SafeText(
                      '${l10n?.savedOn ?? 'Đã lưu'}: ${_formatDate(savedVideo.createdAt)}',
                      style: AppTextStyles.caption2(context).copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Delete button
              IconButton(
                onPressed: () => _showDeleteConfirmation(savedVideo),
                icon: Icon(
                  Icons.delete_outline,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(SavedVideo savedVideo) async {
    final result = await DeleteConfirmationHelper.showDeleteVideo(
      context,
      videoTitle: savedVideo.video.title,
    );

    if (result == true) {
      _deleteSavedVideo(savedVideo);
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    return DateFormatter.formatRelativeTime(context, date);
  }
}
