import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/language_provider.dart';
import '../l10n/app_localizations.dart';

import '../services/supabase_auth_service.dart';
import '../services/simple_supabase_integration_service.dart';
import 'login_screen.dart';
import 'about_screen.dart';
import 'author_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widgets/supabase_status_widget.dart';
import 'user_profile_screen.dart';

class SettingsScreen extends StatefulWidget {
  static const routeName = '/settings';

  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // URL form góp ý ứng dụng
  final String _feedbackFormUrl =
      'https://docs.google.com/forms/d/e/1FAIpQLSe1cLlcyX_Fhwqaq4U7htRW17oQha0P15nEuqxWZg5c9_aXLw/viewform?usp=sharing';
  final SupabaseAuthService _authService = SupabaseAuthService();
  bool _isLoading = false;
  bool _isSyncing = false;

  // Mở URL trong trình duyệt
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      // Hiển thị thông báo lỗi nếu không mở được URL
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n?.cannotOpenLinkMessage ?? 'Không thể mở liên kết'}: $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Hiển thị dialog chọn ngôn ngữ
  void _showLanguageDialog(BuildContext context, LanguageProvider languageProvider) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n?.chooseLanguageDialog ?? 'Chọn ngôn ngữ'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Vietnamese
              ListTile(
                leading: const Text('🇻🇳', style: TextStyle(fontSize: 24)),
                title: const Text('Tiếng Việt'),
                subtitle: const Text('Vietnamese'),
                trailing: languageProvider.currentLocale.languageCode == 'vi'
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () async {
                  await languageProvider.changeLanguage('vi');
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
              ),

              // English
              ListTile(
                leading: const Text('🇺🇸', style: TextStyle(fontSize: 24)),
                title: const Text('English'),
                subtitle: const Text('English'),
                trailing: languageProvider.currentLocale.languageCode == 'en'
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () async {
                  await languageProvider.changeLanguage('en');
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n?.cancel ?? 'Hủy'),
            ),
          ],
        );
      },
    );
  }

  // Xử lý đăng xuất
  Future<void> _handleSignOut() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('🚪 Bắt đầu quá trình đăng xuất...');

      // Dừng tất cả background services trước khi đăng xuất
      await _stopBackgroundServices();

      // Thực hiện đăng xuất với timeout để tránh bị treo
      await _authService.signOut().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          print('⚠️ Timeout khi đăng xuất, tiếp tục với cleanup local');
          throw TimeoutException('Đăng xuất timeout', const Duration(seconds: 10));
        },
      );

      print('✅ Đăng xuất thành công');

      if (mounted) {
        // Hiển thị thông báo đăng xuất thành công
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n?.signOutSuccessMessage ?? 'Đăng xuất thành công'),
            backgroundColor: Colors.green,
          ),
        );

        // Chuyển đến màn hình đăng nhập
        Navigator.of(context)
            .pushNamedAndRemoveUntil(LoginScreen.routeName, (route) => false);
      }
    } catch (e) {
      print('❌ Lỗi khi đăng xuất: $e');

      // Ngay cả khi có lỗi, vẫn thực hiện cleanup local và chuyển về login
      await _forceLocalCleanup();

      if (mounted) {
        // Hiển thị thông báo lỗi nhưng vẫn chuyển về login
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n?.signOutErrorMessage ?? 'Đã đăng xuất (có lỗi nhỏ'}: ${e.toString().length > 50 ? e.toString().substring(0, 50) + '...' : e.toString()})'),
            backgroundColor: Colors.orange,
          ),
        );

        // Vẫn chuyển về màn hình đăng nhập dù có lỗi
        Navigator.of(context)
            .pushNamedAndRemoveUntil(LoginScreen.routeName, (route) => false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Dừng tất cả background services
  Future<void> _stopBackgroundServices() async {
    try {
      print('🛑 Dừng background services...');

      // Dừng auto sync timer (simplified after cleanup)
      print('Background services stopped');

      // Đợi một chút để các operation đang chạy hoàn thành
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ Đã dừng background services');
    } catch (e) {
      print('⚠️ Lỗi khi dừng background services: $e');
    }
  }

  // Force cleanup local data khi có lỗi
  Future<void> _forceLocalCleanup() async {
    try {
      print('🧹 Thực hiện force cleanup local data...');

      final prefs = await SharedPreferences.getInstance();

      // Xóa thông tin user
      await prefs.remove('user_email');
      await prefs.remove('user_id');
      await prefs.remove('display_name');
      await prefs.remove('photo_url');
      await prefs.setBool('rememberLogin', false);

      print('✅ Đã cleanup local data');
    } catch (e) {
      print('❌ Lỗi khi cleanup local data: $e');
    }
  }

  // Hiển thị hộp thoại xác nhận đăng xuất
  void _confirmSignOut() {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(l10n?.confirmSignOut ?? 'Đăng xuất'),
        content: Text(l10n?.confirmSignOutMessage ?? 'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(l10n?.cancel ?? 'Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              _handleSignOut();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text(l10n?.signOut ?? 'Đăng xuất'),
          ),
        ],
      ),
    );
  }

  // Thêm phương thức đồng bộ hóa
  Future<void> _synchronizeData() async {
    // MenuProvider đã bị xóa - function này không còn cần thiết
    final l10n = AppLocalizations.of(context);
    _showSnackBar(l10n?.syncOptimizedMessage ?? 'Chức năng đồng bộ đã được tối ưu hóa', Colors.blue);
  }

  // Thêm phương thức hiển thị thông báo
  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.settings ?? 'Cài đặt'),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tài khoản
                  _buildSectionHeader(l10n?.account ?? 'Tài khoản'),
                  ListTile(
                    title: Text(l10n?.personalInformation ?? 'Thông tin cá nhân'),
                    subtitle: Text(l10n?.viewEditAccountInfo ?? 'Xem và chỉnh sửa thông tin tài khoản'),
                    leading: const Icon(Icons.account_circle_outlined),
                    onTap: () {
                      Navigator.of(context).pushNamed(UserProfileScreen.routeName);
                    },
                  ),
                  ListTile(
                    title: Text(l10n?.signOut ?? 'Đăng xuất'),
                    subtitle: Text(l10n?.signOutFromCurrentAccount ?? 'Đăng xuất khỏi tài khoản hiện tại'),
                    leading: const Icon(Icons.logout, color: Colors.red),
                    onTap: _confirmSignOut,
                  ),
                  const Divider(),

                  // Chế độ giao diện
                  _buildSectionHeader(l10n?.interfaceMode ?? 'Chế độ giao diện'),

                  // Language Selection
                  Consumer<LanguageProvider>(
                    builder: (context, languageProvider, child) {
                      return ListTile(
                        title: Text(l10n?.language ?? 'Ngôn ngữ'),
                        subtitle: Text(languageProvider.currentLocale.languageCode == 'vi'
                            ? 'Tiếng Việt'
                            : 'English'),
                        leading: Text(
                          languageProvider.currentLocale.languageCode == 'vi' ? '🇻🇳' : '🇺🇸',
                          style: const TextStyle(fontSize: 24),
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () => _showLanguageDialog(context, languageProvider),
                      );
                    },
                  ),

                  SwitchListTile(
                    title: Text(l10n?.darkMode ?? 'Chế độ tối'),
                    subtitle: Text(l10n?.enableDisableDarkMode ?? 'Bật/tắt giao diện tối'),
                    value: themeProvider.darkMode,
                    onChanged: (value) {
                      themeProvider.toggleDarkMode();
                    },
                  ),
                  const Divider(),

                  // Màu sắc chính
                  _buildSectionHeader(l10n?.primaryColors ?? 'Màu sắc chính'),
                  _buildColorGrid(
                    colors: themeProvider.availablePrimaryColors,
                    selectedIndex: themeProvider.primaryColorIndex,
                    onColorSelected: (index) {
                      themeProvider.setPrimaryColorIndex(index);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Màu sắc phụ
                  _buildSectionHeader(l10n?.secondaryColors ?? 'Màu sắc phụ'),
                  _buildColorGrid(
                    colors: themeProvider.availableSecondaryColors,
                    selectedIndex: themeProvider.secondaryColorIndex,
                    onColorSelected: (index) {
                      themeProvider.setSecondaryColorIndex(index);
                    },
                  ),
                  const SizedBox(height: 16),

                  // Màu sắc bữa ăn
                  _buildSectionHeader(l10n?.mealColors ?? 'Màu sắc bữa ăn'),
                  _buildMealTypeColorSelector(
                    title: l10n?.breakfast ?? 'Bữa sáng',
                    currentColor: themeProvider.breakfastColor,
                    onColorChanged: (color) {
                      themeProvider.setBreakfastColor(color);
                    },
                  ),
                  const SizedBox(height: 8),

                  _buildMealTypeColorSelector(
                    title: l10n?.lunch ?? 'Bữa trưa',
                    currentColor: themeProvider.lunchColor,
                    onColorChanged: (color) {
                      themeProvider.setLunchColor(color);
                    },
                  ),
                  const SizedBox(height: 8),

                  _buildMealTypeColorSelector(
                    title: l10n?.dinner ?? 'Bữa tối',
                    currentColor: themeProvider.dinnerColor,
                    onColorChanged: (color) {
                      themeProvider.setDinnerColor(color);
                    },
                  ),
                  const SizedBox(height: 24),

                  // Phần quản lý dữ liệu
                  _buildSectionHeader(l10n?.dataManagement ?? 'Quản lý dữ liệu'),

                  // Supabase Status Widget
                  const SupabaseStatusWidget(),

                  ListTile(
                    title: Text(l10n?.clearCacheRefreshData ?? 'Xóa cache và làm mới dữ liệu'),
                    subtitle: Text(l10n?.updateAllDishesFromSource ??
                        'Cập nhật lại tất cả món ăn và thực đơn từ dữ liệu gốc'),
                    leading: const Icon(Icons.cleaning_services),
                    onTap: () => _confirmClearCache(context),
                  ),

                  // Phần thông tin ứng dụng
                  _buildSectionHeader(l10n?.appInfo ?? 'Thông tin ứng dụng'),
                  ListTile(
                    title: Text(l10n?.aboutApp ?? 'Về ứng dụng'),
                    subtitle: Text(l10n?.aboutAppDescription ?? 'Thông tin về ứng dụng Nấu gì đây'),
                    leading: const Icon(Icons.info_outline),
                    onTap: () {
                      Navigator.of(context).pushNamed(AboutScreen.routeName);
                    },
                  ),
                  ListTile(
                    title: Text(l10n?.authorInfo ?? 'Thông tin tác giả'),
                    subtitle: Text(l10n?.authorInfoDescription ?? 'Thông tin về Nguyễn Minh Đức'),
                    leading: const Icon(Icons.person_outline),
                    onTap: () {
                      Navigator.of(context).pushNamed(AuthorScreen.routeName);
                    },
                  ),
                  ListTile(
                    title: Text(l10n?.appFeedback ?? 'Góp ý ứng dụng'),
                    subtitle: Text(l10n?.shareFeedbackToImprove ?? 'Chia sẻ ý kiến để cải thiện ứng dụng'),
                    leading: const Icon(Icons.feedback_outlined),
                    trailing: const Icon(Icons.open_in_new),
                    onTap: () => _launchUrl(_feedbackFormUrl),
                  ),
                  const SizedBox(height: 16),

                  // Nút đặt lại giao diện
                  Center(
                    child: Column(
                      children: [
                        ElevatedButton.icon(
                          onPressed: () {
                            themeProvider.resetToDefaults();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(l10n?.resetToDefaultSuccessMessage ?? 'Đã đặt lại về cài đặt mặc định'),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          },
                          icon: const Icon(Icons.restore),
                          label: Text(l10n?.resetToDefaultInterface ?? 'Đặt lại giao diện mặc định'),
                        ),
                        const SizedBox(height: 12),
                        OutlinedButton.icon(
                          onPressed: () {
                            // Xóa shared preferences
                            showDialog(
                              context: context,
                              builder: (ctx) => AlertDialog(
                                title: Text(l10n?.confirmClearInterfaceCache ?? 'Xóa cache giao diện'),
                                content: Text(l10n?.confirmClearInterfaceCacheMessage ??
                                    'Thao tác này sẽ xóa tất cả cài đặt giao diện và khởi động lại ứng dụng. Bạn có chắc chắn muốn tiếp tục?'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.of(ctx).pop(),
                                    child: Text(l10n?.cancel ?? 'Hủy'),
                                  ),
                                  TextButton(
                                    onPressed: () async {
                                      Navigator.of(ctx).pop();
                                      // Xóa SharedPreferences
                                      final prefs =
                                          await SharedPreferences.getInstance();
                                      await prefs.clear();
                                      if (mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(l10n?.clearInterfaceCacheSuccessMessage ??
                                                'Đã xóa cache giao diện. Vui lòng khởi động lại ứng dụng.'),
                                            backgroundColor: Colors.green,
                                            duration: const Duration(seconds: 3),
                                          ),
                                        );
                                      }
                                    },
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.red,
                                    ),
                                    child: Text(l10n?.delete ?? 'Xóa'),
                                  ),
                                ],
                              ),
                            );
                          },
                          icon: const Icon(Icons.delete_forever),
                          label: Text(l10n?.clearInterfaceCache ?? 'Xóa cache giao diện'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Divider(),
                        const SizedBox(height: 12),
                        Card(
                          elevation: 3,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16)),
                          child: InkWell(
                            onTap: () => _launchUrl(_feedbackFormUrl),
                            borderRadius: BorderRadius.circular(16),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    themeProvider.primaryColor.withOpacity(0.8),
                                    themeProvider.secondaryColor
                                        .withOpacity(0.9),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Column(
                                children: [
                                  const Icon(
                                    Icons.feedback_rounded,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    l10n?.appFeedback ?? 'Góp ý ứng dụng',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    l10n?.shareFeedbackToImprove ?? 'Chia sẻ ý kiến để cải thiện ứng dụng',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white.withValues(alpha: 0.9),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                  Card(
                    margin: const EdgeInsets.all(8),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.sync, size: 24),
                              const SizedBox(width: 8),
                              Text(
                                l10n?.synchronizeData ?? 'Đồng bộ hóa dữ liệu',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              if (_isSyncing) const CircularProgressIndicator(),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            l10n?.syncDataBetweenDevices ?? 'Đồng bộ dữ liệu món ăn của bạn giữa các thiết bị và lưu trữ đám mây',
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _isSyncing ? null : _synchronizeData,
                            icon: const Icon(Icons.cloud_sync),
                            label: Text(l10n?.syncNow ?? 'Đồng bộ ngay'),
                            style: ElevatedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 48),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black45,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  // Hộp thoại xác nhận xóa cache
  void _confirmClearCache(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(l10n?.confirmClearCache ?? 'Xóa cache?'),
        content: Text(l10n?.confirmClearCacheMessage ??
            'Thao tác này sẽ xóa tất cả dữ liệu cache và tải lại thực đơn từ dữ liệu mới nhất. '
            'Các món ăn tự tạo sẽ bị mất. Bạn có chắc chắn muốn tiếp tục?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(l10n?.cancel ?? 'Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              _clearCache(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text(l10n?.delete ?? 'Xóa'),
          ),
        ],
      ),
    );
  }

  // Thực hiện xóa cache
  void _clearCache(BuildContext context) async {
    final l10n = AppLocalizations.of(context);
    try {
      // MenuProvider đã bị xóa - sử dụng SharedPreferences để xóa cache
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n?.clearCacheSuccessMessage ?? 'Đã xóa cache và làm mới dữ liệu thành công'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Lỗi khi xóa cache: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n?.clearCacheErrorMessage ?? 'Lỗi khi xóa cache'}: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildColorGrid({
    required List<Color> colors,
    required int selectedIndex,
    required Function(int) onColorSelected,
  }) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
        childAspectRatio: 1,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        final isSelected = index == selectedIndex;
        return InkWell(
          onTap: () => onColorSelected(index),
          child: CircleAvatar(
            backgroundColor: colors[index],
            child: isSelected
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                  )
                : null,
          ),
        );
      },
    );
  }

  Widget _buildMealTypeColorSelector({
    required String title,
    required Color currentColor,
    required Function(Color) onColorChanged,
  }) {
    // Danh sách các màu sắc cho bữa ăn
    final mealColors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 36,
              decoration: BoxDecoration(
                color: currentColor,
                borderRadius: BorderRadius.circular(18),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 36,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: mealColors.length,
                itemBuilder: (context, index) {
                  final color = mealColors[index];
                  final isSelected = color.value == currentColor.value;

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: InkWell(
                      onTap: () => onColorChanged(color),
                      child: CircleAvatar(
                        radius: 16,
                        backgroundColor: color,
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              )
                            : null,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
