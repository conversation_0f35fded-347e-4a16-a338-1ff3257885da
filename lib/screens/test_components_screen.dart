import 'package:flutter/material.dart';
import '../widgets/shared/progress_indicator_widget.dart';
import '../widgets/shared/wizard_navigation_widget.dart';
import '../widgets/shared/background_pattern_widget.dart';
import '../widgets/shared/styled_form_field.dart';

/// M<PERSON><PERSON> h<PERSON>nh test để verify các shared components hoạt động đúng
class TestComponentsScreen extends StatefulWidget {
  static const routeName = '/test-components';

  const TestComponentsScreen({super.key});

  @override
  State<TestComponentsScreen> createState() => _TestComponentsScreenState();
}

class _TestComponentsScreenState extends State<TestComponentsScreen> {
  int _currentStep = 1;
  final int _totalSteps = 4;
  final _testController = TextEditingController();

  @override
  void dispose() {
    _testController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return BackgroundPatternWidget(
      primaryColor: primaryColor,
      isDarkMode: isDarkMode,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Test Components'),
          elevation: 0,
          backgroundColor: Colors.transparent,
          centerTitle: true,
        ),
        body: Column(
          children: [
            // Test Progress Indicator
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progress Indicator Test',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ProgressIndicatorWidget(
                    currentStep: _currentStep,
                    totalSteps: _totalSteps,
                    primaryColor: primaryColor,
                    isDarkMode: isDarkMode,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: _currentStep > 1
                            ? () => setState(() => _currentStep--)
                            : null,
                        child: const Text('Previous'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: _currentStep < _totalSteps
                            ? () => setState(() => _currentStep++)
                            : null,
                        child: const Text('Next'),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Test Step Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Step Header Test',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  StepHeaderWidget(
                    currentStep: _currentStep,
                    totalSteps: _totalSteps,
                    stepTitle: 'Bước $_currentStep trong $_totalSteps',
                    stepDescription: 'Mô tả cho bước hiện tại',
                    primaryColor: primaryColor,
                    isDarkMode: isDarkMode,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Test Styled Form Field
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Styled Form Field Test',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    StyledFormField(
                      controller: _testController,
                      labelText: 'Test Field',
                      hintText: 'Enter test text...',
                      prefixIcon: Icons.text_fields,
                      isDarkMode: isDarkMode,
                      primaryColor: primaryColor,
                    ),
                    const SizedBox(height: 20),
                    StyledFormField(
                      controller: TextEditingController(),
                      labelText: 'Multi-line Test',
                      hintText: 'Enter multiple lines...',
                      prefixIcon: Icons.notes,
                      isDarkMode: isDarkMode,
                      primaryColor: primaryColor,
                      maxLines: 3,
                    ),
                    const SizedBox(height: 20),
                    SectionHeaderWidget(
                      title: 'Section Header Test',
                      icon: Icons.category,
                      primaryColor: primaryColor,
                      isDarkMode: isDarkMode,
                      subtitle: 'This is a test subtitle',
                    ),
                    const SizedBox(height: 20),
                    TipCardWidget(
                      title: 'Tip Card Test',
                      icon: Icons.lightbulb_outline,
                      primaryColor: primaryColor,
                      isDarkMode: isDarkMode,
                      tips: const [
                        'This is the first tip',
                        'This is the second tip',
                        'This is the third tip',
                      ],
                    ),
                    const SizedBox(height: 100), // Space for navigation
                  ],
                ),
              ),
            ),

            // Test Wizard Navigation
            WizardNavigationWidget(
              currentStep: _currentStep,
              totalSteps: _totalSteps,
              canGoNext: true,
              canGoPrevious: _currentStep > 1,
              isLoading: false,
              onPrevious: _currentStep > 1
                  ? () => setState(() => _currentStep--)
                  : null,
              onNext: _currentStep < _totalSteps
                  ? () => setState(() => _currentStep++)
                  : null,
              onComplete: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Test completed successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              primaryColor: primaryColor,
              isDarkMode: isDarkMode,
              completeButtonText: 'Complete Test',
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget để test animation performance
class AnimationTestWidget extends StatefulWidget {
  const AnimationTestWidget({super.key});

  @override
  State<AnimationTestWidget> createState() => _AnimationTestWidgetState();
}

class _AnimationTestWidgetState extends State<AnimationTestWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (_animation.value * 0.2),
          child: Opacity(
            opacity: 0.5 + (_animation.value * 0.5),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.animation,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }
}
