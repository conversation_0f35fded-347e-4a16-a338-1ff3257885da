import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/supabase_auth_service.dart';
import '../services/new_user_profile_service.dart';

class UserProfileScreen extends StatefulWidget {
  static const routeName = '/user-profile';

  const UserProfileScreen({super.key});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  final NewUserProfileService _profileService = NewUserProfileService();
  
  Map<String, dynamic>? _userInfo;
  Map<String, dynamic>? _userProfile;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // L<PERSON>y thông tin cơ bản từ auth
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        _userInfo = {
          'uid': currentUser.id,
          'email': currentUser.email,
          'displayName': currentUser.userMetadata?['full_name'],
          'photoURL': currentUser.userMetadata?['avatar_url'],
          'createdAt': currentUser.createdAt,
        };
      }

      // Lấy thông tin profile mở rộng
      if (currentUser != null) {
        final userId = currentUser.id;
        final profileResult = await _profileService.getUserProfile(userId);
        if (profileResult['success']) {
          _userProfile = profileResult['data'];
        }
      }

      // Lấy thông tin từ SharedPreferences nếu cần
      final prefs = await SharedPreferences.getInstance();
      final savedDisplayName = prefs.getString('display_name');
      final savedPhotoUrl = prefs.getString('photo_url');
      
      if (_userInfo != null) {
        _userInfo!['savedDisplayName'] = savedDisplayName;
        _userInfo!['savedPhotoUrl'] = savedPhotoUrl;
      }

    } catch (e) {
      print('Lỗi khi tải thông tin người dùng: $e');
      _errorMessage = 'Không thể tải thông tin người dùng. Vui lòng thử lại.';
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildProfileHeader() {
    if (_userInfo == null) return const SizedBox.shrink();

    final displayName = _userInfo!['displayName'] ??
                       _userInfo!['savedDisplayName'] ??
                       'Người dùng';
    final email = _userInfo!['email'] ?? 'Không có email';
    final photoUrl = _userInfo!['photoURL'] ?? _userInfo!['savedPhotoUrl'];
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode
              ? [
                  Theme.of(context).primaryColor.withValues(alpha: 0.9),
                  Theme.of(context).primaryColor.withValues(alpha: 0.7),
                ]
              : [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
        ),
      ),
      child: Column(
        children: [
          // Avatar
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipOval(
              child: photoUrl != null && photoUrl.isNotEmpty
                  ? Image.network(
                      photoUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultAvatar(displayName);
                      },
                    )
                  : _buildDefaultAvatar(displayName),
            ),
          ),
          const SizedBox(height: 16),
          
          // Tên hiển thị
          Text(
            displayName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          // Email
          Text(
            email,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar(String displayName) {
    return Container(
      color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      child: Icon(
        Icons.person,
        size: 50,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildInfoSection() {
    if (_userInfo == null) return const SizedBox.shrink();

    final createdAt = _userInfo!['createdAt'];
    final joinDate = createdAt != null 
        ? DateTime.parse(createdAt).toLocal()
        : null;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin tài khoản',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow(
              icon: Icons.email_outlined,
              label: 'Email',
              value: _userInfo!['email'] ?? 'Không có',
            ),
            const Divider(),
            
            _buildInfoRow(
              icon: Icons.person_outline,
              label: 'ID người dùng',
              value: _userInfo!['uid'] ?? 'Không có',
              isMonospace: true,
            ),
            
            if (joinDate != null) ...[
              const Divider(),
              _buildInfoRow(
                icon: Icons.calendar_today_outlined,
                label: 'Ngày tham gia',
                value: '${joinDate.day}/${joinDate.month}/${joinDate.year}',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isMonospace = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: isMonospace ? 'monospace' : null,
                    fontSize: isMonospace ? 12 : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pushNamed('/edit-profile');
              },
              icon: const Icon(Icons.edit),
              label: const Text('Chỉnh sửa thông tin'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _loadUserData,
              icon: const Icon(Icons.refresh),
              label: const Text('Làm mới thông tin'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thông tin cá nhân'),
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadUserData,
                        child: const Text('Thử lại'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildProfileHeader(),
                      _buildInfoSection(),
                      _buildActionButtons(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
    );
  }
}
