import 'package:flutter/material.dart';
import '../models/weekly_menu.dart';
import '../models/user_profile.dart';
import '../services/weekly_menu_service.dart';
import '../services/youtube_service.dart';
import '../constants/app_text_styles.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';
import '../widgets/feature_development_dialog.dart';
import '../utils/date_formatter.dart';
import 'youtube_player_screen.dart';

/// <PERSON><PERSON>n hình thực đơn hàng tuần
class WeeklyMenuScreen extends StatefulWidget {
  static const String routeName = '/weekly-menu';

  const WeeklyMenuScreen({super.key});

  @override
  State<WeeklyMenuScreen> createState() => _WeeklyMenuScreenState();
}

class _WeeklyMenuScreenState extends State<WeeklyMenuScreen> {
  final WeeklyMenuService _weeklyMenuService = WeeklyMenuService();
  
  WeeklyMenu? _currentMenu;
  bool _isLoading = true;
  String? _error;
  DateTime _selectedWeekStart = WeekUtils.getWeekStartDate(DateTime.now());

  @override
  void initState() {
    super.initState();
    // Tạm thời disable để test dialog
    // _loadWeekMenu();
    setState(() {
      _isLoading = false;
      _currentMenu = WeeklyMenu(
        userId: 'temp-user',
        weekStartDate: _selectedWeekStart,
        items: [],
      );
    });
  }

  Future<void> _loadWeekMenu() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await _weeklyMenuService.getCurrentWeekMenu();
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (result['success']) {
            _currentMenu = result['menu'];
          } else {
            _error = result['message'];
          }
        });
      }
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        setState(() {
          _isLoading = false;
          _error = '${l10n?.errorLoadingMenu ?? 'Lỗi khi tải thực đơn'}: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: SafeText(l10n?.weeklyMenu ?? 'Thực đơn tuần'),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.add,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            onPressed: () {
              FeatureDevelopmentDialog.showWeeklyMenuDialog(context);
            },
          ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            onPressed: _loadWeekMenu,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }
    
    if (_error != null) {
      return _buildErrorState();
    }
    
    if (_currentMenu == null || _currentMenu!.items.isEmpty) {
      return _buildEmptyState();
    }
    
    return _buildMenuContent();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            SafeText(
              l10n?.errorOccurred ?? 'Có lỗi xảy ra',
              style: AppTextStyles.title3(context).copyWith(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            SafeText(
              _error ?? (l10n?.cannotLoadMenu ?? 'Không thể tải thực đơn'),
              style: AppTextStyles.body(context).copyWith(
                color: isDarkMode ? Colors.white70 : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadWeekMenu,
              child: SafeText(l10n?.tryAgain ?? 'Thử lại'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_menu,
              size: 64,
              color: isDarkMode ? Colors.white54 : Colors.grey[400],
            ),
            const SizedBox(height: 16),
            SafeText(
              l10n?.weekMenuEmpty ?? 'Thực đơn tuần này còn trống',
              style: AppTextStyles.title3(context).copyWith(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            SafeText(
              l10n?.addDishesToMenu ?? 'Hãy thêm món ăn vào thực đơn của bạn',
              style: AppTextStyles.body(context).copyWith(
                color: isDarkMode ? Colors.white70 : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                FeatureDevelopmentDialog.showWeeklyMenuDialog(context);
              },
              icon: const Icon(Icons.add),
              label: SafeText(l10n?.addDish ?? 'Thêm món ăn'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuContent() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return CustomScrollView(
      slivers: [
        // Header với thông tin tuần
        SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: SafeText(
                        _currentMenu!.weekName,
                        style: AppTextStyles.title3(context).copyWith(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildWeekStats(),
              ],
            ),
          ),
        ),
        
        // Danh sách ngày trong tuần
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final date = _currentMenu!.weekDays[index];
              return _buildDayCard(date, isDarkMode);
            },
            childCount: _currentMenu!.weekDays.length,
          ),
        ),
        
        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 20),
        ),
      ],
    );
  }

  Widget _buildWeekStats() {
    final stats = _currentMenu!.getStatistics();
    final l10n = AppLocalizations.of(context);

    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            l10n?.totalDishes ?? 'Tổng món',
            '${stats['total_items']}',
            Icons.restaurant,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            l10n?.eaten ?? 'Đã ăn',
            '${stats['completed_items']}',
            Icons.check_circle,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            l10n?.completion ?? 'Hoàn thành',
            '${stats['completion_percentage'].toInt()}%',
            Icons.trending_up,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        SafeText(
          value,
          style: AppTextStyles.body(context).copyWith(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        SafeText(
          label,
          style: AppTextStyles.footnote(context).copyWith(
            color: Theme.of(context).primaryColor.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildDayCard(DateTime date, bool isDarkMode) {
    final dayItems = _currentMenu!.getItemsForDate(date);
    final isToday = DateTime.now().day == date.day &&
                   DateTime.now().month == date.month &&
                   DateTime.now().year == date.year;
    final l10n = AppLocalizations.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isToday ? Border.all(
          color: Theme.of(context).primaryColor,
          width: 2,
        ) : null,
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header ngày
            Row(
              children: [
                SafeText(
                  DateFormatter.formatDayName(context, date),
                  style: AppTextStyles.body(context).copyWith(
                    color: isToday
                        ? Theme.of(context).primaryColor
                        : (isDarkMode ? Colors.white : Colors.black87),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8),
                SafeText(
                  DateFormatter.formatShortDate(context, date),
                  style: AppTextStyles.subhead(context).copyWith(
                    color: isDarkMode ? Colors.white70 : Colors.grey[600],
                  ),
                ),
                if (isToday) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: SafeText(
                      l10n?.today ?? 'Hôm nay',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12),
            
            // Meals cho ngày này
            if (dayItems.isEmpty)
              _buildEmptyDayState(date)
            else
              ...MealType.values.map((mealType) {
                final item = _currentMenu!.getItemForMeal(date, mealType);
                return _buildMealItem(date, mealType, item, isDarkMode);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyDayState(DateTime date) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
          style: BorderStyle.solid,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.add_circle_outline,
            color: isDarkMode ? Colors.white54 : Colors.grey[400],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SafeText(
              l10n?.noMealsForThisDay ?? 'Chưa có món ăn nào cho ngày này',
              style: AppTextStyles.subhead(context).copyWith(
                color: isDarkMode ? Colors.white54 : Colors.grey[600],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              FeatureDevelopmentDialog.showWeeklyMenuDialog(context);
            },
            child: SafeText(l10n?.add ?? 'Thêm'),
          ),
        ],
      ),
    );
  }

  Widget _buildMealItem(DateTime date, MealType mealType, WeeklyMenuItem? item, bool isDarkMode) {
    if (item == null) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
            style: BorderStyle.solid,
          ),
        ),
        child: Row(
          children: [
            SafeText(
              mealType.emoji,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SafeText(
                _getMealTypeName(context, mealType),
                style: AppTextStyles.subhead(context).copyWith(
                  color: isDarkMode ? Colors.white54 : Colors.grey[600],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                FeatureDevelopmentDialog.showWeeklyMenuDialog(context);
              },
              child: const SafeText('Thêm'),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openVideo(item.video),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: item.isCompleted 
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : (isDarkMode ? Colors.grey[800] : Colors.grey[50]),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: item.isCompleted 
                    ? Theme.of(context).primaryColor.withOpacity(0.3)
                    : (isDarkMode ? Colors.grey[700]! : Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                // Meal type emoji
                SafeText(
                  mealType.emoji,
                  style: const TextStyle(fontSize: 20),
                ),
                const SizedBox(width: 12),
                
                // Video thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.network(
                    item.video.thumbnailUrl,
                    width: 60,
                    height: 45,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 45,
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.play_circle_outline,
                          color: Colors.grey[600],
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                
                // Video info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SafeText(
                        item.video.title,
                        style: AppTextStyles.subhead(context).copyWith(
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.visible,
                      ),
                      const SizedBox(height: 2),
                      SafeText(
                        '${_getMealTypeName(context, mealType)} • ${item.video.formattedDuration}',
                        style: AppTextStyles.footnote(context).copyWith(
                          color: isDarkMode ? Colors.white60 : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Completion checkbox
                Checkbox(
                  value: item.isCompleted,
                  onChanged: (value) => _toggleItemCompletion(item, value ?? false),
                  activeColor: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openVideo(YouTubeVideo video) {
    Navigator.of(context).pushNamed(
      YouTubePlayerScreen.routeName,
      arguments: video,
    );
  }

  Future<void> _toggleItemCompletion(WeeklyMenuItem item, bool isCompleted) async {
    try {
      final result = await _weeklyMenuService.updateItemCompletion(item.id, isCompleted);
      
      if (result['success']) {
        // Reload menu to update UI
        _loadWeekMenu();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: SafeText(result['message']),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: SafeText(result['message']),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: SafeText('${l10n?.errorUpdating ?? 'Lỗi khi cập nhật'}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Lấy tên localized cho meal type
  String _getMealTypeName(BuildContext context, MealType mealType) {
    final l10n = AppLocalizations.of(context);
    switch (mealType) {
      case MealType.breakfast:
        return l10n?.breakfast ?? 'Bữa sáng';
      case MealType.lunch:
        return l10n?.lunch ?? 'Bữa trưa';
      case MealType.dinner:
        return l10n?.dinner ?? 'Bữa tối';
    }
  }
}
