import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutter_html/flutter_html.dart';
import '../services/youtube_service.dart';
import '../widgets/safe_text.dart';
import '../constants/app_text_styles.dart';
import '../services/saved_video_service.dart';
import '../widgets/feature_development_dialog.dart';

class YouTubePlayerScreen extends StatefulWidget {
  static const String routeName = '/youtube-player';

  const YouTubePlayerScreen({super.key});

  @override
  State<YouTubePlayerScreen> createState() => _YouTubePlayerScreenState();
}

class _YouTubePlayerScreenState extends State<YouTubePlayerScreen> {
  late YoutubePlayerController _controller;
  final SavedVideoService _savedVideoService = SavedVideoService();
  final YouTubeService _youtubeService = YouTubeService();
  YouTubeVideo? _video;
  List<YouTubeComment> _topComments = [];
  bool _isPlayerReady = false;
  bool _isFullScreen = false;
  bool _isLoadingComments = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Lấy video từ arguments
    final args = ModalRoute.of(context)?.settings.arguments;
    print('🎬 Arguments received: $args');
    
    if (args is YouTubeVideo && _video == null) {
      _video = args;
      print('🎬 Video received: ${_video!.id} - ${_video!.title}');
      _initializePlayer();
      _loadTopComments();
    } else {
      print('❌ No valid video arguments received or video already set');
    }
  }

  void _initializePlayer() {
    if (_video == null) return;
    
    print('🎬 Initializing player for video: ${_video!.id}');
    
    _controller = YoutubePlayerController(
      initialVideoId: _video!.id,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: true,
        captionLanguage: 'vi',
        showLiveFullscreenButton: true,
      ),
    );

    _controller.addListener(() {
      if (_controller.value.isReady && !_isPlayerReady) {
        setState(() {
          _isPlayerReady = true;
        });
        print('🎬 Player is ready');
      }
      
      if (_controller.value.isFullScreen != _isFullScreen) {
        setState(() {
          _isFullScreen = _controller.value.isFullScreen;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_video == null) {
      return Scaffold(
        appBar: AppBar(
          title: const SafeText('Video Player'),
        ),
        body: const Center(
          child: SafeText('Không có video để phát'),
        ),
      );
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        // Xử lý khi thoát fullscreen
      },
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).primaryColor,
        topActions: <Widget>[
          IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 25.0,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          Expanded(
            child: SafeText(
              _video!.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18.0,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 25.0,
            ),
            onPressed: () {
              print('Settings pressed');
            },
          ),
        ],
        onReady: () {
          print('🎬 Player ready callback');
        },
        onEnded: (data) {
          print('🎬 Video ended');
        },
      ),
      builder: (context, player) => Scaffold(
        backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: SafeText(
            _video!.title,
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              player,
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildVideoInfo(isDarkMode),
                      const SizedBox(height: 20),
                    _buildActionButtons(),
                    const SizedBox(height: 20),
                    _buildTopComments(isDarkMode),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoInfo(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SafeText(
          _video!.title,
          style: AppTextStyles.title2(context).copyWith(
            color: isDarkMode ? Colors.white : Colors.black87,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SafeText(
                _video!.channelTitle,
                style: AppTextStyles.body(context).copyWith(
                  color: isDarkMode ? Colors.white70 : Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            SafeText(
              _video!.formattedDuration,
              style: AppTextStyles.footnote(context).copyWith(
                color: isDarkMode ? Colors.white54 : Colors.grey[500],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: _buildActionButton(
            icon: Icons.bookmark_add_outlined,
            label: 'Lưu',
            onTap: _saveVideo,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildActionButton(
            icon: Icons.share_outlined,
            label: 'Chia sẻ',
            onTap: _shareVideo,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildActionButton(
            icon: Icons.calendar_today_outlined,
            label: 'Thêm vào tuần',
            onTap: _addToWeeklyMenu,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          SafeText(
            label,
            style: AppTextStyles.caption2(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopComments(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.comment_outlined,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            SafeText(
              'Bình luận',
              style: AppTextStyles.title3(context).copyWith(
                color: isDarkMode ? Colors.white : Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _isLoadingComments
            ? _buildCommentsLoading(isDarkMode)
            : _topComments.isEmpty
                ? _buildNoComments(isDarkMode)
                : _buildCommentsList(isDarkMode),
      ],
    );
  }

  Widget _buildCommentsLoading(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey[900]?.withValues(alpha: 0.3)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey[700]!
              : Colors.grey[200]!,
        ),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          SafeText(
            'Đang tải bình luận...',
            style: AppTextStyles.body(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoComments(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey[900]?.withValues(alpha: 0.3)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey[700]!
              : Colors.grey[200]!,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.comment_outlined,
            size: 48,
            color: isDarkMode ? Colors.white54 : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          SafeText(
            'Chưa có bình luận',
            style: AppTextStyles.body(context).copyWith(
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsList(bool isDarkMode) {
    return Column(
      children: _topComments.map((comment) => _buildCommentItem(comment, isDarkMode)).toList(),
    );
  }

  Widget _buildCommentItem(YouTubeComment comment, bool isDarkMode) {
    return IntrinsicHeight(
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Colors.grey[900]?.withValues(alpha: 0.3)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDarkMode
                ? Colors.grey[700]!
                : Colors.grey[200]!,
          ),
        ),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header với avatar và tên
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundImage: comment.authorProfileImageUrl.isNotEmpty
                    ? NetworkImage(comment.authorProfileImageUrl)
                    : null,
                backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                child: comment.authorProfileImageUrl.isEmpty
                    ? Icon(
                        Icons.person,
                        size: 16,
                        color: Theme.of(context).primaryColor,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SafeText(
                      comment.authorDisplayName,
                      style: AppTextStyles.footnote(context).copyWith(
                        color: isDarkMode ? Colors.white : Colors.black87,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SafeText(
                      _formatCommentTime(comment.publishedAt),
                      style: AppTextStyles.caption2(context).copyWith(
                        color: isDarkMode ? Colors.white54 : Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
              // Like count
              Row(
                children: [
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 4),
                  SafeText(
                    '${comment.likeCount}',
                    style: AppTextStyles.caption2(context).copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Nội dung comment với HTML rendering
          Builder(
            builder: (context) {
              final htmlContent = _formatCommentHtml(comment.textDisplay);
              print('💬 Comment text: "${comment.textDisplay}"');
              print('💬 HTML content: "$htmlContent"');

              return Html(
                data: htmlContent,
                style: {
                  "body": Style(
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                    fontSize: FontSize(AppTextStyles.body(context).fontSize ?? 14),
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    fontFamily: AppTextStyles.body(context).fontFamily,
                    lineHeight: const LineHeight(1.6),
                  ),
                  "p": Style(
                    margin: Margins.only(bottom: 8),
                  ),
                  "br": Style(
                    display: Display.block,
                  ),
                },
              );
            },
          ),
        ],
        ),
      ),
    );
  }

  String _formatCommentTime(DateTime publishedAt) {
    final now = DateTime.now();
    final difference = now.difference(publishedAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  String _formatCommentHtml(String text) {
    // Convert HTML entities và format HTML content
    String htmlContent = text
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll(RegExp(r'\n{3,}'), '\n\n') // Giới hạn tối đa 2 xuống dòng liên tiếp
        .replaceAll(RegExp(r'[ \t]+'), ' ') // Loại bỏ nhiều space/tab liên tiếp
        .trim();

    // Thêm emoji và format đặc biệt nếu cần
    htmlContent = _addEmojiSpacing(htmlContent);

    // Wrap trong body tag để đảm bảo HTML hợp lệ
    return '<body>$htmlContent</body>';
  }

  String _addEmojiSpacing(String text) {
    // Đơn giản hóa - chỉ đảm bảo xuống dòng đúng cách
    // Thay thế các emoji phổ biến với khoảng trắng phù hợp
    return text
        .replaceAll('😍', ' 😍 ')
        .replaceAll('👍', ' 👍 ')
        .replaceAll('❤️', ' ❤️ ')
        .replaceAll('😊', ' 😊 ')
        .replaceAll('🔥', ' 🔥 ')
        .replaceAll('👌', ' 👌 ')
        .replaceAll('😋', ' 😋 ')
        .replaceAll('🤤', ' 🤤 ')
        .replaceAll('💯', ' 💯 ')
        .replaceAll('👏', ' 👏 ')
        // Loại bỏ khoảng trắng thừa
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }



  void _loadTopComments() async {
    if (_video == null) return;

    setState(() {
      _isLoadingComments = true;
    });

    try {
      final comments = await _youtubeService.getTopPositiveComments(_video!.id);
      if (mounted) {
        setState(() {
          _topComments = comments;
          _isLoadingComments = false;
        });
      }
    } catch (e) {
      print('❌ Error loading comments: $e');
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
        });
      }
    }
  }

  void _saveVideo() async {
    print('💾 Saving video: ${_video!.id}');

    try {
      final result = await _savedVideoService.saveVideo(_video!);

      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: SafeText('Đã lưu video thành công!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: SafeText(result['message'] ?? 'Có lỗi xảy ra khi lưu video'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('💾 Error saving video: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: SafeText('Có lỗi xảy ra: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareVideo() {
    final url = 'https://www.youtube.com/watch?v=${_video!.id}';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: SafeText('Chia sẻ: $url'),
      ),
    );
  }

  void _addToWeeklyMenu() {
    FeatureDevelopmentDialog.showAddToWeeklyMenuDialog(context);
  }


}
