import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthService {
  final supabase = Supabase.instance.client;
  
  AuthService() {
    try {
      print('AuthService: Đã khởi tạo Supabase client');
    } catch (e) {
      print('AuthService: Lỗi khởi tạo: $e');
      rethrow;
    }
  }

  // Validate email
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Validate password
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  User? get currentUser => supabase.auth.currentUser;
  Stream<AuthState> get authStateChanges => supabase.auth.onAuthStateChange;
  
  bool isSignedIn() {
    return supabase.auth.currentUser != null;
  }
  
  // Đăng nhập bằng Google
  Future<Map<String, dynamic>> signInWithGoogle() async {
    try {
      print('=== BẮT ĐẦU ĐĂNG NHẬP BẰNG GOOGLE ===');
      
      final res = await supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'com.minhduc.naugiday://callback'
      );
      
      if (res) {
        // Đợi để Supabase hoàn thành quá trình OAuth
        // Supabase sẽ tự động cập nhật phiên khi hoàn thành
        final currentUser = supabase.auth.currentUser;
        
        if (currentUser != null) {
          print('Đăng nhập bằng Google thành công. UserID: ${currentUser.id}');
          
          // Lưu thông tin vào SharedPreferences
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user_email', currentUser.email ?? '');
          await prefs.setString('user_id', currentUser.id);
          await prefs.setString('display_name', currentUser.userMetadata?['full_name'] ?? '');
          await prefs.setString('photo_url', currentUser.userMetadata?['avatar_url'] ?? '');
          await prefs.setBool('rememberLogin', true);
          print('Đã lưu thông tin người dùng vào SharedPreferences');

          // Tạo map userData với kiểu dữ liệu rõ ràng
          final Map<String, dynamic> userData = {
            'uid': currentUser.id,
            'email': currentUser.email,
            'displayName': currentUser.userMetadata?['full_name'],
            'photoURL': currentUser.userMetadata?['avatar_url'],
          };
          
          return {
            'success': true,
            'userData': userData,
            'message': 'Đăng nhập bằng Google thành công'
          };
        } else {
          return {
            'success': false,
            'message': 'Đăng nhập bị hủy hoặc thất bại'
          };
        }
      } else {
        return {
          'success': false,
          'message': 'Không thể bắt đầu đăng nhập bằng Google'
        };
      }
    } catch (e) {
      print('Lỗi đăng nhập Google: $e');
      
      String message;
      if (e.toString().contains('network') || e.toString().contains('connection')) {
        message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else if (e.toString().contains('canceled') || e.toString().contains('cancelled')) {
        message = 'Đăng nhập đã bị hủy. Vui lòng thử lại.';
      } else if (e.toString().contains('timeout')) {
        message = 'Đăng nhập mất quá nhiều thời gian. Vui lòng thử lại sau.';
      } else {
        message = 'Đã xảy ra lỗi khi đăng nhập bằng Google. Vui lòng thử lại sau.';
      }
      
      return {
        'success': false,
        'message': message
      };
    } finally {
      print('=== KẾT THÚC ĐĂNG NHẬP BẰNG GOOGLE ===');
    }
  }
  
  Future<User?> getCurrentUser() async {
    return currentUser;
  }
  
  Future<Map<String, dynamic>> getCurrentUserData() async {
    if (currentUser == null) {
      return {
        'success': false,
        'message': 'Người dùng chưa đăng nhập'
      };
    }
    
    try {
      return {
        'success': true,
        'userData': {
          'uid': currentUser!.id,
          'email': currentUser!.email,
          'displayName': currentUser!.userMetadata?['full_name'],
          'photoURL': currentUser!.userMetadata?['avatar_url'],
        },
      };
    } catch (e) {
      print('Lỗi khi lấy thông tin người dùng: $e');
      return {
        'success': false,
        'message': 'Không thể lấy thông tin người dùng. Vui lòng thử lại.'
      };
    }
  }
  
  // Đăng ký người dùng mới
  Future<Map<String, dynamic>> signUp(String email, String password) async {
    try {
      // Validate input
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Email không hợp lệ'
        };
      }
      
      if (!_isValidPassword(password)) {
        return {
          'success': false,
          'message': 'Mật khẩu phải có ít nhất 6 ký tự'
        };
      }
      
      // Tạo tài khoản mới
      final response = await supabase.auth.signUp(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        // Không đăng nhập tự động, cần xác nhận email
        return {
          'success': true,
          'message': 'Đăng ký thành công! Vui lòng kiểm tra email để xác nhận tài khoản.',
          'requiresEmailVerification': true
        };
      } else {
        return {
          'success': false,
          'message': 'Đăng ký thất bại. Vui lòng thử lại.'
        };
      }
    } catch (e) {
      print('Lỗi đăng ký: $e');
      String message = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
      
      if (e is AuthException) {
        if (e.message.contains('email') && e.message.contains('already')) {
          message = 'Email này đã được sử dụng. Vui lòng dùng email khác.';
        } else if (e.message.contains('weak-password')) {
          message = 'Mật khẩu không đủ mạnh. Vui lòng chọn mật khẩu khác.';
        }
      }
      
      return {
        'success': false,
        'message': message
      };
    }
  }
  
  // Đăng nhập
  Future<Map<String, dynamic>> signIn(String email, String password) async {
    try {
      // Validate input
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Email không hợp lệ'
        };
      }
      
      if (password.isEmpty) {
        return {
          'success': false,
          'message': 'Vui lòng nhập mật khẩu'
        };
      }
      
      // Đăng nhập
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        // Lưu thông tin vào SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_email', response.user!.email ?? '');
        await prefs.setString('user_id', response.user!.id);
        await prefs.setString('display_name', response.user!.userMetadata?['full_name'] ?? '');
        await prefs.setString('photo_url', response.user!.userMetadata?['avatar_url'] ?? '');
        await prefs.setBool('rememberLogin', true);
        
        return {
          'success': true,
          'userData': {
            'uid': response.user!.id,
            'email': response.user!.email,
            'displayName': response.user!.userMetadata?['full_name'],
            'photoURL': response.user!.userMetadata?['avatar_url'],
          },
          'message': 'Đăng nhập thành công'
        };
      } else {
        return {
          'success': false,
          'message': 'Đăng nhập thất bại. Vui lòng kiểm tra email và mật khẩu.'
        };
      }
    } catch (e) {
      print('Lỗi đăng nhập: $e');
      String message = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
      
      if (e is AuthException) {
        if (e.message.contains('Invalid login credentials')) {
          message = 'Email hoặc mật khẩu không chính xác.';
        } else if (e.message.contains('not confirmed')) {
          message = 'Email chưa được xác nhận. Vui lòng kiểm tra hộp thư để xác nhận.';
        }
      }
      
      return {
        'success': false,
        'message': message
      };
    }
  }
  
  // Quên mật khẩu
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Email không hợp lệ'
        };
      }
      
      await supabase.auth.resetPasswordForEmail(email);
      
      return {
        'success': true,
        'message': 'Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.'
      };
    } catch (e) {
      print('Lỗi quên mật khẩu: $e');
      
      return {
        'success': false,
        'message': 'Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.'
      };
    }
  }
  
  // Đăng xuất
  Future<Map<String, dynamic>> signOut() async {
    try {
      print('🚪 Bắt đầu đăng xuất từ AuthService...');

      // Thực hiện đăng xuất với timeout để tránh treo
      await supabase.auth.signOut().timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          print('⚠️ Timeout khi đăng xuất, tiếp tục với cleanup local');
          // Không throw error, tiếp tục với cleanup
        },
      );

      // Xóa thông tin trong SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_email');
      await prefs.remove('user_id');
      await prefs.remove('display_name');
      await prefs.remove('photo_url');
      await prefs.setBool('rememberLogin', false);

      print('✅ Đăng xuất thành công từ AuthService');

      return {
        'success': true,
        'message': 'Đăng xuất thành công'
      };
    } catch (e) {
      print('❌ Lỗi đăng xuất từ AuthService: $e');

      // Vẫn thực hiện cleanup local dù có lỗi
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('user_email');
        await prefs.remove('user_id');
        await prefs.remove('display_name');
        await prefs.remove('photo_url');
        await prefs.setBool('rememberLogin', false);

        print('✅ Đã cleanup local data dù có lỗi đăng xuất');

        // Trả về success vì đã cleanup được local data
        return {
          'success': true,
          'message': 'Đăng xuất thành công (có lỗi nhỏ)'
        };
      } catch (cleanupError) {
        print('❌ Lỗi khi cleanup local data: $cleanupError');

        return {
          'success': false,
          'message': 'Không thể đăng xuất hoàn toàn. Vui lòng thử lại.'
        };
      }
    }
  }
  
  // Kiểm tra đăng nhập từ SharedPreferences
  Future<Map<String, dynamic>> checkLoginState() async {
    try {
      // Kiểm tra phiên hiện tại trước
      final session = supabase.auth.currentSession;
      if (session != null) {
        // Người dùng đã đăng nhập
        final user = supabase.auth.currentUser;
        if (user != null) {
          return {
            'success': true,
            'userData': {
              'uid': user.id,
              'email': user.email,
              'displayName': user.userMetadata?['full_name'],
              'photoURL': user.userMetadata?['avatar_url'],
            },
            'message': 'Người dùng đã đăng nhập'
          };
        }
      }
      
      // Kiểm tra trong SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final rememberLogin = prefs.getBool('rememberLogin') ?? false;
      final email = prefs.getString('user_email');
      
      if (!rememberLogin || email == null) {
        return {
          'success': false,
          'message': 'Người dùng chưa đăng nhập'
        };
      }
      
      // Đã lưu thông tin đăng nhập, nhưng không có phiên hiện tại, cần đăng nhập lại
      return {
        'success': false,
        'needRelogin': true,
        'savedEmail': email,
        'message': 'Phiên đã hết hạn, cần đăng nhập lại'
      };
    } catch (e) {
      print('Lỗi kiểm tra trạng thái đăng nhập: $e');
      
      return {
        'success': false,
        'message': 'Không thể kiểm tra trạng thái đăng nhập'
      };
    }
  }
  
  // Cập nhật thông tin người dùng
  Future<Map<String, dynamic>> updateUserProfile({String? displayName, String? photoURL}) async {
    try {
      if (currentUser == null) {
        return {
          'success': false,
          'message': 'Người dùng chưa đăng nhập'
        };
      }
      
      final updates = <String, dynamic>{};
      if (displayName != null) {
        updates['full_name'] = displayName;
      }
      if (photoURL != null) {
        updates['avatar_url'] = photoURL;
      }
      
      if (updates.isNotEmpty) {
        await supabase.auth.updateUser(UserAttributes(
          data: updates,
        ));
        
        // Cập nhật thông tin trong SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        if (displayName != null) {
          await prefs.setString('display_name', displayName);
        }
        if (photoURL != null) {
          await prefs.setString('photo_url', photoURL);
        }
      }
      
      return {
        'success': true,
        'message': 'Cập nhật thông tin thành công'
      };
    } catch (e) {
      print('Lỗi cập nhật thông tin: $e');
      
      return {
        'success': false,
        'message': 'Không thể cập nhật thông tin. Vui lòng thử lại.'
      };
    }
  }
}