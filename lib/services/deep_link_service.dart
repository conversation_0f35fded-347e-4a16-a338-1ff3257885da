import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../supabase_options.dart';

/// Service để xử lý deep link callbacks từ OAuth providers
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;
  final StreamController<bool> _authSuccessController = StreamController<bool>.broadcast();

  /// Stream để listen auth success events
  Stream<bool> get authSuccessStream => _authSuccessController.stream;

  /// Khởi tạo deep link service
  Future<void> initialize() async {
    try {
      _appLinks = AppLinks();
      
      // Listen for incoming links when app is already running
      _linkSubscription = _appLinks.uriLinkStream.listen(
        _handleIncomingLink,
        onError: (err) {
          print('Deep link error: $err');
        },
      );

      // Handle initial link when app is launched from a link
      final initialUri = await _appLinks.getInitialAppLink();
      if (initialUri != null) {
        print('App launched with deep link: $initialUri');
        await _handleIncomingLink(initialUri);
      }

      print('✅ Deep link service initialized successfully');
    } catch (e) {
      print('❌ Error initializing deep link service: $e');
    }
  }

  /// Xử lý incoming deep link
  Future<void> _handleIncomingLink(Uri uri) async {
    print('🔗 =================================');
    print('🔗 HANDLING INCOMING DEEP LINK');
    print('🔗 =================================');

    try {
      print('📋 Deep link details:');
      print('   ✓ Full URI: $uri');
      print('   ✓ Scheme: ${uri.scheme}');
      print('   ✓ Host: ${uri.host}');
      print('   ✓ Path: ${uri.path}');
      print('   ✓ Query: ${uri.query}');
      print('   ✓ Fragment: ${uri.fragment}');
      print('   ✓ Query parameters: ${uri.queryParameters}');

      // Kiểm tra xem có phải callback từ OAuth không
      print('📋 Checking if OAuth callback...');
      final isOAuthCallback = uri.scheme == 'com.minhduc.naugiday' && uri.host == 'callback';
      print('   ✓ Scheme match: ${uri.scheme == 'com.minhduc.naugiday'}');
      print('   ✓ Host match: ${uri.host == 'callback'}');
      print('   ✓ Is OAuth callback: $isOAuthCallback');

      if (isOAuthCallback) {
        print('   ✅ OAuth callback detected - processing...');

        // Xử lý OAuth callback với Supabase
        await _handleOAuthCallback(uri);
      } else {
        print('   ℹ️ Non-OAuth deep link detected');
        print('   ℹ️ Expected scheme: com.minhduc.naugiday');
        print('   ℹ️ Expected host: callback');
        print('   ℹ️ Actual scheme: ${uri.scheme}');
        print('   ℹ️ Actual host: ${uri.host}');
      }
    } catch (e, stackTrace) {
      print('💥 =================================');
      print('💥 ERROR HANDLING DEEP LINK');
      print('💥 =================================');
      print('❌ Exception: $e');
      print('❌ Exception type: ${e.runtimeType}');
      print('❌ Stack trace:');
      print(stackTrace.toString());
    } finally {
      print('🏁 =================================');
      print('🏁 FINISHED HANDLING DEEP LINK');
      print('🏁 =================================');
    }
  }

  /// Xử lý OAuth callback
  Future<void> _handleOAuthCallback(Uri uri) async {
    print('🔐 =================================');
    print('🔐 PROCESSING OAUTH CALLBACK');
    print('🔐 =================================');

    try {
      print('📋 STEP 1: Analyzing callback URI');
      print('   ✓ URI: $uri');
      print('   ✓ Has access_token: ${uri.queryParameters.containsKey('access_token')}');
      print('   ✓ Has refresh_token: ${uri.queryParameters.containsKey('refresh_token')}');
      print('   ✓ Has code: ${uri.queryParameters.containsKey('code')}');
      print('   ✓ Has error: ${uri.queryParameters.containsKey('error')}');

      if (uri.queryParameters.containsKey('error')) {
        final error = uri.queryParameters['error'];
        final errorDescription = uri.queryParameters['error_description'];
        print('   ❌ OAuth error detected: $error');
        print('   ❌ Error description: $errorDescription');
        _authSuccessController.add(false);
        return;
      }

      // Sử dụng Supabase để xử lý session từ URL
      print('📋 STEP 2: Getting Supabase client');
      final supabase = Supabase.instance.client;
      print('   ✓ Supabase client: ${supabase != null ? "OK" : "NULL"}');

      print('📋 STEP 3: Processing OAuth code');

      // Bypass getSessionFromUrl hoàn toàn - xử lý code thủ công
      final code = uri.queryParameters['code'];
      if (code != null) {
        print('   ✓ Found authorization code: ${code.substring(0, 10)}...');
        print('   🔄 Bypassing getSessionFromUrl - using direct code exchange');

        await _exchangeCodeForSession(code, supabase);
      } else {
        print('   ❌ No authorization code found in callback');
        throw Exception('No authorization code in OAuth callback');
      }

      // Kiểm tra xem có session không
      print('📋 STEP 4: Checking current session');
      var session = supabase.auth.currentSession;
      print('   ✓ Session exists: ${session != null}');

      // Nếu không có session, thử refresh
      if (session == null) {
        print('📋 STEP 4.1: No session found, trying to refresh...');
        try {
          await supabase.auth.refreshSession();
          session = supabase.auth.currentSession;
          print('   ✓ After refresh - Session exists: ${session != null}');
        } catch (refreshError) {
          print('   ❌ Refresh session failed: $refreshError');
        }
      }

      if (session != null) {
        print('   ✓ Session ID: ${session.accessToken.substring(0, 20)}...');
        print('   ✓ User ID: ${session.user.id}');
        print('   ✓ User email: ${session.user.email}');
        print('   ✓ User metadata: ${session.user.userMetadata}');
        print('   ✓ Session expires at: ${session.expiresAt}');

        print('📋 STEP 5: Saving user data');
        // Lưu thông tin user vào SharedPreferences
        await _saveUserDataAfterOAuth(session.user);

        print('📋 STEP 6: Broadcasting auth success');
        // Thông báo auth success
        _authSuccessController.add(true);
        print('   ✅ Auth success broadcasted');
      } else {
        print('   ❌ No session found after OAuth callback and refresh');
        print('   ❌ This might be a Supabase configuration issue');
        print('   ❌ Check if redirect URI is correctly configured in Supabase dashboard');

        // Debug Supabase configuration
        await _debugSupabaseConfig(uri, supabase);

        _authSuccessController.add(false);
      }
    } catch (e, stackTrace) {
      print('💥 =================================');
      print('💥 ERROR PROCESSING OAUTH CALLBACK');
      print('💥 =================================');
      print('❌ Exception: $e');
      print('❌ Exception type: ${e.runtimeType}');
      print('❌ Stack trace:');
      print(stackTrace.toString());
      _authSuccessController.add(false);
    } finally {
      print('🏁 =================================');
      print('🏁 FINISHED OAUTH CALLBACK PROCESSING');
      print('🏁 =================================');
    }
  }

  /// Lưu user data sau khi OAuth thành công
  Future<void> _saveUserDataAfterOAuth(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_email', user.email ?? '');
      await prefs.setString('user_id', user.id);
      await prefs.setString(
          'display_name', user.userMetadata?['full_name'] ?? '');
      await prefs.setString(
          'photo_url', user.userMetadata?['avatar_url'] ?? '');
      await prefs.setBool('rememberLogin', true);
      print('✅ Đã lưu thông tin người dùng vào SharedPreferences');
    } catch (e) {
      print('❌ Lỗi khi lưu SharedPreferences: $e');
    }
  }

  /// Exchange authorization code for session directly
  Future<void> _exchangeCodeForSession(String code, SupabaseClient supabase) async {
    print('🔄 =================================');
    print('🔄 DIRECT CODE EXCHANGE');
    print('🔄 =================================');

    try {
      print('   📋 Step 1: Preparing code exchange');
      print('   ✓ Authorization code: ${code.substring(0, 10)}...');

      // Method 1: Đơn giản chỉ trigger auth state change
      print('   📋 Step 2: Triggering auth state change detection');

      // Đợi một chút để auth state có thể update
      await Future.delayed(Duration(seconds: 2));

      // Kiểm tra xem có session không
      final session = supabase.auth.currentSession;
      if (session != null) {
        print('   ✅ Session found after delay - auth state updated automatically');
        return;
      }

      // Method 2: Force refresh session
      print('   📋 Step 3: Force refreshing session');
      try {
        await supabase.auth.refreshSession();
        final refreshedSession = supabase.auth.currentSession;
        if (refreshedSession != null) {
          print('   ✅ Session found after refresh');
          return;
        }
      } catch (e) {
        print('   ❌ Refresh session failed: $e');
      }

      // Method 3: Restart auth flow
      print('   📋 Step 4: Restarting auth flow');
      await _restartAuthFlow(supabase);

    } catch (e) {
      print('   ❌ Direct code exchange failed: $e');
      print('   🔄 Falling back to sign in with stored credentials...');

      // Fallback: Thử đăng nhập lại với Google
      await _fallbackGoogleSignIn(supabase);
    }
  }

  /// Restart auth flow
  Future<void> _restartAuthFlow(SupabaseClient supabase) async {
    try {
      print('   🔄 Restarting auth flow...');

      // Đơn giản chỉ báo thành công và để AuthWrapper handle
      print('   ✅ Assuming OAuth was successful - letting AuthWrapper handle navigation');

      // Broadcast success để AuthWrapper có thể check auth state
      _authSuccessController.add(true);

    } catch (e) {
      print('   ❌ Restart auth flow error: $e');
      _authSuccessController.add(false);
    }
  }

  /// Fallback Google sign in
  Future<void> _fallbackGoogleSignIn(SupabaseClient supabase) async {
    try {
      print('   🔄 Attempting fallback Google sign in...');

      // Thử đăng nhập lại với Google OAuth
      final result = await supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'com.minhduc.naugiday://callback',
      );

      if (result) {
        print('   ✅ Fallback Google sign in initiated');
      } else {
        print('   ❌ Fallback Google sign in failed');
      }

    } catch (e) {
      print('   ❌ Fallback sign in error: $e');
    }
  }

  /// Debug Supabase configuration
  Future<void> _debugSupabaseConfig(Uri uri, SupabaseClient supabase) async {
    print('🔍 =================================');
    print('🔍 DEBUG SUPABASE CONFIGURATION');
    print('🔍 =================================');

    try {
      print('   📋 Supabase URL: ${SupabaseConfig.url}');
      print('   📋 Supabase Key: ${SupabaseConfig.anonKey.substring(0, 20)}...');
      print('   📋 Auth URL: ${SupabaseConfig.url}/auth/v1');

      print('   📋 Callback URI details:');
      print('      - Full URI: $uri');
      print('      - Code: ${uri.queryParameters['code']?.substring(0, 10)}...');
      print('      - State: ${uri.queryParameters['state'] ?? 'none'}');

      print('   📋 Expected redirect URI: com.minhduc.naugiday://callback');
      print('   📋 Actual redirect URI: ${uri.scheme}://${uri.host}${uri.path}');

      final isRedirectMatch = uri.toString().startsWith('com.minhduc.naugiday://callback');
      print('   📋 Redirect URI match: $isRedirectMatch');

      if (!isRedirectMatch) {
        print('   ❌ REDIRECT URI MISMATCH!');
        print('   ❌ This is likely the cause of the OAuth failure');
        print('   ❌ Check Supabase Dashboard > Authentication > URL Configuration');
      }

    } catch (e) {
      print('   ❌ Error debugging Supabase config: $e');
    }
  }



  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
    _authSuccessController.close();
  }
}
