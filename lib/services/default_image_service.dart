import 'package:supabase_flutter/supabase_flutter.dart';

class DefaultImageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static List<String>? _cachedImageUrls;
  static DateTime? _lastFetchTime;
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// Lấy danh sách URL ảnh mặc định từ table dishes
  static Future<List<String>> getDefaultImageUrls() async {
    try {
      // Kiểm tra cache còn hiệu lực không
      if (_cachedImageUrls != null && 
          _lastFetchTime != null && 
          DateTime.now().difference(_lastFetchTime!) < _cacheExpiry) {
        return _cachedImageUrls!;
      }

      // Lấy tất cả image_url từ table dishes
      final response = await _supabase
          .from('dishes')
          .select('image_url')
          .not('image_url', 'is', null);

      final List<String> imageUrls = (response as List<dynamic>)
          .map((item) => item['image_url'] as String)
          .where((url) => url.isNotEmpty)
          .toList();

      // Lưu vào cache
      _cachedImageUrls = imageUrls;
      _lastFetchTime = DateTime.now();

      print('Đã lấy ${imageUrls.length} URL ảnh từ Supabase');
      return imageUrls;
    } catch (e) {
      print('Lỗi khi lấy danh sách ảnh mặc định: $e');
      return [];
    }
  }

  /// Lấy một ảnh mặc định ngẫu nhiên
  static Future<String?> getRandomDefaultImageUrl() async {
    try {
      final imageUrls = await getDefaultImageUrls();
      if (imageUrls.isEmpty) return null;
      
      // Trả về ảnh ngẫu nhiên
      imageUrls.shuffle();
      return imageUrls.first;
    } catch (e) {
      print('Lỗi khi lấy ảnh mặc định ngẫu nhiên: $e');
      return null;
    }
  }

  /// Lấy ảnh mặc định theo index (để có tính nhất quán)
  static Future<String?> getDefaultImageUrlByIndex(int index) async {
    try {
      final imageUrls = await getDefaultImageUrls();
      if (imageUrls.isEmpty) return null;
      
      // Sử dụng modulo để đảm bảo index luôn hợp lệ
      final safeIndex = index % imageUrls.length;
      return imageUrls[safeIndex];
    } catch (e) {
      print('Lỗi khi lấy ảnh mặc định theo index: $e');
      return null;
    }
  }

  /// Xóa cache để force refresh
  static void clearCache() {
    _cachedImageUrls = null;
    _lastFetchTime = null;
  }
}