import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/feedback.dart';

/// Service để quản lý feedback từ người dùng
class FeedbackService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Gửi feedback mới
  Future<bool> submitFeedback(String content) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Người dùng chưa đăng nhập');
      }

      print('📝 Đang gửi feedback: ${content.substring(0, content.length > 50 ? 50 : content.length)}...');

      final response = await _supabase
          .from('feedback')
          .insert({
            'user_id': user.id,
            'content': content,
          })
          .select()
          .single();

      print('✅ Feedback đã được gửi thành công: ${response['id']}');
      return true;
    } catch (e) {
      print('❌ Lỗi khi gửi feedback: $e');
      return false;
    }
  }

  /// L<PERSON>y danh sách feedback của người dùng hiện tại
  Future<List<FeedbackModel>> getUserFeedback() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Người dùng chưa đăng nhập');
      }

      print('📋 Đang lấy danh sách feedback của user: ${user.id}');

      final response = await _supabase
          .from('feedback')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      final feedbackList = (response as List)
          .map((json) => FeedbackModel.fromJson(json))
          .toList();

      print('✅ Lấy được ${feedbackList.length} feedback');
      return feedbackList;
    } catch (e) {
      print('❌ Lỗi khi lấy feedback: $e');
      return [];
    }
  }

  /// Xóa feedback (nếu cần)
  Future<bool> deleteFeedback(String feedbackId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Người dùng chưa đăng nhập');
      }

      print('🗑️ Đang xóa feedback: $feedbackId');

      await _supabase
          .from('feedback')
          .delete()
          .eq('id', feedbackId)
          .eq('user_id', user.id); // Đảm bảo chỉ xóa feedback của mình

      print('✅ Feedback đã được xóa thành công');
      return true;
    } catch (e) {
      print('❌ Lỗi khi xóa feedback: $e');
      return false;
    }
  }

  /// Kiểm tra xem user đã gửi feedback trong ngày hôm nay chưa
  Future<bool> hasSubmittedTodayFeedback() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return false;
      }

      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _supabase
          .from('feedback')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', startOfDay.toIso8601String())
          .lt('created_at', endOfDay.toIso8601String())
          .limit(1);

      return (response as List).isNotEmpty;
    } catch (e) {
      print('❌ Lỗi khi kiểm tra feedback hôm nay: $e');
      return false;
    }
  }

  /// Lấy thống kê feedback của user
  Future<Map<String, dynamic>> getFeedbackStats() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {
          'total': 0,
          'thisMonth': 0,
          'lastFeedback': null,
        };
      }

      // Tổng số feedback
      final totalResponse = await _supabase
          .from('feedback')
          .select('id')
          .eq('user_id', user.id);

      final total = (totalResponse as List).length;

      // Feedback tháng này
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final monthlyResponse = await _supabase
          .from('feedback')
          .select('id')
          .eq('user_id', user.id)
          .gte('created_at', startOfMonth.toIso8601String());

      final thisMonth = (monthlyResponse as List).length;

      // Feedback gần nhất
      final latestResponse = await _supabase
          .from('feedback')
          .select('created_at')
          .eq('user_id', user.id)
          .order('created_at', ascending: false)
          .limit(1);

      DateTime? lastFeedback;
      if ((latestResponse as List).isNotEmpty) {
        lastFeedback = DateTime.parse(latestResponse.first['created_at']);
      }

      return {
        'total': total,
        'thisMonth': thisMonth,
        'lastFeedback': lastFeedback,
      };
    } catch (e) {
      print('❌ Lỗi khi lấy thống kê feedback: $e');
      return {
        'total': 0,
        'thisMonth': 0,
        'lastFeedback': null,
      };
    }
  }
}
