import 'dart:convert';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/user_profile.dart';

/// Service để sử dụng Gemini AI lọc và đánh giá video YouTube
class GeminiAIService {
  static const String _apiKey = 'AIzaSyBeOMsrxlGoJ_hS92xlnP0PXX1GX-KYtCE';
  static const String _modelName = 'gemini-1.5-flash-8b';
  
  late final GenerativeModel _model;
  
  // Cache để tránh re-analyze video đã xử lý
  final Map<String, VideoAnalysisResult> _analysisCache = {};
  
  GeminiAIService() {
    _model = GenerativeModel(
      model: _modelName,
      apiKey: _apiKey,
      generationConfig: GenerationConfig(
        temperature: 0.1, // Thấp để có kết quả ổn định
        topK: 1,
        topP: 0.8,
        maxOutputTokens: 1000,
      ),
    );
    print('🤖 GeminiAIService đã được khởi tạo với model: $_modelName');
  }

  /// Phân tích batch videos để tối ưu rate limit
  Future<List<VideoAnalysisResult>> analyzeVideosBatch(
    List<dynamic> videos,
    MealSuggestionQuery query,
  ) async {
    try {
      print('🤖 Bắt đầu phân tích ${videos.length} videos với Gemini AI...');
      
      // Kiểm tra cache trước
      final List<VideoAnalysisResult> results = [];
      final List<dynamic> videosToAnalyze = [];
      
      for (final video in videos) {
        final cacheKey = '${video.id}_${query.hashCode}';
        if (_analysisCache.containsKey(cacheKey)) {
          results.add(_analysisCache[cacheKey]!);
          print('📋 Sử dụng cache cho video: ${video.title}');
        } else {
          videosToAnalyze.add(video);
        }
      }
      
      if (videosToAnalyze.isEmpty) {
        print('✅ Tất cả videos đã có trong cache');
        return results;
      }
      
      // Phân tích batch (giảm xuống 5 videos/request để tránh response quá dài)
      const batchSize = 5;
      for (int i = 0; i < videosToAnalyze.length; i += batchSize) {
        final batch = videosToAnalyze.skip(i).take(batchSize).toList();
        final batchResults = await _analyzeBatch(batch, query);
        
        // Cache kết quả
        for (int j = 0; j < batch.length; j++) {
          final cacheKey = '${batch[j].id}_${query.hashCode}';
          _analysisCache[cacheKey] = batchResults[j];
        }
        
        results.addAll(batchResults);
        
        // Delay nhỏ giữa các batch để tránh rate limit
        if (i + batchSize < videosToAnalyze.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
      
      print('✅ Hoàn thành phân tích ${videos.length} videos');
      return results;
      
    } catch (e) {
      print('❌ Lỗi khi phân tích videos với Gemini: $e');
      // Fallback: trả về kết quả mặc định
      return videos.map((video) => VideoAnalysisResult(
        videoId: video.id,
        relevanceScore: 50, // Điểm trung bình
        confidence: 0.5,
        isRelevant: true,
      )).toList();
    }
  }

  /// Phân tích một batch videos
  Future<List<VideoAnalysisResult>> _analyzeBatch(
    List<dynamic> videos,
    MealSuggestionQuery query,
  ) async {
    final prompt = _buildBatchPrompt(videos, query);
    
    try {
      final response = await _model.generateContent([Content.text(prompt)]);
      final responseText = response.text;
      
      if (responseText == null) {
        throw Exception('Gemini trả về response rỗng');
      }
      
      print('🤖 Gemini response length: ${responseText.length} chars');

      // Clean và validate response
      String cleanedResponse = _cleanAndValidateResponse(responseText);

      if (cleanedResponse.isEmpty) {
        throw Exception('Response rỗng sau khi clean');
      }

      print('🤖 Cleaned response length: ${cleanedResponse.length} chars');

      // Parse JSON response với error handling
      final jsonResponse = jsonDecode(cleanedResponse);

      if (jsonResponse is! Map<String, dynamic>) {
        throw Exception('Response không phải là JSON object');
      }

      if (!jsonResponse.containsKey('analyses')) {
        throw Exception('Response thiếu key "analyses"');
      }

      final List<dynamic> analyses = jsonResponse['analyses'];

      if (analyses.isEmpty) {
        throw Exception('Analyses array rỗng');
      }
      
      return analyses.map((analysis) => VideoAnalysisResult.fromJson(analysis)).toList();
      
    } catch (e) {
      print('❌ Lỗi khi gọi Gemini API: $e');
      // Fallback: trả về kết quả mặc định
      return videos.map((video) => VideoAnalysisResult(
        videoId: video.id,
        relevanceScore: 50,
        confidence: 0.5,
        isRelevant: true,
      )).toList();
    }
  }

  /// Tạo prompt cho batch analysis
  String _buildBatchPrompt(List<dynamic> videos, MealSuggestionQuery query) {
    final queryDescription = _buildQueryDescription(query);
    
    // Chỉ gửi ID và tiêu đề để tiết kiệm token
    final videosJson = videos.map((video) => {
      'id': video.id,
      'title': video.title,
    }).toList();
    
    return '''
Phân tích TIÊU ĐỀ video YouTube cho: $queryDescription

Tiêu đề videos: ${jsonEncode(videosJson)}

Chỉ dựa vào TIÊU ĐỀ, đánh giá độ phù hợp (0-100):

{
  "analyses": [
    {
      "videoId": "string",
      "relevanceScore": 0-100,
      "confidence": 0.0-1.0,
      "isRelevant": true/false
    }
  ]
}

Quy tắc chấm điểm:
- Tiêu đề có đúng nguyên liệu yêu cầu: 90-100 điểm
- Tiêu đề có nguyên liệu tương tự: 70-89 điểm
- Tiêu đề có từ khóa liên quan: 50-69 điểm
- Tiêu đề không liên quan: 0-30 điểm
- Confidence cao nếu chắc chắn, thấp nếu không chắc
- Chỉ trả về JSON, không giải thích
''';
  }

  /// Tạo mô tả query cho prompt
  String _buildQueryDescription(MealSuggestionQuery query) {
    final parts = <String>[];
    
    // Meal type
    final mealTypeVi = {
      MealType.breakfast: 'bữa sáng',
      MealType.lunch: 'bữa trưa',
      MealType.dinner: 'bữa tối',
    };
    parts.add('Loại bữa ăn: ${mealTypeVi[query.mealType] ?? query.mealType.displayName}');
    
    // Vegetarian
    if (query.isVegetarian) {
      parts.add('Món chay (không thịt, không cá)');
    }
    
    // Preferred ingredients
    if (query.preferredIngredients.isNotEmpty) {
      final ingredients = query.preferredIngredients.map((i) => i.displayName).join(', ');
      parts.add('Nguyên liệu ưu tiên: $ingredients');
    }
    
    return parts.join('\n');
  }

  /// Clean và validate Gemini response
  String _cleanAndValidateResponse(String responseText) {
    try {
      String cleaned = responseText.trim();

      // Loại bỏ markdown code blocks
      if (cleaned.startsWith('```json')) {
        cleaned = cleaned.substring(7);
      }
      if (cleaned.startsWith('```')) {
        cleaned = cleaned.substring(3);
      }
      if (cleaned.endsWith('```')) {
        cleaned = cleaned.substring(0, cleaned.length - 3);
      }

      cleaned = cleaned.trim();

      // Kiểm tra JSON có hoàn chỉnh không
      if (!cleaned.startsWith('{') || !cleaned.endsWith('}')) {
        print('⚠️ JSON không hoàn chỉnh, cố gắng fix...');

        // Tìm vị trí bắt đầu và kết thúc JSON
        int startIndex = cleaned.indexOf('{');
        int lastBraceIndex = cleaned.lastIndexOf('}');

        if (startIndex != -1 && lastBraceIndex != -1 && lastBraceIndex > startIndex) {
          cleaned = cleaned.substring(startIndex, lastBraceIndex + 1);
          print('✅ Đã fix JSON: ${cleaned.length} chars');
        } else {
          throw Exception('Không thể fix JSON không hoàn chỉnh');
        }
      }

      // Validate JSON syntax bằng cách thử parse
      try {
        jsonDecode(cleaned);
        return cleaned;
      } catch (e) {
        print('❌ JSON syntax error: $e');

        // Cố gắng fix một số lỗi thường gặp
        cleaned = _fixCommonJsonErrors(cleaned);

        // Thử parse lại
        jsonDecode(cleaned);
        return cleaned;
      }

    } catch (e) {
      print('❌ Không thể clean response: $e');
      return '';
    }
  }

  /// Fix các lỗi JSON thường gặp
  String _fixCommonJsonErrors(String json) {
    String fixed = json;

    // Fix unterminated strings - thêm quote nếu thiếu
    fixed = _fixUnterminatedStrings(fixed);

    // Fix trailing commas
    fixed = fixed.replaceAll(RegExp(r',(\s*[}\]])'), r'$1');

    // Fix missing commas
    fixed = _fixMissingCommas(fixed);

    return fixed;
  }

  /// Fix unterminated strings
  String _fixUnterminatedStrings(String json) {
    try {
      // Tìm các string không được đóng đúng cách
      final lines = json.split('\n');
      final fixedLines = <String>[];

      for (String line in lines) {
        String fixedLine = line;

        // Đếm số lượng quotes
        int quoteCount = 0;
        for (int i = 0; i < line.length; i++) {
          if (line[i] == '"' && (i == 0 || line[i-1] != '\\')) {
            quoteCount++;
          }
        }

        // Nếu số quotes lẻ, thêm quote ở cuối
        if (quoteCount % 2 == 1) {
          // Tìm vị trí cuối cùng có thể thêm quote
          if (fixedLine.endsWith(',') || fixedLine.endsWith('}') || fixedLine.endsWith(']')) {
            fixedLine = fixedLine.substring(0, fixedLine.length - 1) + '"' + fixedLine.substring(fixedLine.length - 1);
          } else {
            fixedLine += '"';
          }
        }

        fixedLines.add(fixedLine);
      }

      return fixedLines.join('\n');
    } catch (e) {
      return json; // Trả về original nếu không fix được
    }
  }

  /// Fix missing commas
  String _fixMissingCommas(String json) {
    try {
      // Fix missing commas between objects/arrays
      json = json.replaceAll(RegExp(r'}\s*{'), '},{');
      json = json.replaceAll(RegExp(r']\s*\['), '],[');
      json = json.replaceAll(RegExp(r'}\s*\['), '},[');
      json = json.replaceAll(RegExp(r']\s*{'), '],{');

      return json;
    } catch (e) {
      return json;
    }
  }

  /// Clear cache để tiết kiệm memory
  void clearCache() {
    _analysisCache.clear();
    print('🗑️ Đã xóa cache phân tích AI');
  }
}

/// Kết quả phân tích video từ AI (chỉ dựa trên tiêu đề)
class VideoAnalysisResult {
  final String videoId;
  final int relevanceScore; // 0-100
  final double confidence; // 0.0-1.0
  final bool isRelevant;

  VideoAnalysisResult({
    required this.videoId,
    required this.relevanceScore,
    required this.confidence,
    required this.isRelevant,
  });

  factory VideoAnalysisResult.fromJson(Map<String, dynamic> json) {
    return VideoAnalysisResult(
      videoId: json['videoId'] ?? '',
      relevanceScore: (json['relevanceScore'] ?? 50).toInt(),
      confidence: (json['confidence'] ?? 0.5).toDouble(),
      isRelevant: json['isRelevant'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'videoId': videoId,
      'relevanceScore': relevanceScore,
      'confidence': confidence,
      'isRelevant': isRelevant,
    };
  }
}
