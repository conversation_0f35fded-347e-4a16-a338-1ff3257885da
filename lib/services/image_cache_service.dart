import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ImageCacheService {
  static const String _cacheDirectoryName = 'image_cache';
  static const String _cacheMetadataKey = 'image_cache_metadata';
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int _maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7 ngày
  
  // Singleton pattern
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();
  
  Directory? _cacheDirectory;
  Map<String, Map<String, dynamic>> _cacheMetadata = {};
  
  // Khởi tạo cache directory
  Future<void> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/$_cacheDirectoryName');
      
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }
      
      await _loadCacheMetadata();
      await _cleanExpiredCache();
    } catch (e) {
      print('Lỗi khởi tạo image cache: $e');
    }
  }
  
  // Tạo key cache từ URL
  String _generateCacheKey(String url) {
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return digest.toString();
  }
  
  // Lấy đường dẫn file cache
  String _getCacheFilePath(String cacheKey) {
    return '${_cacheDirectory!.path}/$cacheKey';
  }
  
  // Kiểm tra file có tồn tại trong cache không
  Future<bool> isCached(String url) async {
    if (_cacheDirectory == null) await initialize();
    
    final cacheKey = _generateCacheKey(url);
    final filePath = _getCacheFilePath(cacheKey);
    final file = File(filePath);
    
    if (await file.exists()) {
      // Kiểm tra thời gian hết hạn
      final metadata = _cacheMetadata[cacheKey];
      if (metadata != null) {
        final cachedTime = DateTime.parse(metadata['cachedAt']);
        final now = DateTime.now();
        
        if (now.difference(cachedTime).inMilliseconds < _maxCacheAge) {
          return true;
        } else {
          // File đã hết hạn, xóa
          await _removeCacheFile(cacheKey);
          return false;
        }
      }
    }
    
    return false;
  }
  
  // Lấy file từ cache
  Future<File?> getCachedFile(String url) async {
    if (await isCached(url)) {
      final cacheKey = _generateCacheKey(url);
      final filePath = _getCacheFilePath(cacheKey);
      final file = File(filePath);
      
      // Cập nhật thời gian truy cập
      _cacheMetadata[cacheKey]?['lastAccessed'] = DateTime.now().toIso8601String();
      await _saveCacheMetadata();
      
      return file;
    }
    
    return null;
  }
  
  // Lấy bytes từ cache
  Future<Uint8List?> getCachedBytes(String url) async {
    final file = await getCachedFile(url);
    if (file != null) {
      return await file.readAsBytes();
    }
    return null;
  }
  
  // Cache file từ URL
  Future<File?> cacheFromUrl(String url) async {
    try {
      if (_cacheDirectory == null) await initialize();
      
      // Kiểm tra đã cache chưa
      if (await isCached(url)) {
        return await getCachedFile(url);
      }
      
      // Download file
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        return await cacheBytes(url, response.bodyBytes);
      }
    } catch (e) {
      print('Lỗi cache từ URL $url: $e');
    }
    
    return null;
  }
  
  // Cache bytes với URL key
  Future<File?> cacheBytes(String url, Uint8List bytes) async {
    try {
      if (_cacheDirectory == null) await initialize();
      
      final cacheKey = _generateCacheKey(url);
      final filePath = _getCacheFilePath(cacheKey);
      final file = File(filePath);
      
      // Kiểm tra dung lượng cache
      await _ensureCacheSize(bytes.length);
      
      // Ghi file
      await file.writeAsBytes(bytes);
      
      // Cập nhật metadata
      _cacheMetadata[cacheKey] = {
        'url': url,
        'size': bytes.length,
        'cachedAt': DateTime.now().toIso8601String(),
        'lastAccessed': DateTime.now().toIso8601String(),
      };
      
      await _saveCacheMetadata();
      
      return file;
    } catch (e) {
      print('Lỗi cache bytes: $e');
      return null;
    }
  }
  
  // Cache file từ đường dẫn local
  Future<File?> cacheFromFile(String url, File sourceFile) async {
    try {
      final bytes = await sourceFile.readAsBytes();
      return await cacheBytes(url, bytes);
    } catch (e) {
      print('Lỗi cache từ file: $e');
      return null;
    }
  }
  
  // Xóa file cache
  Future<void> removeFromCache(String url) async {
    final cacheKey = _generateCacheKey(url);
    await _removeCacheFile(cacheKey);
  }
  
  // Xóa file cache theo key
  Future<void> _removeCacheFile(String cacheKey) async {
    try {
      final filePath = _getCacheFilePath(cacheKey);
      final file = File(filePath);
      
      if (await file.exists()) {
        await file.delete();
      }
      
      _cacheMetadata.remove(cacheKey);
      await _saveCacheMetadata();
    } catch (e) {
      print('Lỗi xóa cache file: $e');
    }
  }
  
  // Xóa toàn bộ cache
  Future<void> clearCache() async {
    try {
      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create(recursive: true);
      }
      
      _cacheMetadata.clear();
      await _saveCacheMetadata();
    } catch (e) {
      print('Lỗi xóa toàn bộ cache: $e');
    }
  }
  
  // Lấy thông tin cache
  Future<Map<String, dynamic>> getCacheInfo() async {
    if (_cacheDirectory == null) await initialize();
    
    int totalSize = 0;
    int fileCount = 0;
    
    for (final metadata in _cacheMetadata.values) {
      totalSize += metadata['size'] as int;
      fileCount++;
    }
    
    return {
      'totalSize': totalSize,
      'fileCount': fileCount,
      'maxSize': _maxCacheSize,
      'usagePercentage': (totalSize / _maxCacheSize * 100).round(),
    };
  }
  
  // Đảm bảo dung lượng cache không vượt quá giới hạn
  Future<void> _ensureCacheSize(int newFileSize) async {
    int currentSize = 0;
    for (final metadata in _cacheMetadata.values) {
      currentSize += metadata['size'] as int;
    }
    
    // Nếu thêm file mới sẽ vượt quá giới hạn
    if (currentSize + newFileSize > _maxCacheSize) {
      // Sắp xếp theo thời gian truy cập cuối
      final sortedEntries = _cacheMetadata.entries.toList()
        ..sort((a, b) {
          final aTime = DateTime.parse(a.value['lastAccessed']);
          final bTime = DateTime.parse(b.value['lastAccessed']);
          return aTime.compareTo(bTime);
        });
      
      // Xóa các file cũ nhất cho đến khi đủ dung lượng
      for (final entry in sortedEntries) {
        await _removeCacheFile(entry.key);
        currentSize -= entry.value['size'] as int;
        
        if (currentSize + newFileSize <= _maxCacheSize) {
          break;
        }
      }
    }
  }
  
  // Xóa cache đã hết hạn
  Future<void> _cleanExpiredCache() async {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheMetadata.entries) {
      final cachedTime = DateTime.parse(entry.value['cachedAt']);
      if (now.difference(cachedTime).inMilliseconds > _maxCacheAge) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      await _removeCacheFile(key);
    }
  }
  
  // Load metadata từ SharedPreferences
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = prefs.getString(_cacheMetadataKey);
      
      if (metadataJson != null) {
        final decoded = jsonDecode(metadataJson) as Map<String, dynamic>;
        _cacheMetadata = decoded.map(
          (key, value) => MapEntry(key, Map<String, dynamic>.from(value)),
        );
      }
    } catch (e) {
      print('Lỗi load cache metadata: $e');
      _cacheMetadata = {};
    }
  }
  
  // Lưu metadata vào SharedPreferences
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = jsonEncode(_cacheMetadata);
      await prefs.setString(_cacheMetadataKey, metadataJson);
    } catch (e) {
      print('Lỗi lưu cache metadata: $e');
    }
  }
  
  // Preload hình ảnh
  Future<void> preloadImages(List<String> urls) async {
    for (final url in urls) {
      if (!await isCached(url)) {
        await cacheFromUrl(url);
      }
    }
  }
  
  // Lấy URL cache local cho widget Image
  Future<String?> getLocalCacheUrl(String url) async {
    final file = await getCachedFile(url);
    return file?.path;
  }
  
  // Kiểm tra và tải hình ảnh với fallback
  Future<File?> getImageWithFallback(String url, {File? fallbackFile}) async {
    // Thử lấy từ cache trước
    File? cachedFile = await getCachedFile(url);
    if (cachedFile != null) {
      return cachedFile;
    }
    
    // Thử tải từ mạng
    cachedFile = await cacheFromUrl(url);
    if (cachedFile != null) {
      return cachedFile;
    }
    
    // Trả về fallback nếu có
    return fallbackFile;
  }
}