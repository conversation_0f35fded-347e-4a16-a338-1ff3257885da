import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_options.dart';
import 'dart:math';

/// Service để xử lý OAuth thủ công khi signInWithOAuth gặp lỗi
class ManualOAuthService {
  static final ManualOAuthService _instance = ManualOAuthService._internal();
  factory ManualOAuthService() => _instance;
  ManualOAuthService._internal();

  /// Tạo OAuth URL thủ công và mở bằng URL launcher
  Future<Map<String, dynamic>> signInWithGoogleManual() async {
    print('🔧 =================================');
    print('🔧 BẮT ĐẦU MANUAL GOOGLE OAUTH');
    print('🔧 =================================');

    try {
      // 1. Tạo PKCE parameters
      print('📋 STEP 1: Tạo PKCE parameters');
      final codeVerifier = _generateCodeVerifier();
      final codeChallenge = _generateCodeChallenge(codeVerifier);
      print('   ✓ Code verifier length: ${codeVerifier.length}');
      print('   ✓ Code challenge: $codeChallenge');

      // 2. Tạo OAuth URL
      print('📋 STEP 2: Tạo OAuth URL');
      final oauthUrl = _buildOAuthUrl(codeChallenge);
      print('   ✓ Manual OAuth URL: $oauthUrl');
      print('   ✓ URL length: ${oauthUrl.length}');

      // 3. Mở URL với nhiều fallback options
      print('📋 STEP 3: Launch URL với fallback options');
      final launched = await _launchUrl(oauthUrl);
      print('   ✓ Launch result: $launched');

      if (!launched) {
        print('   ❌ Tất cả launch methods đều thất bại');
        return {
          'success': false,
          'message': 'Không thể mở trình duyệt để đăng nhập. Vui lòng thử lại.',
          'debug': 'All URL launch methods failed'
        };
      }

      print('   ✅ Manual OAuth URL launched successfully');

      final result = {
        'success': true,
        'message': 'Đang xử lý đăng nhập Google...',
        'pending': true,
        'manual': true,
        'debug': 'Manual OAuth launched successfully'
      };

      print('   ✓ Returning result: $result');
      return result;

    } catch (e, stackTrace) {
      print('💥 =================================');
      print('💥 LỖI TRONG MANUAL OAUTH');
      print('💥 =================================');
      print('❌ Exception: $e');
      print('❌ Exception type: ${e.runtimeType}');
      print('❌ Stack trace:');
      print(stackTrace.toString());

      final errorResult = {
        'success': false,
        'message': 'Lỗi khi khởi tạo đăng nhập Google: $e',
        'debug': 'Manual OAuth exception',
        'error': e.toString(),
        'errorType': e.runtimeType.toString()
      };

      print('   ❌ Returning error result: $errorResult');
      return errorResult;
    } finally {
      print('🏁 =================================');
      print('🏁 KẾT THÚC MANUAL GOOGLE OAUTH');
      print('🏁 =================================');
    }
  }

  /// Tạo OAuth URL thủ công
  String _buildOAuthUrl(String codeChallenge) {
    final baseUrl = SupabaseConfig.url;
    final redirectUri = Uri.encodeComponent('com.minhduc.naugiday://callback');
    
    final params = {
      'provider': 'google',
      'redirect_to': redirectUri,
      'flow_type': 'pkce',
      'code_challenge': codeChallenge,
      'code_challenge_method': 's256',
    };

    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl/auth/v1/authorize?$queryString';
  }

  /// Launch URL với nhiều fallback options
  Future<bool> _launchUrl(String url) async {
    print('🚀 =================================');
    print('🚀 BẮT ĐẦU LAUNCH URL');
    print('🚀 =================================');
    print('   🔗 URL: $url');

    final uri = Uri.parse(url);
    print('   📋 Parsed URI: $uri');
    print('   📋 URI scheme: ${uri.scheme}');
    print('   📋 URI host: ${uri.host}');
    print('   📋 URI path: ${uri.path}');

    // Option 1: External application
    print('📋 OPTION 1: External Application');
    try {
      print('   ⏳ Checking canLaunchUrl...');
      final canLaunch = await canLaunchUrl(uri);
      print('   ✓ canLaunchUrl result: $canLaunch');

      if (canLaunch) {
        print('   ⏳ Launching with externalApplication...');
        final launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        print('   ✓ Launch result: $launched');

        if (launched) {
          print('   ✅ SUCCESS: Launched with externalApplication');
          return true;
        } else {
          print('   ❌ FAILED: externalApplication returned false');
        }
      } else {
        print('   ❌ SKIPPED: canLaunchUrl returned false');
      }
    } catch (e, stackTrace) {
      print('   ❌ EXCEPTION in externalApplication: $e');
      print('   ❌ Stack trace: $stackTrace');
    }

    // Option 2: Platform default
    print('📋 OPTION 2: Platform Default');
    try {
      print('   ⏳ Launching with platformDefault...');
      final launched = await launchUrl(
        uri,
        mode: LaunchMode.platformDefault,
      );
      print('   ✓ Launch result: $launched');

      if (launched) {
        print('   ✅ SUCCESS: Launched with platformDefault');
        return true;
      } else {
        print('   ❌ FAILED: platformDefault returned false');
      }
    } catch (e, stackTrace) {
      print('   ❌ EXCEPTION in platformDefault: $e');
      print('   ❌ Stack trace: $stackTrace');
    }

    // Option 3: In-app web view
    print('📋 OPTION 3: In-App WebView');
    try {
      print('   ⏳ Launching with inAppWebView...');
      final launched = await launchUrl(
        uri,
        mode: LaunchMode.inAppWebView,
      );
      print('   ✓ Launch result: $launched');

      if (launched) {
        print('   ✅ SUCCESS: Launched with inAppWebView');
        return true;
      } else {
        print('   ❌ FAILED: inAppWebView returned false');
      }
    } catch (e, stackTrace) {
      print('   ❌ EXCEPTION in inAppWebView: $e');
      print('   ❌ Stack trace: $stackTrace');
    }

    print('💥 =================================');
    print('💥 TẤT CẢ LAUNCH METHODS ĐỀU THẤT BẠI');
    print('💥 =================================');
    return false;
  }

  /// Tạo code verifier cho PKCE
  String _generateCodeVerifier() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    final random = Random.secure();
    return List.generate(128, (i) => chars[random.nextInt(chars.length)]).join();
  }

  /// Tạo code challenge từ code verifier
  String _generateCodeChallenge(String codeVerifier) {
    // Simplified version - trong production nên sử dụng SHA256
    return codeVerifier;
  }
}
