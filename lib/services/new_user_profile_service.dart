import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_profile.dart';
import 'auth_service.dart';

/// Service quản lý user profile với schema mới
class NewUserProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthService _authService = AuthService();

  /// Lấy user profile từ database
  Future<Map<String, dynamic>> getUserProfile(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select()
          .eq('user_id', userId)
          .single();

      final profile = UserProfile.fromJson(response);

      return {
        'success': true,
        'profile': profile,
      };
    } catch (e) {
      print('Lỗi khi lấy user profile: $e');
      return {
        'success': false,
        'message': 'Không thể lấy thông tin người dùng: $e',
      };
    }
  }

  /// Tạo user profile mới
  Future<Map<String, dynamic>> createUserProfile(UserProfile profile) async {
    try {
      await _supabase
          .from('user_profiles')
          .insert(profile.toJson());

      return {
        'success': true,
        'message': 'Tạo profile thành công',
      };
    } catch (e) {
      print('Lỗi khi tạo user profile: $e');
      return {
        'success': false,
        'message': 'Không thể tạo profile: $e',
      };
    }
  }

  /// Cập nhật user profile
  Future<Map<String, dynamic>> updateUserProfile(UserProfile profile) async {
    try {
      await _supabase
          .from('user_profiles')
          .update(profile.toJson())
          .eq('user_id', profile.userId);

      return {
        'success': true,
        'message': 'Cập nhật profile thành công',
      };
    } catch (e) {
      print('Lỗi khi cập nhật user profile: $e');
      return {
        'success': false,
        'message': 'Không thể cập nhật profile: $e',
      };
    }
  }

  /// Kiểm tra xem user đã hoàn thành onboarding chưa
  Future<Map<String, dynamic>> checkOnboardingStatus(String userId) async {
    try {
      final response = await _supabase
          .from('user_profiles')
          .select('is_onboarding_completed')
          .eq('user_id', userId)
          .single();

      final isCompleted = response['is_onboarding_completed'] ?? false;

      return {
        'success': true,
        'is_completed': isCompleted,
      };
    } catch (e) {
      // Nếu không tìm thấy profile, nghĩa là chưa onboarding
      return {
        'success': true,
        'is_completed': false,
      };
    }
  }

  /// Đánh dấu hoàn thành onboarding
  Future<Map<String, dynamic>> completeOnboarding(String userId) async {
    try {
      await _supabase
          .from('user_profiles')
          .update({'is_onboarding_completed': true})
          .eq('user_id', userId);

      return {
        'success': true,
        'message': 'Hoàn thành onboarding',
      };
    } catch (e) {
      print('Lỗi khi hoàn thành onboarding: $e');
      return {
        'success': false,
        'message': 'Không thể hoàn thành onboarding: $e',
      };
    }
  }

  /// Lưu lịch sử gợi ý món ăn
  Future<Map<String, dynamic>> saveSuggestionHistory({
    required String userId,
    required MealSuggestionQuery query,
    required int resultsCount,
    String? selectedVideoId,
  }) async {
    try {
      await _supabase
          .from('meal_suggestion_history')
          .insert({
            'user_id': userId,
            'meal_type': query.mealType.value,
            'is_vegetarian': query.isVegetarian,
            'preferred_ingredients': query.preferredIngredients.map((e) => e.value).toList(),
            'auto_suggest': query.autoSuggest,
            'search_query': query.toYouTubeSearchQuery(),
            'results_count': resultsCount,
            'selected_video_id': selectedVideoId,
          });

      return {
        'success': true,
        'message': 'Lưu lịch sử thành công',
      };
    } catch (e) {
      print('Lỗi khi lưu lịch sử gợi ý: $e');
      return {
        'success': false,
        'message': 'Không thể lưu lịch sử: $e',
      };
    }
  }

  /// Lấy lịch sử gợi ý món ăn
  Future<Map<String, dynamic>> getSuggestionHistory(String userId, {int limit = 20}) async {
    try {
      final response = await _supabase
          .from('meal_suggestion_history')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(limit);

      return {
        'success': true,
        'history': response,
      };
    } catch (e) {
      print('Lỗi khi lấy lịch sử gợi ý: $e');
      return {
        'success': false,
        'message': 'Không thể lấy lịch sử: $e',
        'history': [],
      };
    }
  }

  /// Lấy user preferences
  Future<Map<String, dynamic>> getUserPreferences(String userId) async {
    try {
      final response = await _supabase
          .from('user_preferences')
          .select()
          .eq('user_id', userId)
          .single();

      return {
        'success': true,
        'preferences': response,
      };
    } catch (e) {
      // Nếu chưa có preferences, tạo mặc định
      return await _createDefaultPreferences(userId);
    }
  }

  /// Tạo preferences mặc định
  Future<Map<String, dynamic>> _createDefaultPreferences(String userId) async {
    try {
      final defaultPreferences = {
        'user_id': userId,
        'theme_mode': 'system',
        'language': 'vi',
        'notifications_enabled': true,
        'meal_reminders_enabled': true,
        'weekly_planning_reminders': true,
        'auto_suggest_enabled': false,
        'preferred_video_duration_max': 1800,
      };

      await _supabase
          .from('user_preferences')
          .insert(defaultPreferences);

      return {
        'success': true,
        'preferences': defaultPreferences,
      };
    } catch (e) {
      print('Lỗi khi tạo preferences mặc định: $e');
      return {
        'success': false,
        'message': 'Không thể tạo preferences: $e',
        'preferences': {},
      };
    }
  }

  /// Cập nhật user preferences
  Future<Map<String, dynamic>> updateUserPreferences(String userId, Map<String, dynamic> preferences) async {
    try {
      await _supabase
          .from('user_preferences')
          .update(preferences)
          .eq('user_id', userId);

      return {
        'success': true,
        'message': 'Cập nhật preferences thành công',
      };
    } catch (e) {
      print('Lỗi khi cập nhật preferences: $e');
      return {
        'success': false,
        'message': 'Không thể cập nhật preferences: $e',
      };
    }
  }

  /// Lấy thống kê user
  Future<Map<String, dynamic>> getUserStatistics(String userId) async {
    try {
      final response = await _supabase
          .rpc('get_user_statistics', params: {'user_uuid': userId});

      return {
        'success': true,
        'statistics': response,
      };
    } catch (e) {
      print('Lỗi khi lấy thống kê user: $e');
      return {
        'success': false,
        'message': 'Không thể lấy thống kê: $e',
        'statistics': {},
      };
    }
  }

  /// Xóa user profile và tất cả dữ liệu liên quan
  Future<Map<String, dynamic>> deleteUserProfile(String userId) async {
    try {
      // Xóa theo thứ tự để tránh foreign key constraints
      await _supabase.from('meal_suggestion_history').delete().eq('user_id', userId);
      await _supabase.from('weekly_menu_items').delete().eq('user_id', userId);
      await _supabase.from('user_preferences').delete().eq('user_id', userId);
      await _supabase.from('user_profiles').delete().eq('user_id', userId);

      return {
        'success': true,
        'message': 'Xóa profile thành công',
      };
    } catch (e) {
      print('Lỗi khi xóa user profile: $e');
      return {
        'success': false,
        'message': 'Không thể xóa profile: $e',
      };
    }
  }

  /// Export dữ liệu user
  Future<Map<String, dynamic>> exportUserData(String userId) async {
    try {
      final profile = await getUserProfile(userId);
      final preferences = await getUserPreferences(userId);
      final history = await getSuggestionHistory(userId, limit: 1000);
      final statistics = await getUserStatistics(userId);

      // Lấy weekly menu items
      final menuItems = await _supabase
          .from('weekly_menu_items')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'user_id': userId,
        'profile': profile['success'] ? profile['profile'] : null,
        'preferences': preferences['success'] ? preferences['preferences'] : null,
        'suggestion_history': history['success'] ? history['history'] : [],
        'weekly_menu_items': menuItems,
        'statistics': statistics['success'] ? statistics['statistics'] : {},
      };

      return {
        'success': true,
        'data': exportData,
      };
    } catch (e) {
      print('Lỗi khi export dữ liệu user: $e');
      return {
        'success': false,
        'message': 'Không thể export dữ liệu: $e',
      };
    }
  }
}
