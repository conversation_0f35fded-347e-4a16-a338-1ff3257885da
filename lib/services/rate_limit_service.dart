import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_rate_limit_service.dart';

class RateLimitService {
  static const String _keyPrefix = 'rate_limit_';
  static const String _countPrefix = 'rate_count_';
  static const int _maxAttempts = 3; // Tối đa 3 lần gửi
  static const int _cooldownMinutes = 60; // Cooldown 60 phút

  /// Kiểm tra xem có thể gửi email không (sử dụng Supabase Auth Logs)
  static Future<Map<String, dynamic>> canSendEmail(String email) async {
    print('🔍 Checking rate limit for email: $email');
    
    try {
      // Thử sử dụng Supabase Auth Logs API trước
      final supabaseResult = await SupabaseRateLimitService.getPasswordResetRateLimit(email);
      
      if (supabaseResult['success'] == true) {
        print('✅ Got rate limit data from Supabase Auth Logs');
        
        return {
          'canSend': supabaseResult['canSend'],
          'remainingAttempts': supabaseResult['remainingAttempts'] ?? _maxAttempts,
          'cooldownMinutes': (supabaseResult['cooldownSeconds'] ?? 0) ~/ 60,
          'cooldownSeconds': supabaseResult['cooldownSeconds'] ?? 0,
          'message': supabaseResult['message'],
          'source': 'supabase_logs',
          'lastAttemptTime': supabaseResult['lastAttemptTime'],
          'totalAttempts': supabaseResult['totalAttempts'] ?? 0,
        };
      } else {
        print('⚠️ Supabase Auth Logs failed, falling back to local storage');
        print('Error: ${supabaseResult['error']}');
      }
    } catch (e) {
      print('❌ Exception when checking Supabase Auth Logs: $e');
    }
    
    // Fallback to local storage method
    return await _canSendEmailLocal(email);
  }
  
  /// Fallback method sử dụng local storage
  static Future<Map<String, dynamic>> _canSendEmailLocal(String email) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_keyPrefix$email';
    final countKey = '$_countPrefix$email';
    
    final lastAttemptTime = prefs.getInt(key) ?? 0;
    final attemptCount = prefs.getInt(countKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // Kiểm tra xem đã hết thời gian cooldown chưa
    final timeDiff = now - lastAttemptTime;
    final minutesPassed = timeDiff / (1000 * 60);
    
    if (minutesPassed >= _cooldownMinutes) {
      // Reset counter nếu đã hết thời gian cooldown
      await prefs.remove(countKey);
      await prefs.remove(key);
      return {
        'canSend': true,
        'remainingAttempts': _maxAttempts,
        'cooldownMinutes': 0,
        'cooldownSeconds': 0,
        'source': 'local_storage',
      };
    }
    
    if (attemptCount >= _maxAttempts) {
      final remainingMinutes = _cooldownMinutes - minutesPassed.ceil();
      return {
        'canSend': false,
        'remainingAttempts': 0,
        'cooldownMinutes': remainingMinutes > 0 ? remainingMinutes : 0,
        'cooldownSeconds': (remainingMinutes > 0 ? remainingMinutes : 0) * 60,
        'message': 'Bạn đã gửi quá nhiều yêu cầu. Vui lòng đợi $remainingMinutes phút.',
        'source': 'local_storage',
      };
    }
    
    return {
      'canSend': true,
      'remainingAttempts': _maxAttempts - attemptCount,
      'cooldownMinutes': 0,
      'cooldownSeconds': 0,
      'source': 'local_storage',
    };
  }
  
  /// Ghi nhận một lần gửi email
  static Future<void> recordEmailSent(String email) async {
    // Ghi nhận vào local storage (fallback)
    final prefs = await SharedPreferences.getInstance();
    final key = '$_keyPrefix$email';
    final countKey = '$_countPrefix$email';
    final now = DateTime.now().millisecondsSinceEpoch;
    
    final currentCount = prefs.getInt(countKey) ?? 0;
    await prefs.setInt(countKey, currentCount + 1);
    await prefs.setInt(key, now);
    
    // Đồng thời ghi nhận vào Supabase database
    try {
      await SupabaseRateLimitService.recordOtpRequest(
        email: email,
        eventType: 'password_reset',
      );
      print('✅ Recorded OTP request to Supabase database');
    } catch (e) {
      print('⚠️ Failed to record to Supabase, using local storage only: $e');
    }
  }
  
  /// Ghi nhận rate limit từ server (khi nhận được lỗi rate limit)
  static Future<void> recordRateLimit(String email) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_keyPrefix$email';
    final countKey = '$_countPrefix$email';
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // Set count to max để trigger cooldown
    await prefs.setInt(countKey, _maxAttempts);
    await prefs.setInt(key, now);
  }
  
  /// Reset rate limit cho email (dùng cho admin hoặc development)
  static Future<void> resetRateLimit(String email) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_keyPrefix$email';
    final countKey = '$_countPrefix$email';
    
    await prefs.remove(key);
    await prefs.remove(countKey);
  }
  
  /// Lấy thông tin chi tiết về rate limit
  static Future<Map<String, dynamic>> getRateLimitInfo(String email) async {
    final prefs = await SharedPreferences.getInstance();
    final key = '$_keyPrefix$email';
    final countKey = '$_countPrefix$email';
    
    final lastAttemptTime = prefs.getInt(key) ?? 0;
    final attemptCount = prefs.getInt(countKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    if (lastAttemptTime == 0) {
      return {
        'hasAttempts': false,
        'attemptCount': 0,
        'remainingMinutes': 0,
      };
    }
    
    final timeDiff = now - lastAttemptTime;
    final minutesPassed = timeDiff / (1000 * 60);
    final remainingMinutes = _cooldownMinutes - minutesPassed.ceil();
    
    return {
      'hasAttempts': true,
      'attemptCount': attemptCount,
      'remainingMinutes': remainingMinutes > 0 ? remainingMinutes : 0,
      'lastAttemptTime': DateTime.fromMillisecondsSinceEpoch(lastAttemptTime),
    };
  }
}
