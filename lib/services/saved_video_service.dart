import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/saved_video.dart';
import 'youtube_service.dart';

/// Service để quản lý video đã lưu của người dùng
class SavedVideoService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Lưu video vào danh sách đã lưu của user
  Future<Map<String, dynamic>> saveVideo(YouTubeVideo video) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'message': 'Bạn cần đăng nhập để lưu video',
        };
      }

      // Kiểm tra video đã được lưu chưa
      final existingVideo = await _supabase
          .from('saved_videos')
          .select()
          .eq('user_id', user.id)
          .eq('video_id', video.id)
          .maybeSingle();

      if (existingVideo != null) {
        return {
          'success': false,
          'message': 'Video này đã đượ<PERSON> lưu trước đó',
        };
      }

      // Lưu video mới
      final savedVideo = SavedVideo(
        userId: user.id,
        video: video,
        tags: _generateTags(video),
      );

      await _supabase.from('saved_videos').insert(savedVideo.toJson());

      return {
        'success': true,
        'message': 'Đã lưu video "${video.title}" thành công',
      };
    } catch (e) {
      print('❌ Lỗi khi lưu video: $e');
      return {
        'success': false,
        'message': 'Có lỗi xảy ra khi lưu video. Vui lòng thử lại.',
      };
    }
  }

  /// Lấy danh sách video đã lưu của user
  Future<List<SavedVideo>> getSavedVideos() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return [];
      }

      final response = await _supabase
          .from('saved_videos')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return response.map<SavedVideo>((json) => SavedVideo.fromJson(json)).toList();
    } catch (e) {
      print('❌ Lỗi khi lấy video đã lưu: $e');
      return [];
    }
  }

  /// Xóa video khỏi danh sách đã lưu
  Future<Map<String, dynamic>> removeSavedVideo(String videoId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'message': 'Bạn cần đăng nhập để xóa video',
        };
      }

      await _supabase
          .from('saved_videos')
          .delete()
          .eq('user_id', user.id)
          .eq('video_id', videoId);

      return {
        'success': true,
        'message': 'Đã xóa video khỏi danh sách đã lưu',
      };
    } catch (e) {
      print('❌ Lỗi khi xóa video đã lưu: $e');
      return {
        'success': false,
        'message': 'Có lỗi xảy ra khi xóa video. Vui lòng thử lại.',
      };
    }
  }

  /// Kiểm tra video đã được lưu chưa
  Future<bool> isVideoSaved(String videoId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      final response = await _supabase
          .from('saved_videos')
          .select('id')
          .eq('user_id', user.id)
          .eq('video_id', videoId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      print('❌ Lỗi khi kiểm tra video đã lưu: $e');
      return false;
    }
  }

  /// Tạo tags tự động cho video
  List<String> _generateTags(YouTubeVideo video) {
    final tags = <String>[];
    final title = video.title.toLowerCase();
    final description = video.description.toLowerCase();

    // Tags cơ bản
    if (title.contains('nấu') || title.contains('làm') || title.contains('recipe')) {
      tags.add('nấu ăn');
    }
    if (title.contains('cách') || title.contains('how to')) {
      tags.add('hướng dẫn');
    }
    if (title.contains('ngon') || title.contains('delicious')) {
      tags.add('ngon');
    }
    if (title.contains('đơn giản') || title.contains('easy')) {
      tags.add('đơn giản');
    }

    // Tags theo loại món
    if (title.contains('phở') || title.contains('bún') || title.contains('mì')) {
      tags.add('món nước');
    }
    if (title.contains('cơm') || title.contains('rice')) {
      tags.add('cơm');
    }
    if (title.contains('thịt') || title.contains('meat')) {
      tags.add('thịt');
    }
    if (title.contains('cá') || title.contains('fish')) {
      tags.add('cá');
    }
    if (title.contains('rau') || title.contains('vegetable')) {
      tags.add('rau');
    }

    return tags;
  }
}
