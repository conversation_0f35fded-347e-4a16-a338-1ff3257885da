import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Simple OAuth service - chỉ listen auth state thay vì xử lý callback
class SimpleOAuthService {
  static final SimpleOAuthService _instance = SimpleOAuthService._internal();
  factory SimpleOAuthService() => _instance;
  SimpleOAuthService._internal();

  StreamSubscription<AuthState>? _authSubscription;
  final StreamController<bool> _authSuccessController = StreamController<bool>.broadcast();

  /// Stream để listen auth success events
  Stream<bool> get authSuccessStream => _authSuccessController.stream;

  /// Khởi tạo auth state listener
  void initialize() {
    print('🎯 =================================');
    print('🎯 INITIALIZING SIMPLE OAUTH SERVICE');
    print('🎯 =================================');
    
    final supabase = Supabase.instance.client;
    
    // Listen for auth state changes
    _authSubscription = supabase.auth.onAuthStateChange.listen((data) {
      print('🎯 Auth state change detected:');
      print('   ✓ Event: ${data.event}');
      print('   ✓ Session exists: ${data.session != null}');
      
      if (data.event == AuthChangeEvent.signedIn && data.session != null) {
        print('   ✅ User signed in successfully!');
        print('   ✓ User ID: ${data.session!.user.id}');
        print('   ✓ User email: ${data.session!.user.email}');
        
        // Lưu user data
        _saveUserData(data.session!.user);
        
        // Broadcast success
        _authSuccessController.add(true);
      } else if (data.event == AuthChangeEvent.signedOut) {
        print('   ℹ️ User signed out');
        _authSuccessController.add(false);
      }
    });
    
    print('   ✅ Auth state listener initialized');
  }

  /// Lưu user data
  Future<void> _saveUserData(User user) async {
    try {
      print('   📋 Saving user data...');
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_email', user.email ?? '');
      await prefs.setString('user_id', user.id);
      await prefs.setString('display_name', user.userMetadata?['full_name'] ?? '');
      await prefs.setString('photo_url', user.userMetadata?['avatar_url'] ?? '');
      await prefs.setBool('rememberLogin', true);
      
      print('   ✅ User data saved successfully');
    } catch (e) {
      print('   ❌ Error saving user data: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _authSubscription?.cancel();
    _authSuccessController.close();
  }
}
