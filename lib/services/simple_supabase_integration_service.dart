import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_service.dart';
import 'image_cache_service.dart';

/// Simplified Supabase Integration Service after cleanup
class SimpleSupabaseIntegrationService {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  // Services (simplified after cleanup)
  late final AuthService _authService;
  late final ImageCacheService _imageCacheService;
  
  // Singleton pattern
  static SimpleSupabaseIntegrationService? _instance;
  static SimpleSupabaseIntegrationService get instance {
    _instance ??= SimpleSupabaseIntegrationService._internal();
    return _instance!;
  }
  
  SimpleSupabaseIntegrationService._internal();
  
  // Getters cho các services
  AuthService get auth => _authService;
  ImageCacheService get imageCache => _imageCacheService;
  
  // Khởi tạo tất cả services
  Future<void> initialize() async {
    _authService = AuthService();
    _imageCacheService = ImageCacheService();
    
    print('✅ SimpleSupabaseIntegrationService initialized');
  }
  
  // Kiểm tra kết nối Supabase
  Future<Map<String, dynamic>> checkConnection() async {
    try {
      final response = await _supabase.from('user_profiles').select('count').limit(1);
      return {
        'success': true,
        'message': 'Kết nối Supabase thành công',
        'data': response,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi kết nối Supabase: $e',
      };
    }
  }
  
  // Xóa tất cả dữ liệu local
  Future<Map<String, dynamic>> clearAllLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Danh sách các keys cần xóa
      final keysToRemove = [
        'user_profile',
        'user_preferences',
        'last_sync_timestamp',
        'app_language',
        'theme_mode',
      ];
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      // Xóa image cache
      await _imageCacheService.clearCache();
      
      return {
        'success': true,
        'message': 'Xóa tất cả dữ liệu local thành công',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi xóa dữ liệu local: $e',
      };
    }
  }
  
  // Lấy thông tin trạng thái
  Future<Map<String, dynamic>> getStatus() async {
    try {
      final user = _supabase.auth.currentUser;
      final connectionStatus = await checkConnection();
      
      return {
        'success': true,
        'data': {
          'user_authenticated': user != null,
          'user_id': user?.id,
          'user_email': user?.email,
          'connection_status': connectionStatus['success'],
          'timestamp': DateTime.now().toIso8601String(),
        },
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi lấy trạng thái: $e',
      };
    }
  }
  
  // Reset service
  void reset() {
    _instance = null;
  }
}
