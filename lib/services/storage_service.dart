import 'dart:io';
import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'auth_service.dart';

class StorageService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthService _authService = AuthService();
  final String _bucketName = 'dish_images';
  
  // Kiểm tra bucket có tồn tại không
  Future<void> initializeBucket() async {
    try {
      // Giả định bucket đã tồn tại (đã được kiểm tra qua MCP)
      print('✅ Bucket "$_bucketName" đã được cấu hình và sẵn sàng sử dụng');
      
      // Thử lấy thông tin bucket để xác nhận kết nối
      try {
        await _supabase.storage.from(_bucketName).list();
        print('✅ Kết nối thành công với bucket "$_bucketName"');
      } catch (listError) {
        // Không báo lỗi ngay cả khi không list được, có thể do quyền truy cập
        print('ℹ️ Không thể liệt kê nội dung bucket, nhưng vẫn tiếp tục: $listError');
      }
    } catch (e) {
      // Không hiển thị lỗi, chỉ ghi log
      print('ℹ️ Lưu ý: $e');
    }
  }
  
  // Upload hình ảnh từ file
  Future<Map<String, dynamic>> uploadImageFromFile(File imageFile) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return {
          'success': false,
          'message': 'Người dùng chưa đăng nhập',
        };
      }
      
      // Tạo tên file unique
      final fileExtension = path.extension(imageFile.path);
      final fileName = '${user.id}/${const Uuid().v4()}$fileExtension';
      
      // Upload file
      final response = await _supabase.storage
          .from(_bucketName)
          .upload(fileName, imageFile);
      
      // Lấy public URL
      final publicUrl = _supabase.storage
          .from(_bucketName)
          .getPublicUrl(fileName);
      
      return {
        'success': true,
        'url': publicUrl,
        'fileName': fileName,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi upload hình ảnh: $e',
      };
    }
  }
  
  // Upload hình ảnh từ bytes
  Future<Map<String, dynamic>> uploadImageFromBytes(
    Uint8List imageBytes, 
    String fileName
  ) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return {
          'success': false,
          'message': 'Người dùng chưa đăng nhập',
        };
      }
      
      // Tạo tên file unique
      final fileExtension = path.extension(fileName);
      final uniqueFileName = '${user.id}/${const Uuid().v4()}$fileExtension';
      
      // Upload bytes
      final response = await _supabase.storage
          .from(_bucketName)
          .uploadBinary(uniqueFileName, imageBytes);
      
      // Lấy public URL
      final publicUrl = _supabase.storage
          .from(_bucketName)
          .getPublicUrl(uniqueFileName);
      
      return {
        'success': true,
        'url': publicUrl,
        'fileName': uniqueFileName,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi upload hình ảnh: $e',
      };
    }
  }
  
  // Xóa hình ảnh
  Future<Map<String, dynamic>> deleteImage(String fileName) async {
    try {
      await _supabase.storage
          .from(_bucketName)
          .remove([fileName]);
      
      return {
        'success': true,
        'message': 'Xóa hình ảnh thành công',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi xóa hình ảnh: $e',
      };
    }
  }
  
  // Lấy danh sách hình ảnh của người dùng
  Future<Map<String, dynamic>> getUserImages() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return {
          'success': false,
          'message': 'Người dùng chưa đăng nhập',
        };
      }
      
      final files = await _supabase.storage
          .from(_bucketName)
          .list(path: user.id);
      
      final imageUrls = files.map((file) => {
        'name': file.name,
        'url': _supabase.storage
            .from(_bucketName)
            .getPublicUrl('${user.id}/${file.name}'),
        'size': file.metadata?['size'],
        'lastModified': file.metadata?['lastModified'],
      }).toList();
      
      return {
        'success': true,
        'images': imageUrls,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi lấy danh sách hình ảnh: $e',
      };
    }
  }
  
  // Tạo thumbnail cho hình ảnh
  Future<Map<String, dynamic>> createThumbnail(
    String originalFileName,
    {int width = 300, int height = 300}
  ) async {
    try {
      // Supabase có thể tự động tạo thumbnail thông qua transform
      final thumbnailUrl = _supabase.storage
          .from(_bucketName)
          .getPublicUrl(
            originalFileName,
            transform: TransformOptions(
              width: width,
              height: height,
              resize: ResizeMode.cover,
            ),
          );
      
      return {
        'success': true,
        'thumbnailUrl': thumbnailUrl,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi tạo thumbnail: $e',
      };
    }
  }
  
  // Lấy thông tin storage
  Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return {
          'totalFiles': 0,
          'totalSize': 0,
          'bucketName': _bucketName,
        };
      }
      
      final files = await _supabase.storage
          .from(_bucketName)
          .list(path: user.id);
      
      int totalSize = 0;
      for (final file in files) {
        final size = file.metadata?['size'];
        if (size is int) {
          totalSize += size;
        }
      }
      
      return {
        'totalFiles': files.length,
        'totalSize': totalSize,
        'bucketName': _bucketName,
      };
    } catch (e) {
      return {
        'totalFiles': 0,
        'totalSize': 0,
        'bucketName': _bucketName,
        'error': e.toString(),
      };
    }
  }
  
  // Kiểm tra bucket có tồn tại không
  Future<Map<String, dynamic>> checkBucketExists() async {
    try {
      final buckets = await _supabase.storage.listBuckets();
      final bucketExists = buckets.any((bucket) => bucket.name == _bucketName);
      
      return {
        'exists': bucketExists,
        'bucketName': _bucketName,
        'isHealthy': bucketExists,
      };
    } catch (e) {
      return {
        'exists': false,
        'bucketName': _bucketName,
        'isHealthy': false,
        'error': e.toString(),
      };
    }
  }
  
  // Kiểm tra kích thước file
  bool isValidFileSize(File file, {int maxSizeInMB = 5}) {
    final fileSizeInBytes = file.lengthSync();
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSizeInBytes <= maxSizeInBytes;
  }
  
  // Kiểm tra định dạng file
  bool isValidImageFormat(String fileName) {
    final allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    final fileExtension = path.extension(fileName).toLowerCase();
    return allowedExtensions.contains(fileExtension);
  }
  
  // Lấy URL với transform options
  String getTransformedImageUrl(
    String fileName, {
    int? width,
    int? height,
    ResizeMode resize = ResizeMode.cover,
    int quality = 80,
  }) {
    return _supabase.storage
        .from(_bucketName)
        .getPublicUrl(
          fileName,
          transform: TransformOptions(
            width: width,
            height: height,
            resize: resize,
            quality: quality,
          ),
        );
  }
}