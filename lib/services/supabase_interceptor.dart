import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service để intercept và xử lý các request/response từ Supabase
/// Gi<PERSON><PERSON> theo dõi, log và xử lý lỗi một cách tập trung
class SupabaseInterceptor {
  static final SupabaseInterceptor _instance = SupabaseInterceptor._internal();
  factory SupabaseInterceptor() => _instance;
  SupabaseInterceptor._internal();

  late final SupabaseClient _supabase;
  StreamSubscription<AuthState>? _authSubscription;
  bool _isInitialized = false;

  /// Khởi tạo interceptor
  static Future<void> initialize() async {
    final instance = SupabaseInterceptor();
    await instance._initialize();
  }

  Future<void> _initialize() async {
    if (_isInitialized) {
      print('SupabaseInterceptor đã được khởi tạo trước đó');
      return;
    }

    try {
      _supabase = Supabase.instance.client;
      
      // Thiết lập auth state listener
      _setupAuthStateListener();
      
      // Thiết lập error handling
      _setupErrorHandling();
      
      _isInitialized = true;
      print('✅ SupabaseInterceptor đã được khởi tạo thành công');
    } catch (e) {
      print('❌ Lỗi khởi tạo SupabaseInterceptor: $e');
      rethrow;
    }
  }

  /// Thiết lập listener cho auth state changes
  void _setupAuthStateListener() {
    _authSubscription = _supabase.auth.onAuthStateChange.listen(
      (data) async {
        final event = data.event;
        final session = data.session;
        
        print('🔐 Auth state changed: $event');
        
        switch (event) {
          case AuthChangeEvent.signedIn:
            await _handleSignIn(session);
            break;
          case AuthChangeEvent.signedOut:
            await _handleSignOut();
            break;
          case AuthChangeEvent.tokenRefreshed:
            await _handleTokenRefresh(session);
            break;
          case AuthChangeEvent.userUpdated:
            await _handleUserUpdate(session);
            break;
          case AuthChangeEvent.passwordRecovery:
            print('🔑 Password recovery event detected');
            break;
          default:
            print('🔄 Auth event: $event');
        }
      },
      onError: (error) {
        print('❌ Auth state listener error: $error');
        _handleAuthError(error);
      },
    );
  }

  /// Thiết lập error handling tổng quát
  void _setupErrorHandling() {
    // Có thể mở rộng để handle các loại error khác
    print('🛡️ Error handling đã được thiết lập');
  }

  /// Xử lý khi user đăng nhập
  Future<void> _handleSignIn(Session? session) async {
    if (session?.user != null) {
      print('✅ User đã đăng nhập: ${session!.user.email}');
      
      // Lưu thông tin user vào SharedPreferences
      await _saveUserInfo(session.user);
      
      // Log session info
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
      print('📅 Session expires at: $expiresAt');
    }
  }

  /// Xử lý khi user đăng xuất
  Future<void> _handleSignOut() async {
    print('🚪 User đã đăng xuất');
    
    // Xóa thông tin user khỏi SharedPreferences (trừ remember login)
    await _clearUserInfo();
  }

  /// Xử lý khi token được refresh
  Future<void> _handleTokenRefresh(Session? session) async {
    if (session != null) {
      print('🔄 Token đã được refresh');
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
      print('📅 New session expires at: $expiresAt');
    }
  }

  /// Xử lý khi user info được update
  Future<void> _handleUserUpdate(Session? session) async {
    if (session?.user != null) {
      print('👤 User info đã được cập nhật');
      await _saveUserInfo(session!.user);
    }
  }

  /// Xử lý lỗi auth
  void _handleAuthError(dynamic error) {
    print('🚨 Auth error detected: $error');
    
    // Xử lý các loại lỗi cụ thể
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('jwt') || 
        errorString.contains('token') || 
        errorString.contains('expired')) {
      print('🔧 JWT/Token error - cần xử lý refresh hoặc re-login');
      // Có thể trigger re-authentication flow
    } else if (errorString.contains('network') || 
               errorString.contains('connection')) {
      print('🌐 Network error - kiểm tra kết nối internet');
    } else {
      print('❓ Unknown auth error type');
    }
  }

  /// Lưu thông tin user vào SharedPreferences
  Future<void> _saveUserInfo(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString('user_id', user.id);
      await prefs.setString('user_email', user.email ?? '');
      
      // Lưu display name từ user metadata hoặc email
      final displayName = user.userMetadata?['full_name'] ?? 
                         user.userMetadata?['name'] ?? 
                         user.email?.split('@').first ?? 
                         'User';
      await prefs.setString('display_name', displayName);
      
      // Lưu photo URL nếu có
      final photoUrl = user.userMetadata?['avatar_url'] ?? 
                      user.userMetadata?['picture'] ?? '';
      if (photoUrl.isNotEmpty) {
        await prefs.setString('photo_url', photoUrl);
      }
      
      print('💾 Đã lưu thông tin user: $displayName ($user.email)');
    } catch (e) {
      print('❌ Lỗi lưu thông tin user: $e');
    }
  }

  /// Xóa thông tin user khỏi SharedPreferences
  Future<void> _clearUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Xóa thông tin user nhưng giữ lại remember login setting
      await prefs.remove('user_id');
      
      // Chỉ xóa email nếu user không chọn remember login
      final rememberLogin = prefs.getBool('rememberLogin') ?? false;
      if (!rememberLogin) {
        await prefs.remove('user_email');
        await prefs.remove('display_name');
        await prefs.remove('photo_url');
      }
      
      print('🗑️ Đã xóa thông tin user session');
    } catch (e) {
      print('❌ Lỗi xóa thông tin user: $e');
    }
  }

  /// Lấy thông tin user hiện tại
  User? get currentUser => _supabase.auth.currentUser;

  /// Lấy session hiện tại
  Session? get currentSession => _supabase.auth.currentSession;

  /// Kiểm tra trạng thái đăng nhập
  bool get isSignedIn => currentUser != null;

  /// Log request (có thể mở rộng để log tất cả requests)
  void logRequest(String method, String endpoint, {Map<String, dynamic>? data}) {
    print('📤 $method $endpoint');
    if (data != null && data.isNotEmpty) {
      print('   Data: ${data.toString().substring(0, data.toString().length > 100 ? 100 : data.toString().length)}...');
    }
  }

  /// Log response (có thể mở rộng để log tất cả responses)
  void logResponse(String endpoint, int? statusCode, {dynamic data}) {
    print('📥 Response from $endpoint: $statusCode');
    if (data != null) {
      final dataString = data.toString();
      print('   Data: ${dataString.substring(0, dataString.length > 100 ? 100 : dataString.length)}...');
    }
  }

  /// Log error
  void logError(String operation, dynamic error) {
    print('🚨 Error in $operation: $error');
  }

  /// Cleanup khi app đóng
  Future<void> dispose() async {
    await _authSubscription?.cancel();
    _authSubscription = null;
    _isInitialized = false;
    print('🧹 SupabaseInterceptor đã được cleanup');
  }

  /// Kiểm tra health của Supabase connection
  Future<Map<String, dynamic>> checkHealth() async {
    try {
      final user = currentUser;
      final session = currentSession;
      
      return {
        'isConnected': true,
        'isSignedIn': user != null,
        'userId': user?.id,
        'userEmail': user?.email,
        'sessionValid': session != null && 
                       DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)
                           .isAfter(DateTime.now()),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'isConnected': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
