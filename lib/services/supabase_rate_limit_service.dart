import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseRateLimitService {
  // Use Supabase client for database operations
  static SupabaseClient get _supabase => Supabase.instance.client;
  
  /// Tạo bảng tracking OTP requests trong Supabase (chạy SQL này trong Supabase Dashboard)
  /// 
  /// CREATE TABLE IF NOT EXISTS otp_rate_limit (
  ///   id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ///   email TEXT NOT NULL,
  ///   event_type TEXT NOT NULL DEFAULT 'password_reset',
  ///   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ///   ip_address INET,
  ///   user_agent TEXT
  /// );
  /// 
  /// CREATE INDEX idx_otp_rate_limit_email_created ON otp_rate_limit(email, created_at);
  /// 
  /// -- Enable RLS
  /// ALTER TABLE otp_rate_limit ENABLE ROW LEVEL SECURITY;
  /// 
  /// -- Policy cho phép insert và select
  /// CREATE POLICY "Anyone can insert OTP tracking" ON otp_rate_limit FOR INSERT WITH CHECK (true);
  /// CREATE POLICY "Anyone can read own OTP tracking" ON otp_rate_limit FOR SELECT USING (true);
  
  /// Ghi nhận request OTP vào database
  static Future<Map<String, dynamic>> recordOtpRequest({
    required String email,
    String eventType = 'password_reset',
  }) async {
    try {
      print('📝 Recording OTP request for: $email');
      
      await _supabase
          .from('otp_rate_limit')
          .insert({
            'email': email,
            'event_type': eventType,
          });

      print('✅ OTP request recorded successfully');
      return {
        'success': true,
      };
    } catch (e) {
      print('❌ Error recording OTP request: $e');
      // Không trả về lỗi vì đây chỉ là tracking, không nên block user
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
  
  /// Lấy rate limit info từ database tracking
  static Future<Map<String, dynamic>> getOtpRateLimit({
    required String email,
    String eventType = 'password_reset',
    Duration? timeWindow,
  }) async {
    try {
      final now = DateTime.now();
      final timeWindowStart = timeWindow != null 
          ? now.subtract(timeWindow)
          : now.subtract(Duration(hours: 1)); // Default 1 hour window

      print('🔍 Checking OTP rate limit for: $email');
      print('⏰ Time window: ${timeWindowStart.toIso8601String()} to ${now.toIso8601String()}');

      // Query OTP requests trong time window
      final response = await _supabase
          .from('otp_rate_limit')
          .select('id, email, event_type, created_at')
          .eq('email', email)
          .eq('event_type', eventType)
          .gte('created_at', timeWindowStart.toUtc().toIso8601String())
          .order('created_at', ascending: false);

      print('📊 Found ${response.length} OTP requests in time window');
      
      return _analyzeOtpRequests(response, email);
      
    } catch (e) {
      print('❌ Error querying OTP rate limit: $e');
      // Trả về fallback để không block user khi có lỗi
      return {
        'success': false,
        'error': e.toString(),
        'canSend': true, // Fallback to allow sending
        'remainingAttempts': 3,
        'cooldownSeconds': 0,
      };
    }
  }

  /// Phân tích OTP requests để xác định rate limit status
  static Map<String, dynamic> _analyzeOtpRequests(List<dynamic> requests, String email) {
    try {
      final now = DateTime.now();
      const maxAttempts = 3;
      const cooldownMinutes = 60;

      print('🔍 Analyzing ${requests.length} OTP requests');

      if (requests.isEmpty) {
        return {
          'success': true,
          'canSend': true,
          'remainingAttempts': maxAttempts,
          'cooldownSeconds': 0,
          'lastAttemptTime': null,
          'totalAttempts': 0,
        };
      }

      // Requests đã được sort theo created_at desc
      final latestRequest = requests.first;
      final latestAttemptTime = DateTime.parse(latestRequest['created_at']);
      final timeSinceLastAttempt = now.difference(latestAttemptTime);

      print('⏰ Latest attempt: $latestAttemptTime');
      print('⌛ Time since last attempt: ${timeSinceLastAttempt.inMinutes} minutes');
      print('📊 Total attempts in window: ${requests.length}');

      // Check if user has exceeded max attempts
      if (requests.length >= maxAttempts) {
        final remainingCooldown = cooldownMinutes - timeSinceLastAttempt.inMinutes;
        
        if (remainingCooldown > 0) {
          return {
            'success': true,
            'canSend': false,
            'remainingAttempts': 0,
            'cooldownSeconds': remainingCooldown * 60,
            'lastAttemptTime': latestAttemptTime.toIso8601String(),
            'totalAttempts': requests.length,
            'message': 'Bạn đã gửi quá nhiều yêu cầu OTP. Vui lòng đợi $remainingCooldown phút.',
          };
        } else {
          // Cooldown period has passed, reset
          return {
            'success': true,
            'canSend': true,
            'remainingAttempts': maxAttempts,
            'cooldownSeconds': 0,
            'lastAttemptTime': latestAttemptTime.toIso8601String(),
            'totalAttempts': 0, // Reset count
          };
        }
      }

      // User can still send, but show remaining attempts
      final remainingAttempts = maxAttempts - requests.length;
      
      return {
        'success': true,
        'canSend': true,
        'remainingAttempts': remainingAttempts,
        'cooldownSeconds': 0,
        'lastAttemptTime': latestAttemptTime.toIso8601String(),
        'totalAttempts': requests.length,
      };
      
    } catch (e) {
      print('❌ Error analyzing OTP requests: $e');
      return {
        'success': false,
        'error': e.toString(),
        'canSend': true, // Fallback
        'remainingAttempts': 3,
        'cooldownSeconds': 0,
      };
    }
  }

  /// Lấy rate limit info cho password reset OTP
  static Future<Map<String, dynamic>> getPasswordResetRateLimit(String email) async {
    return await getOtpRateLimit(
      email: email,
      eventType: 'password_reset',
      timeWindow: Duration(minutes: 60),
    );
  }

  /// Lấy rate limit info cho signup OTP
  static Future<Map<String, dynamic>> getSignupRateLimit(String email) async {
    return await getOtpRateLimit(
      email: email,
      eventType: 'signup',
      timeWindow: Duration(minutes: 60),
    );
  }

  /// Test connection đến Supabase
  static Future<bool> testConnection() async {
    try {
      // Test bằng cách query một record đơn giản
      await _supabase
          .from('otp_rate_limit')
          .select('id')
          .limit(1);
      
      print('🔌 Supabase connection test: OK');
      return true;
    } catch (e) {
      print('❌ Supabase connection test failed: $e');
      return false;
    }
  }

  /// Cleanup old records (chạy định kỳ để dọn dẹp database)
  static Future<void> cleanupOldRecords({int daysToKeep = 7}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      await _supabase
          .from('otp_rate_limit')
          .delete()
          .lt('created_at', cutoffDate.toUtc().toIso8601String());
          
      print('🧹 Cleaned up OTP records older than $daysToKeep days');
    } catch (e) {
      print('❌ Error cleaning up old records: $e');
    }
  }
}
