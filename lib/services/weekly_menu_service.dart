import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/weekly_menu.dart';
import '../services/auth_service.dart';


/// Service quản lý thực đơn hàng tuần
class WeeklyMenuService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthService _authService = AuthService();

  /// Lấy thực đơn tuần hiện tại
  Future<Map<String, dynamic>> getCurrentWeekMenu() async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để xem thực đơn',
        };
      }

      final userId = userData['userData']['uid'];
      final now = DateTime.now();
      final weekStartDate = WeekUtils.getWeekStartDate(now);

      return await getWeekMenu(userId, weekStartDate);
    } catch (e) {
      print('Lỗi khi lấy thực đơn tuần hiện tại: $e');

      // Nếu table chưa tồn tại, trả về empty menu thay vì lỗi
      if (e.toString().contains('does not exist') || e.toString().contains('42P01')) {
        final userData = await _authService.getCurrentUserData();
        if (userData['success'] == true) {
          final userId = userData['userData']['uid'];
          final weekStartDate = WeekUtils.getWeekStartDate(DateTime.now());
          final weekMenu = WeeklyMenu(
            userId: userId,
            weekStartDate: weekStartDate,
            items: [],
          );
          return {
            'success': true,
            'menu': weekMenu,
            'message': 'Tính năng thực đơn tuần đang được phát triển',
          };
        }
      }

      return {
        'success': false,
        'message': 'Không thể lấy thực đơn: $e',
      };
    }
  }

  /// Lấy thực đơn cho tuần cụ thể
  Future<Map<String, dynamic>> getWeekMenu(String userId, DateTime weekStartDate) async {
    try {
      final weekEndDate = weekStartDate.add(const Duration(days: 6));
      
      // Lấy tất cả items trong tuần
      final response = await _supabase
          .from('weekly_menu_items')
          .select()
          .eq('user_id', userId)
          .gte('date', weekStartDate.toIso8601String().split('T')[0])
          .lte('date', weekEndDate.toIso8601String().split('T')[0])
          .order('date')
          .order('meal_type');

      final items = (response as List)
          .map((item) => WeeklyMenuItem.fromJson(item))
          .toList();

      final weekMenu = WeeklyMenu(
        userId: userId,
        weekStartDate: weekStartDate,
        items: items,
      );

      return {
        'success': true,
        'menu': weekMenu,
      };
    } catch (e) {
      print('Lỗi khi lấy thực đơn tuần: $e');

      // Nếu table chưa tồn tại, trả về empty menu thay vì lỗi
      if (e.toString().contains('does not exist') || e.toString().contains('42P01')) {
        final weekMenu = WeeklyMenu(
          userId: userId,
          weekStartDate: weekStartDate,
          items: [],
        );
        return {
          'success': true,
          'menu': weekMenu,
          'message': 'Tính năng thực đơn tuần đang được phát triển',
        };
      }

      return {
        'success': false,
        'message': 'Không thể lấy thực đơn: $e',
      };
    }
  }

  /// Thêm món ăn vào thực đơn tuần
  Future<Map<String, dynamic>> addItemToWeekMenu(WeeklyMenuItem item) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để thêm món ăn',
        };
      }

      // Kiểm tra xem đã có món ăn cho bữa này chưa
      final existingResponse = await _supabase
          .from('weekly_menu_items')
          .select()
          .eq('user_id', item.userId)
          .eq('date', item.date.toIso8601String().split('T')[0])
          .eq('meal_type', item.mealType.value);

      // Nếu đã có, xóa món cũ
      if (existingResponse.isNotEmpty) {
        await _supabase
            .from('weekly_menu_items')
            .delete()
            .eq('user_id', item.userId)
            .eq('date', item.date.toIso8601String().split('T')[0])
            .eq('meal_type', item.mealType.value);
      }

      // Thêm món mới
      await _supabase
          .from('weekly_menu_items')
          .insert(item.toJson());

      return {
        'success': true,
        'message': 'Đã thêm món ăn vào thực đơn',
      };
    } catch (e) {
      print('Lỗi khi thêm món ăn vào thực đơn: $e');
      return {
        'success': false,
        'message': 'Không thể thêm món ăn: $e',
      };
    }
  }

  /// Xóa món ăn khỏi thực đơn
  Future<Map<String, dynamic>> removeItemFromWeekMenu(String itemId) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để xóa món ăn',
        };
      }

      await _supabase
          .from('weekly_menu_items')
          .delete()
          .eq('id', itemId);

      return {
        'success': true,
        'message': 'Đã xóa món ăn khỏi thực đơn',
      };
    } catch (e) {
      print('Lỗi khi xóa món ăn: $e');
      return {
        'success': false,
        'message': 'Không thể xóa món ăn: $e',
      };
    }
  }

  /// Cập nhật trạng thái hoàn thành món ăn
  Future<Map<String, dynamic>> updateItemCompletion(String itemId, bool isCompleted) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để cập nhật trạng thái',
        };
      }

      await _supabase
          .from('weekly_menu_items')
          .update({
            'is_completed': isCompleted,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', itemId);

      return {
        'success': true,
        'message': isCompleted ? 'Đã đánh dấu hoàn thành' : 'Đã bỏ đánh dấu hoàn thành',
      };
    } catch (e) {
      print('Lỗi khi cập nhật trạng thái: $e');
      return {
        'success': false,
        'message': 'Không thể cập nhật trạng thái: $e',
      };
    }
  }

  /// Cập nhật ghi chú cho món ăn
  Future<Map<String, dynamic>> updateItemNotes(String itemId, String notes) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để cập nhật ghi chú',
        };
      }

      await _supabase
          .from('weekly_menu_items')
          .update({
            'notes': notes,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', itemId);

      return {
        'success': true,
        'message': 'Đã cập nhật ghi chú',
      };
    } catch (e) {
      print('Lỗi khi cập nhật ghi chú: $e');
      return {
        'success': false,
        'message': 'Không thể cập nhật ghi chú: $e',
      };
    }
  }

  /// Lấy lịch sử thực đơn
  Future<Map<String, dynamic>> getMenuHistory(int limit) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để xem lịch sử',
        };
      }

      final userId = userData['userData']['uid'];
      
      // Lấy các tuần gần đây
      final response = await _supabase
          .from('weekly_menu_items')
          .select()
          .eq('user_id', userId)
          .order('date', ascending: false)
          .limit(limit * 21); // Tối đa 21 items mỗi tuần (7 ngày x 3 bữa)

      final items = (response as List)
          .map((item) => WeeklyMenuItem.fromJson(item))
          .toList();

      // Group theo tuần
      final Map<String, List<WeeklyMenuItem>> weekGroups = {};
      for (final item in items) {
        final weekStart = WeekUtils.getWeekStartDate(item.date);
        final weekKey = weekStart.toIso8601String().split('T')[0];
        
        if (!weekGroups.containsKey(weekKey)) {
          weekGroups[weekKey] = [];
        }
        weekGroups[weekKey]!.add(item);
      }

      // Tạo WeeklyMenu objects
      final weekMenus = weekGroups.entries.map((entry) {
        final weekStart = DateTime.parse(entry.key);
        return WeeklyMenu(
          userId: userId,
          weekStartDate: weekStart,
          items: entry.value,
        );
      }).toList();

      // Sắp xếp theo thời gian giảm dần
      weekMenus.sort((a, b) => b.weekStartDate.compareTo(a.weekStartDate));

      return {
        'success': true,
        'menus': weekMenus.take(limit).toList(),
      };
    } catch (e) {
      print('Lỗi khi lấy lịch sử thực đơn: $e');
      return {
        'success': false,
        'message': 'Không thể lấy lịch sử: $e',
      };
    }
  }

  /// Sao chép thực đơn từ tuần khác
  Future<Map<String, dynamic>> copyMenuFromWeek(DateTime sourceWeekStart, DateTime targetWeekStart) async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để sao chép thực đơn',
        };
      }

      final userId = userData['userData']['uid'];

      // Lấy thực đơn nguồn
      final sourceResult = await getWeekMenu(userId, sourceWeekStart);
      if (!sourceResult['success']) {
        return sourceResult;
      }

      final sourceMenu = sourceResult['menu'] as WeeklyMenu;
      
      // Xóa thực đơn hiện tại của tuần đích (nếu có)
      final targetWeekEnd = targetWeekStart.add(const Duration(days: 6));
      await _supabase
          .from('weekly_menu_items')
          .delete()
          .eq('user_id', userId)
          .gte('date', targetWeekStart.toIso8601String().split('T')[0])
          .lte('date', targetWeekEnd.toIso8601String().split('T')[0]);

      // Sao chép items sang tuần mới
      final daysDiff = targetWeekStart.difference(sourceWeekStart).inDays;
      
      for (final item in sourceMenu.items) {
        final newDate = item.date.add(Duration(days: daysDiff));
        final newItem = WeeklyMenuItem(
          userId: userId,
          date: newDate,
          mealType: item.mealType,
          video: item.video,
          notes: item.notes,
          isCompleted: false, // Reset trạng thái hoàn thành
        );

        await _supabase
            .from('weekly_menu_items')
            .insert(newItem.toJson());
      }

      return {
        'success': true,
        'message': 'Đã sao chép thực đơn thành công',
      };
    } catch (e) {
      print('Lỗi khi sao chép thực đơn: $e');
      return {
        'success': false,
        'message': 'Không thể sao chép thực đơn: $e',
      };
    }
  }

  /// Lấy thống kê thực đơn
  Future<Map<String, dynamic>> getMenuStatistics() async {
    try {
      final userData = await _authService.getCurrentUserData();
      if (!userData['success']) {
        return {
          'success': false,
          'message': 'Vui lòng đăng nhập để xem thống kê',
        };
      }

      final userId = userData['userData']['uid'];
      final now = DateTime.now();
      final weekStart = WeekUtils.getWeekStartDate(now);
      
      // Lấy thực đơn tuần hiện tại
      final currentWeekResult = await getWeekMenu(userId, weekStart);
      if (!currentWeekResult['success']) {
        return currentWeekResult;
      }

      final currentMenu = currentWeekResult['menu'] as WeeklyMenu;
      final stats = currentMenu.getStatistics();

      // Thêm thống kê bổ sung
      final totalItemsResponse = await _supabase
          .from('weekly_menu_items')
          .select('id')
          .eq('user_id', userId);

      final completedItemsResponse = await _supabase
          .from('weekly_menu_items')
          .select('id')
          .eq('user_id', userId)
          .eq('is_completed', true);

      stats['total_items_all_time'] = totalItemsResponse.length;
      stats['completed_items_all_time'] = completedItemsResponse.length;

      return {
        'success': true,
        'statistics': stats,
      };
    } catch (e) {
      print('Lỗi khi lấy thống kê: $e');
      return {
        'success': false,
        'message': 'Không thể lấy thống kê: $e',
      };
    }
  }
}
