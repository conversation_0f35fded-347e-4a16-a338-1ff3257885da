import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user_profile.dart';
import '../config/api_config.dart';
import 'gemini_ai_service.dart';
import 'saved_video_service.dart';

/// Model cho comment YouTube
class YouTubeComment {
  final String id;
  final String authorDisplayName;
  final String authorProfileImageUrl;
  final String textDisplay;
  final int likeCount;
  final DateTime publishedAt;

  YouTubeComment({
    required this.id,
    required this.authorDisplayName,
    required this.authorProfileImageUrl,
    required this.textDisplay,
    required this.likeCount,
    required this.publishedAt,
  });

  factory YouTubeComment.fromJson(Map<String, dynamic> json) {
    final snippet = json['snippet']['topLevelComment']['snippet'];
    return YouTubeComment(
      id: json['id'] ?? '',
      authorDisplayName: snippet['authorDisplayName'] ?? 'Ẩn danh',
      authorProfileImageUrl: snippet['authorProfileImageUrl'] ?? '',
      textDisplay: snippet['textDisplay'] ?? '',
      likeCount: snippet['likeCount'] ?? 0,
      publishedAt: DateTime.tryParse(snippet['publishedAt'] ?? '') ?? DateTime.now(),
    );
  }
}

/// Model cho video YouTube
class YouTubeVideo {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String channelTitle;
  final Duration duration;
  final int viewCount;
  final DateTime publishedAt;

  YouTubeVideo({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.channelTitle,
    required this.duration,
    required this.viewCount,
    required this.publishedAt,
  });

  factory YouTubeVideo.fromJson(Map<String, dynamic> json) {
    // Handle different API response formats
    String videoId;
    if (json['id'] is Map) {
      // Search API format: {"id": {"videoId": "..."}}
      videoId = json['id']['videoId'] ?? '';
    } else {
      // Videos API format: {"id": "..."}
      videoId = json['id'] ?? '';
    }

    return YouTubeVideo(
      id: videoId,
      title: json['snippet']['title'] ?? '',
      description: json['snippet']['description'] ?? '',
      thumbnailUrl: json['snippet']['thumbnails']?['medium']?['url'] ??
                   json['snippet']['thumbnails']?['default']?['url'] ?? '',
      channelTitle: json['snippet']['channelTitle'] ?? '',
      duration: _parseDuration(json['contentDetails']?['duration'] ?? 'PT0S'),
      viewCount: int.tryParse(json['statistics']?['viewCount'] ?? '0') ?? 0,
      publishedAt: DateTime.tryParse(json['snippet']['publishedAt'] ?? '') ?? DateTime.now(),
    );
  }

  static Duration _parseDuration(String duration) {
    // Parse ISO 8601 duration format (PT4M13S)
    final regex = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?');
    final match = regex.firstMatch(duration);
    
    if (match == null) return Duration.zero;
    
    final hours = int.tryParse(match.group(1) ?? '0') ?? 0;
    final minutes = int.tryParse(match.group(2) ?? '0') ?? 0;
    final seconds = int.tryParse(match.group(3) ?? '0') ?? 0;
    
    return Duration(hours: hours, minutes: minutes, seconds: seconds);
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedViewCount {
    if (viewCount >= 1000000) {
      return '${(viewCount / 1000000).toStringAsFixed(1)}M lượt xem';
    } else if (viewCount >= 1000) {
      return '${(viewCount / 1000).toStringAsFixed(1)}K lượt xem';
    } else {
      return '$viewCount lượt xem';
    }
  }
}

/// Service để tích hợp với YouTube Data API v3
class YouTubeService {
  // HTTP client với timeout
  final http.Client _httpClient = http.Client();

  // Gemini AI service cho intelligent filtering
  final GeminiAIService _geminiService = GeminiAIService();

  // Saved video service để filter out videos đã lưu
  final SavedVideoService _savedVideoService = SavedVideoService();
  
  /// Search video món ăn dựa trên query
  Future<Map<String, dynamic>> searchVideos(dynamic query) async {
    print('🎬 YouTubeService.searchVideos được gọi với query: $query');
    print('🎬 Query type: ${query.runtimeType}');

    // Log API configuration
    ApiConfig.logConfigInfo();

    try {
      String searchQuery;
      if (query is String) {
        searchQuery = query;
        print('🎬 Using String query directly: "$searchQuery"');
      } else if (query is MealSuggestionQuery) {
        searchQuery = query.toYouTubeSearchQuery();
        print('🎬 Converted MealSuggestionQuery to: "$searchQuery"');
      } else {
        print('❌ Unsupported query type: ${query.runtimeType}');
        return {
          'success': false,
          'message': 'Loại query không được hỗ trợ',
          'videos': <YouTubeVideo>[],
        };
      }

      // Gọi YouTube Search API
      print('🎬 Đang gọi _searchVideos...');
      final searchResults = await _searchVideos(searchQuery);
      print('🎬 _searchVideos trả về: ${searchResults['success']}');

      if (!searchResults['success']) {
        print('❌ _searchVideos thất bại: ${searchResults['message']}');
        return searchResults;
      }

      final videos = searchResults['videos'] as List<YouTubeVideo>;
      print('🎬 Số video từ _searchVideos: ${videos.length}');

      // Lọc và sắp xếp kết quả với AI
      print('🎬 Đang lọc và sắp xếp videos với AI...');
      final filteredVideos = await _filterAndSortVideosWithAI(videos, query);
      print('🎬 Số video sau khi lọc: ${filteredVideos.length}');

      return {
        'success': true,
        'videos': filteredVideos,
        'query': searchQuery,
        'nextPageToken': searchResults['nextPageToken'], // Trả về nextPageToken
      };
    } catch (e) {
      print('❌ Exception trong YouTubeService.searchVideos: $e');
      return {
        'success': false,
        'message': 'Không thể tìm kiếm video: $e',
        'videos': <YouTubeVideo>[],
      };
    }
  }

  /// Search video với query string trực tiếp (cho refresh)
  Future<Map<String, dynamic>> searchVideosWithQuery(String searchQuery) async {
    print('🔄 YouTubeService.searchVideosWithQuery được gọi với query: "$searchQuery"');

    try {
      // Gọi YouTube Search API
      print('🔄 Đang gọi _searchVideos...');
      final searchResults = await _searchVideos(searchQuery);
      print('🔄 _searchVideos trả về: ${searchResults['success']}');

      if (!searchResults['success']) {
        print('❌ _searchVideos thất bại: ${searchResults['message']}');
        return searchResults;
      }

      final videos = searchResults['videos'] as List<YouTubeVideo>;
      print('🔄 Số video từ _searchVideos: ${videos.length}');

      // Không cần AI filtering cho refresh, chỉ lọc thời lượng
      final durationFilteredVideos = videos.where((video) {
        final minutes = video.duration.inMinutes;
        return minutes >= 5 && minutes <= 30;
      }).toList();

      print('🔄 Số video sau khi lọc thời lượng: ${durationFilteredVideos.length}');

      return {
        'success': true,
        'videos': durationFilteredVideos,
        'query': searchQuery,
      };
    } catch (e) {
      print('❌ Exception trong YouTubeService.searchVideosWithQuery: $e');
      return {
        'success': false,
        'message': 'Không thể tìm kiếm video: $e',
        'videos': <YouTubeVideo>[],
      };
    }
  }

  /// Search video với pagination (cho refresh)
  Future<Map<String, dynamic>> searchVideosWithPagination(MealSuggestionQuery query, String pageToken) async {
    print('📄 YouTubeService.searchVideosWithPagination được gọi');
    print('📄 Query: ${query.toYouTubeSearchQuery()}');
    print('📄 PageToken: $pageToken');

    try {
      final searchQuery = query.toYouTubeSearchQuery();

      // Gọi YouTube Search API với pageToken
      print('📄 Đang gọi _searchVideosWithPageToken...');
      final searchResults = await _searchVideosWithPageToken(searchQuery, pageToken);
      print('📄 _searchVideosWithPageToken trả về: ${searchResults['success']}');

      if (!searchResults['success']) {
        print('❌ _searchVideosWithPageToken thất bại: ${searchResults['message']}');
        return searchResults;
      }

      final videos = searchResults['videos'] as List<YouTubeVideo>;
      print('📄 Số video từ _searchVideosWithPageToken: ${videos.length}');

      // Lọc và sắp xếp kết quả với AI (giữ chất lượng)
      print('📄 Đang lọc và sắp xếp videos với AI...');
      final filteredVideos = await _filterAndSortVideosWithAI(videos, query);
      print('📄 Số video sau khi lọc: ${filteredVideos.length}');

      return {
        'success': true,
        'videos': filteredVideos,
        'query': searchQuery,
        'nextPageToken': searchResults['nextPageToken'], // Truyền nextPageToken
      };
    } catch (e) {
      print('❌ Exception trong YouTubeService.searchVideosWithPagination: $e');
      return {
        'success': false,
        'message': 'Không thể tải trang tiếp theo: $e',
        'videos': <YouTubeVideo>[],
      };
    }
  }

  /// Gọi YouTube Data API v3 để tìm kiếm videos
  Future<Map<String, dynamic>> _searchVideos(String query) async {
    print('🔍 _searchVideos được gọi với query: "$query"');

    try {
      // Kiểm tra API key hoặc force mock data
      if (!ApiConfig.isYouTubeApiConfigured || ApiConfig.forceMockData) {
        if (ApiConfig.enableApiDebugLogs) {
          print('⚠️ Chưa cấu hình YouTube API key hoặc force mock data, sử dụng mock data');
        }
        return _getMockVideos(query);
      }
      
      // Tạo search query với keywords tiếng Việt thuần túy
      final searchQuery = '$query nấu ăn món ngon';
      if (ApiConfig.enableApiDebugLogs) {
        print('🔍 Search query: "$searchQuery"');
      }

      // Sử dụng ApiConfig để tạo URL
      final url = ApiConfigExtension.createYouTubeSearchUrl(query: searchQuery);

      if (ApiConfig.enableApiDebugLogs) {
        print('🔗 YouTube API URL: ${url.toString().replaceAll(ApiConfig.youtubeApiKey, 'API_KEY_HIDDEN')}');
      }

      final response = await _httpClient.get(url).timeout(
        ApiConfig.youtubeApiTimeout,
        onTimeout: () {
          throw TimeoutException('YouTube API timeout', ApiConfig.youtubeApiTimeout);
        },
      );

      print('📡 YouTube API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('📊 Response data keys: ${data.keys.toList()}');

        if (data['items'] == null || (data['items'] as List).isEmpty) {
          print('⚠️ Không tìm thấy video nào từ YouTube API, fallback to mock data');
          return await _getMockVideos(query);
        }

        final items = data['items'] as List;
        print('📹 Tìm được ${items.length} videos từ search API');

        // Lấy thêm thông tin chi tiết về video
        final videoIds = items.map((item) => item['id']['videoId']).join(',');
        print('🔍 Video IDs: $videoIds');

        final detailsResponse = await _getVideoDetails(videoIds);

        if (detailsResponse['success']) {
          final videos = detailsResponse['videos'] as List<YouTubeVideo>;
          print('✅ Lấy được chi tiết ${videos.length} videos');

          return {
            'success': true,
            'videos': videos,
            'nextPageToken': data['nextPageToken'], // Trả về nextPageToken
          };
        } else {
          print('❌ Lỗi khi lấy chi tiết video: ${detailsResponse['message']}');
        }
      } else {
        print('❌ YouTube API error: ${response.statusCode}');
        print('❌ Response body: ${response.body}');
      }
      
      // Nếu không thành công, fallback to mock data
      print('⚠️ YouTube API không thành công, sử dụng mock data');
      return await _getMockVideos(query);

    } catch (e) {
      print('❌ Exception trong _searchVideos: $e');
      // Fallback to mock data khi có lỗi
      return await _getMockVideos(query);
    }
  }

  /// Gọi YouTube Data API v3 với pageToken để phân trang
  Future<Map<String, dynamic>> _searchVideosWithPageToken(String query, String pageToken) async {
    print('📄 _searchVideosWithPageToken được gọi với query: "$query", pageToken: "$pageToken"');

    try {
      // Kiểm tra API key hoặc force mock data
      if (!ApiConfig.isYouTubeApiConfigured || ApiConfig.forceMockData) {
        if (ApiConfig.enableApiDebugLogs) {
          print('⚠️ Chưa cấu hình YouTube API key hoặc force mock data, sử dụng mock data');
        }
        return _getMockVideos(query);
      }

      // Tạo search query với keywords tiếng Việt thuần túy
      final searchQuery = '$query nấu ăn món ngon';
      if (ApiConfig.enableApiDebugLogs) {
        print('📄 Search query: "$searchQuery"');
      }

      // Sử dụng ApiConfig để tạo URL với pageToken
      final url = ApiConfigExtension.createYouTubeSearchUrlWithPageToken(
        query: searchQuery,
        pageToken: pageToken
      );

      if (ApiConfig.enableApiDebugLogs) {
        print('🔗 YouTube API URL với pageToken: ${url.toString().replaceAll(ApiConfig.youtubeApiKey, 'API_KEY_HIDDEN')}');
      }

      final response = await _httpClient.get(url).timeout(
        ApiConfig.youtubeApiTimeout,
        onTimeout: () {
          throw TimeoutException('YouTube API timeout', ApiConfig.youtubeApiTimeout);
        },
      );

      print('📡 YouTube API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('📊 Response data keys: ${data.keys.toList()}');

        if (data['items'] == null || (data['items'] as List).isEmpty) {
          print('⚠️ Không tìm thấy video nào từ YouTube API với pageToken');
          return {
            'success': false,
            'message': 'Không có video nào trong trang này',
            'videos': <YouTubeVideo>[],
          };
        }

        final items = data['items'] as List;
        print('📹 Tìm được ${items.length} videos từ search API với pageToken');

        // Lấy thêm thông tin chi tiết về video
        final videoIds = items.map((item) => item['id']['videoId']).join(',');
        print('📄 Video IDs: $videoIds');

        final detailsResponse = await _getVideoDetails(videoIds);

        if (detailsResponse['success']) {
          final videos = detailsResponse['videos'] as List<YouTubeVideo>;
          print('✅ Lấy được chi tiết ${videos.length} videos');

          return {
            'success': true,
            'videos': videos,
            'nextPageToken': data['nextPageToken'], // Trả về nextPageToken
          };
        } else {
          print('❌ Lỗi khi lấy chi tiết video: ${detailsResponse['message']}');
        }
      } else {
        print('❌ YouTube API error: ${response.statusCode}');
        print('❌ Response body: ${response.body}');
      }

      // Nếu không thành công
      print('⚠️ YouTube API không thành công với pageToken');
      return {
        'success': false,
        'message': 'Không thể tải trang tiếp theo',
        'videos': <YouTubeVideo>[],
      };

    } catch (e) {
      print('❌ Exception trong _searchVideosWithPageToken: $e');
      return {
        'success': false,
        'message': 'Lỗi khi tải trang tiếp theo: $e',
        'videos': <YouTubeVideo>[],
      };
    }
  }

  /// Lấy thông tin chi tiết về video
  Future<Map<String, dynamic>> _getVideoDetails(String videoIds) async {
    try {
      // Sử dụng ApiConfig để tạo URL
      final url = ApiConfigExtension.createYouTubeVideosUrl(videoIds: videoIds);

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List;

        print('🔍 _getVideoDetails Items count: ${items.length}');

        List<YouTubeVideo> videos = [];
        for (int i = 0; i < items.length; i++) {
          try {
            print('🔍 Processing video $i: ${items[i]['id']}');
            final video = YouTubeVideo.fromJson(items[i]);
            videos.add(video);
            print('✅ Video $i processed successfully');
          } catch (e) {
            print('❌ Error processing video $i: $e');
            print('❌ Video data keys: ${items[i].keys.toList()}');
          }
        }

        return {
          'success': true,
          'videos': videos,
        };
      }
      
      return {
        'success': false,
        'message': 'Không thể lấy thông tin video',
        'videos': <YouTubeVideo>[],
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Lỗi khi lấy thông tin video: $e',
        'videos': <YouTubeVideo>[],
      };
    }
  }

  /// Lọc và sắp xếp video theo tiêu chí
  List<YouTubeVideo> _filterAndSortVideos(List<YouTubeVideo> videos, MealSuggestionQuery query) {
    // Lọc video có thời lượng hợp lý (5-30 phút)
    final filteredVideos = videos.where((video) {
      final minutes = video.duration.inMinutes;
      return minutes >= 5 && minutes <= 30;
    }).toList();

    // Sắp xếp theo độ phù hợp với relevance scoring
    filteredVideos.sort((a, b) {
      // 1. Tính điểm relevance cho từng video
      final relevanceA = _calculateRelevanceScore(a, query);
      final relevanceB = _calculateRelevanceScore(b, query);

      // Ưu tiên relevance score cao hơn
      final relevanceComparison = relevanceB.compareTo(relevanceA);
      if (relevanceComparison != 0) return relevanceComparison;

      // 2. Nếu relevance bằng nhau, ưu tiên view count
      final viewScore = b.viewCount.compareTo(a.viewCount);
      if (viewScore != 0) return viewScore;

      // 3. Cuối cùng ưu tiên video mới hơn
      return b.publishedAt.compareTo(a.publishedAt);
    });

    return filteredVideos.take(10).toList();
  }

  /// Lọc và sắp xếp video với AI (thay thế method cũ)
  Future<List<YouTubeVideo>> _filterAndSortVideosWithAI(List<YouTubeVideo> videos, dynamic query) async {
    try {
      // Chỉ xử lý MealSuggestionQuery với AI
      if (query is! MealSuggestionQuery) {
        print('🤖 Query không phải MealSuggestionQuery, sử dụng filtering cũ');
        return _filterAndSortVideos(videos, query);
      }

      // Lọc video có thời lượng hợp lý trước (5-30 phút)
      final durationFilteredVideos = videos.where((video) {
        final minutes = video.duration.inMinutes;
        return minutes >= 5 && minutes <= 30;
      }).toList();

      print('🤖 Videos sau khi lọc thời lượng: ${durationFilteredVideos.length}');

      if (durationFilteredVideos.isEmpty) {
        return [];
      }

      // Lọc bỏ videos đã lưu
      print('🤖 Đang lọc bỏ videos đã lưu...');
      final savedVideos = await _savedVideoService.getSavedVideos();
      final savedVideoIds = savedVideos.map((sv) => sv.video.id).toSet();

      final unsavedVideos = durationFilteredVideos.where((video) {
        return !savedVideoIds.contains(video.id);
      }).toList();

      print('🤖 Videos sau khi lọc bỏ đã lưu: ${unsavedVideos.length} (đã lưu: ${savedVideoIds.length})');

      if (unsavedVideos.isEmpty) {
        print('🤖 Tất cả videos đều đã được lưu, trả về danh sách rỗng');
        return [];
      }

      // Sử dụng AI để phân tích và scoring
      print('🤖 Bắt đầu phân tích AI cho ${unsavedVideos.length} videos...');
      final aiAnalyses = await _geminiService.analyzeVideosBatch(unsavedVideos, query);

      // Kết hợp video với AI analysis
      final videoWithScores = <Map<String, dynamic>>[];
      for (int i = 0; i < unsavedVideos.length; i++) {
        final video = unsavedVideos[i];
        final analysis = aiAnalyses.firstWhere(
          (a) => a.videoId == video.id,
          orElse: () => VideoAnalysisResult(
            videoId: video.id,
            relevanceScore: 50,
            confidence: 0.5,
            isRelevant: true,
          ),
        );

        videoWithScores.add({
          'video': video,
          'analysis': analysis,
          'finalScore': _calculateFinalScore(video, analysis, query),
        });
      }

      // Lọc video không phù hợp (AI đánh giá isRelevant = false)
      final relevantVideos = videoWithScores.where((item) {
        final analysis = item['analysis'] as VideoAnalysisResult;
        return analysis.isRelevant && analysis.relevanceScore >= 30; // Ngưỡng tối thiểu
      }).toList();

      print('🤖 Videos phù hợp sau AI filtering: ${relevantVideos.length}');

      // Sắp xếp theo điểm số cuối cùng
      relevantVideos.sort((a, b) {
        final scoreA = a['finalScore'] as double;
        final scoreB = b['finalScore'] as double;
        return scoreB.compareTo(scoreA); // Điểm cao hơn lên trước
      });

      // Debug top results
      for (int i = 0; i < relevantVideos.take(3).length; i++) {
        final item = relevantVideos[i];
        final video = item['video'] as YouTubeVideo;
        final analysis = item['analysis'] as VideoAnalysisResult;
        final score = item['finalScore'] as double;
        print('🏆 Top ${i + 1}: "${video.title}" - Score: $score (AI: ${analysis.relevanceScore}, Confidence: ${analysis.confidence})');
      }

      // Trả về top 10 videos
      return relevantVideos
          .take(10)
          .map((item) => item['video'] as YouTubeVideo)
          .toList();

    } catch (e) {
      print('❌ Lỗi trong AI filtering: $e');
      // Fallback về method cũ nếu AI có lỗi
      return _filterAndSortVideos(videos, query);
    }
  }

  /// Tính điểm cuối cùng kết hợp AI và traditional scoring
  double _calculateFinalScore(YouTubeVideo video, VideoAnalysisResult analysis, MealSuggestionQuery query) {
    // AI score (0-100) -> weight 70%
    final aiScore = analysis.relevanceScore * 0.7;

    // Traditional relevance score -> weight 20%
    final traditionalScore = _calculateRelevanceScore(video, query) * 0.2;

    // Confidence bonus -> weight 10%
    final confidenceBonus = analysis.confidence * 10;

    // View count bonus (normalized) -> small weight
    final viewBonus = (video.viewCount / 1000000).clamp(0, 5); // Max 5 points

    final finalScore = aiScore + traditionalScore + confidenceBonus + viewBonus;

    return finalScore;
  }

  /// Tính điểm relevance của video với query tìm kiếm
  double _calculateRelevanceScore(YouTubeVideo video, MealSuggestionQuery query) {
    double score = 0.0;

    // Lấy từ khóa chính từ query
    final mainKeywords = _extractMainKeywords(query);
    final title = video.title.toLowerCase();
    final description = video.description.toLowerCase();

    // Debug logs
    // print('🎯 Scoring video: "${video.title}"');
    // print('🎯 Main keywords: $mainKeywords');

    // Điểm cho title chứa từ khóa chính (trọng số khác nhau)
    for (final keyword in mainKeywords) {
      final keywordLower = keyword.toLowerCase();

      // Xác định trọng số dựa trên loại keyword
      double baseScore = 10.0; // Default
      double bonusScore = 5.0; // Default

      // Nguyên liệu chính có trọng số cao nhất
      if (query.preferredIngredients.any((ingredient) =>
          ingredient.displayName.toLowerCase() == keywordLower)) {
        baseScore = 20.0; // Tăng gấp đôi cho nguyên liệu chính
        bonusScore = 10.0; // Tăng bonus cho nguyên liệu chính
      }
      // Meal type có trọng số thấp hơn
      else if (['bữa sáng', 'bữa trưa', 'bữa tối', 'sáng', 'trưa', 'tối', 'điểm tâm'].contains(keywordLower)) {
        baseScore = 5.0; // Giảm trọng số cho meal type
        bonusScore = 2.0; // Giảm bonus cho meal type
      }

      if (title.contains(keywordLower)) {
        score += baseScore;
        // print('🎯   +$baseScore for "$keyword" in title');

        // Bonus nếu từ khóa ở đầu title (các pattern tiếng Việt)
        if (title.startsWith(keywordLower) ||
            title.startsWith('cách nấu $keywordLower') ||
            title.startsWith('món $keywordLower') ||
            title.startsWith('nấu $keywordLower') ||
            title.startsWith('làm $keywordLower') ||
            title.startsWith('hướng dẫn $keywordLower')) {
          score += bonusScore;
          // print('🎯   +$bonusScore bonus for "$keyword" at title start');
        }
      }

      // Điểm cho description chứa từ khóa
      if (description.contains(keywordLower)) {
        final descScore = baseScore * 0.2; // 20% của base score
        score += descScore;
        // print('🎯   +$descScore for "$keyword" in description');
      }
    }

    // Điểm cho channel uy tín (có từ khóa nấu ăn tiếng Việt)
    final channelTitle = video.channelTitle.toLowerCase();
    if (channelTitle.contains('nấu ăn') ||
        channelTitle.contains('bếp') ||
        channelTitle.contains('món ngon') ||
        channelTitle.contains('ẩm thực') ||
        channelTitle.contains('đầu bếp') ||
        channelTitle.contains('nhà bếp')) {
      score += 3.0;
    }

    // Trừ điểm nếu có từ khóa không liên quan (cả tiếng Việt và tiếng Anh)
    final irrelevantKeywords = [
      // Tiếng Việt
      'review', 'đánh giá', 'phản ứng', 'reaction',
      'mukbang', 'ăn show', 'thử thách', 'challenge',
      'asmr', 'vlog', 'blog', 'unboxing', 'mở hộp'
    ];
    for (final irrelevant in irrelevantKeywords) {
      if (title.contains(irrelevant) || description.contains(irrelevant)) {
        score -= 5.0;
      }
    }

    // Trừ điểm MẠNH nếu video chứa nguyên liệu khác với nguyên liệu được chọn
    if (query.preferredIngredients.isNotEmpty) {
      final conflictingIngredients = [
        'thịt gà', 'gà', 'chicken',
        'thịt bò', 'bò', 'beef',
        'thịt lợn', 'lợn', 'heo', 'pork',
        'cá', 'fish',
        'tôm', 'cua', 'hải sản', 'seafood'
      ];

      final selectedIngredients = query.preferredIngredients
          .map((e) => e.displayName.toLowerCase())
          .toList();

      // Conflict detection: trừ điểm mạnh cho video chứa nguyên liệu khác
      for (final conflicting in conflictingIngredients) {
        final isSelectedIngredient = selectedIngredients.any((selected) =>
            selected.contains(conflicting) || conflicting.contains(selected));

        if (!isSelectedIngredient && title.contains(conflicting)) {
          score -= 15.0; // Trừ điểm mạnh cho nguyên liệu conflict
        }
      }
    }

    // print('🎯 Final score: $score');
    return score;
  }

  /// Trích xuất từ khóa chính từ query
  List<String> _extractMainKeywords(MealSuggestionQuery query) {
    final keywords = <String>[];

    // Thêm preferred ingredients (convert MainIngredient to String)
    keywords.addAll(query.preferredIngredients.map((ingredient) => ingredient.displayName));

    // Thêm meal type keywords (chỉ tiếng Việt)
    switch (query.mealType) {
      case MealType.breakfast:
        keywords.addAll(['bữa sáng', 'sáng', 'điểm tâm']);
        break;
      case MealType.lunch:
        keywords.addAll(['bữa trưa', 'trưa', 'cơm trưa']);
        break;
      case MealType.dinner:
        keywords.addAll(['bữa tối', 'tối', 'cơm tối', 'bữa chiều']);
        break;
    }

    // Thêm vegetarian keywords nếu cần (chỉ tiếng Việt)
    if (query.isVegetarian) {
      keywords.addAll(['chay', 'thuần chay', 'không thịt', 'rau củ']);
    }

    return keywords;
  }

  /// Mock data cho testing (khi chưa có API key)
  Future<Map<String, dynamic>> _getMockVideos(String query) async {
    print('🎭 _getMockVideos được gọi với query: "$query"');

    // Simulate API delay
    print('🎭 Đang simulate API delay 500ms...');
    await Future.delayed(const Duration(milliseconds: 500));
    print('🎭 API delay hoàn thành, tạo mock data...');
    
    final mockVideos = [
      YouTubeVideo(
        id: 'mock1',
        title: 'Cách nấu $query đơn giản tại nhà',
        description: 'Hướng dẫn nấu $query một cách đơn giản và ngon miệng...',
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        channelTitle: 'Kênh Nấu Ăn Ngon',
        duration: const Duration(minutes: 12, seconds: 30),
        viewCount: 125000,
        publishedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      YouTubeVideo(
        id: 'mock2',
        title: '$query chuẩn vị - Bí quyết từ đầu bếp',
        description: 'Chia sẻ bí quyết nấu $query chuẩn vị từ đầu bếp chuyên nghiệp...',
        thumbnailUrl: 'https://i.ytimg.com/vi/9bZkp7q19f0/mqdefault.jpg',
        channelTitle: 'Đầu Bếp Chuyên Nghiệp',
        duration: const Duration(minutes: 8, seconds: 45),
        viewCount: 89000,
        publishedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      YouTubeVideo(
        id: 'mock3',
        title: '$query 15 phút - Nhanh gọn cho người bận rộn',
        description: 'Món $query chỉ mất 15 phút, phù hợp cho người bận rộn...',
        thumbnailUrl: 'https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg',
        channelTitle: 'Nấu Ăn Nhanh',
        duration: const Duration(minutes: 15, seconds: 20),
        viewCount: 67000,
        publishedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      YouTubeVideo(
        id: 'mock4',
        title: '$query healthy - Ăn ngon mà không lo tăng cân',
        description: 'Phiên bản healthy của $query, ít calo nhưng vẫn ngon...',
        thumbnailUrl: 'https://i.ytimg.com/vi/L_jWHffIx5E/mqdefault.jpg',
        channelTitle: 'Healthy Kitchen',
        duration: const Duration(minutes: 10, seconds: 15),
        viewCount: 45000,
        publishedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      YouTubeVideo(
        id: 'mock5',
        title: 'Top 5 cách nấu $query ngon nhất',
        description: 'Tổng hợp 5 cách nấu $query ngon và dễ làm nhất...',
        thumbnailUrl: 'https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',
        channelTitle: 'Top Món Ngon',
        duration: const Duration(minutes: 20, seconds: 30),
        viewCount: 156000,
        publishedAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];

    print('🎭 Mock data tạo thành công: ${mockVideos.length} videos');

    return {
      'success': true,
      'videos': mockVideos,
    };
  }

  /// Lấy URL để embed video
  String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }

  /// Lấy URL để xem video trên YouTube
  String getWatchUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }

  /// Lấy 3 comment tích cực nhất của video
  Future<List<YouTubeComment>> getTopPositiveComments(String videoId) async {
    print('💬 Đang lấy comments cho video: $videoId');

    try {
      // Gọi YouTube Comments API
      final url = Uri.parse(
        '${ApiConfig.youtubeBaseUrl}/commentThreads'
        '?part=snippet'
        '&videoId=$videoId'
        '&order=relevance'
        '&maxResults=50'
        '&key=${ApiConfig.youtubeApiKey}'
      );

      print('🔗 YouTube Comments API URL: $url');

      final response = await http.get(url);
      print('📡 YouTube Comments API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List<dynamic>? ?? [];

        print('💬 Tìm được ${items.length} comments');

        // Parse comments
        List<YouTubeComment> comments = items
            .map((item) => YouTubeComment.fromJson(item))
            .toList();

        // Lọc comments tích cực (có like > 0 và nội dung tích cực)
        List<YouTubeComment> positiveComments = comments.where((comment) {
          return comment.likeCount > 0 &&
                 _isPositiveComment(comment.textDisplay);
        }).toList();

        // Sắp xếp theo số like giảm dần
        positiveComments.sort((a, b) => b.likeCount.compareTo(a.likeCount));

        // Lấy top 3
        List<YouTubeComment> topComments = positiveComments.take(3).toList();

        print('💬 Lọc được ${topComments.length} comments tích cực');
        for (int i = 0; i < topComments.length; i++) {
          print('💬 Top ${i + 1}: "${topComments[i].textDisplay.substring(0, topComments[i].textDisplay.length > 50 ? 50 : topComments[i].textDisplay.length)}..." - ${topComments[i].likeCount} likes');
        }

        return topComments;
      } else {
        print('❌ YouTube Comments API error: ${response.statusCode}');
        return _getMockComments();
      }
    } catch (e) {
      print('❌ Error getting comments: $e');
      return _getMockComments();
    }
  }

  /// Kiểm tra comment có tích cực không
  bool _isPositiveComment(String text) {
    final positiveWords = [
      'ngon', 'tuyệt', 'hay', 'tốt', 'đẹp', 'xuất sắc', 'tuyệt vời',
      'amazing', 'good', 'great', 'excellent', 'perfect', 'love',
      'thích', 'yêu', 'cảm ơn', 'thanks', 'thank you', 'hữu ích',
      'bổ ích', 'học được', 'rất hay', 'quá tuyệt', 'tuyệt vời quá'
    ];

    final lowerText = text.toLowerCase();
    return positiveWords.any((word) => lowerText.contains(word));
  }

  /// Mock comments cho testing
  List<YouTubeComment> _getMockComments() {
    return [
      YouTubeComment(
        id: 'mock1',
        authorDisplayName: 'Minh Anh',
        authorProfileImageUrl: 'https://yt3.ggpht.com/a/default-user=s28-c-k-c0x00ffffff-no-rj',
        textDisplay: 'Món này nhìn ngon quá! Cảm ơn bạn đã chia sẻ công thức 😍\n\nMình đã thử làm theo và kết quả rất tuyệt vời. Gia đình ai cũng khen ngon.\n\nCác bước làm rất dễ hiểu, nguyên liệu cũng dễ tìm. Chắc chắn sẽ làm lại nhiều lần nữa! 👍',
        likeCount: 45,
        publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      YouTubeComment(
        id: 'mock2',
        authorDisplayName: 'Hương Giang',
        authorProfileImageUrl: 'https://yt3.ggpht.com/a/default-user=s28-c-k-c0x00ffffff-no-rj',
        textDisplay: 'Làm theo hướng dẫn và thành công rồi! Gia đình ai cũng khen ngon 👍\n\nĐặc biệt là phần gia vị, rất chuẩn vị. Con trai mình ăn hết 2 bát cơm luôn 😋\n\nCảm ơn chị đã chia sẻ. Mình sẽ theo dõi kênh để học thêm nhiều món ngon khác nữa!',
        likeCount: 32,
        publishedAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      YouTubeComment(
        id: 'mock3',
        authorDisplayName: 'Tuấn Anh',
        authorProfileImageUrl: 'https://yt3.ggpht.com/a/default-user=s28-c-k-c0x00ffffff-no-rj',
        textDisplay: 'Video rất hữu ích, cách làm đơn giản mà hiệu quả. Thanks bạn!\n\nMình là người mới học nấu ăn, nhưng làm theo video này thì thành công ngay lần đầu.\n\nHình ảnh rõ nét, giọng nói dễ nghe, giải thích chi tiết từng bước. Đây chính là kênh mình đang tìm kiếm! 🔥',
        likeCount: 28,
        publishedAt: DateTime.now().subtract(const Duration(hours: 8)),
      ),
    ];
  }
}
