import 'package:flutter/widgets.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_options.dart';
import 'package:http/http.dart' as http;
import '../services/simple_supabase_integration_service.dart';
import '../services/supabase_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Helper class để khởi tạo ứng dụng
class AppInit {
  static Future<void> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();

    // Khởi tạo Supabase
    await _initializeSupabase();

    // Khởi tạo Supabase Interceptor
    await _initializeSupabaseInterceptor();

    // Khởi tạo Supabase Integration Service
    await _initializeSupabaseIntegration();

    // Thực hiện migration
    await _performMigration();

    // Không khởi tạo repositories ở đây - sẽ khởi tạo sau khi đăng nhập
    print('✅ App initialization completed - ready for login');
  }

  /// Khởi tạo Supabase
  static Future<void> _initializeSupabase() async {
    try {
      print('Đang khởi tạo Supabase...');

      // Kiểm tra và xóa session cũ trước khi khởi tạo
      await _clearExpiredSession();

      await Supabase.initialize(
        url: SupabaseConfig.url,
        anonKey: SupabaseConfig.anonKey,
        debug: true,
      );

      print('Supabase đã được khởi tạo thành công');

      // Kiểm tra kết nối
      await _testSupabaseConnection();
    } catch (e) {
      // Xử lý lỗi JWT trong quá trình khởi tạo
      if (e.toString().contains('InvalidJWTToken') ||
          e.toString().contains('exp')) {
        print(
            'Phát hiện JWT token hết hạn trong quá trình khởi tạo, đang xử lý...');
        await handleJWTError();

        // Thử khởi tạo lại sau khi xóa session
        try {
          await Supabase.initialize(
            url: SupabaseConfig.url,
            anonKey: SupabaseConfig.anonKey,
            debug: true,
          );
          print('Supabase đã được khởi tạo thành công sau khi xử lý JWT');
        } catch (retryError) {
          print('Lỗi khởi tạo Supabase sau khi xử lý JWT: $retryError');
        }
      } else {
        print('Lỗi khởi tạo Supabase: $e');
      }
    }
  }

  /// Kiểm tra và xử lý session hết hạn trước khi khởi tạo
  static Future<void> _clearExpiredSession() async {
    try {
      print('Kiểm tra session trong SharedPreferences...');

      final prefs = await SharedPreferences.getInstance();
      final rememberLogin = prefs.getBool('rememberLogin') ?? false;

      // Vì Supabase chưa được khởi tạo, chúng ta chỉ có thể kiểm tra
      // session data trong SharedPreferences
      final keys = prefs.getKeys();
      final sessionKeys = keys
          .where((key) =>
              key.startsWith('sb-') &&
              (key.contains('session') || key.contains('token')))
          .toList();

      if (sessionKeys.isEmpty) {
        print('Không tìm thấy session data trong SharedPreferences');
        return;
      }

      // Nếu người dùng không chọn ghi nhớ, xóa tất cả session data
      if (!rememberLogin) {
        print('Người dùng không chọn ghi nhớ - xóa tất cả session data');
        for (final key in sessionKeys) {
          await prefs.remove(key);
          print('Đã xóa session key: $key');
        }
        print('Đã xóa ${sessionKeys.length} session keys');
        return;
      }

      // Nếu người dùng chọn ghi nhớ, giữ lại session data
      // Việc kiểm tra tính hợp lệ sẽ được thực hiện sau khi Supabase khởi tạo
      print('Người dùng đã chọn ghi nhớ - giữ lại session data để kiểm tra sau');
      print('Tìm thấy ${sessionKeys.length} session keys');

    } catch (e) {
      print('Lỗi khi kiểm tra session: $e');
    }
  }

  /// Kiểm tra và xử lý session hiện có sau khi Supabase đã khởi tạo
  static Future<void> _validateExistingSession() async {
    try {
      print('Kiểm tra session hiện có sau khi Supabase khởi tạo...');

      final prefs = await SharedPreferences.getInstance();
      final rememberLogin = prefs.getBool('rememberLogin') ?? false;

      if (!rememberLogin) {
        print('Người dùng không chọn ghi nhớ - bỏ qua kiểm tra session');
        return;
      }

      final session = Supabase.instance.client.auth.currentSession;
      if (session == null) {
        print('Không có session hiện tại dù người dùng chọn ghi nhớ');
        return;
      }

      // Kiểm tra thời hạn token
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
      final now = DateTime.now();

      if (expiresAt.isBefore(now.add(Duration(minutes: 5)))) {
        print('Session sắp hết hạn hoặc đã hết hạn - xóa session data');
        await _clearExpiredSessionData();
      } else {
        print('Session hợp lệ - giữ nguyên (hết hạn lúc: $expiresAt)');
      }

    } catch (e) {
      print('Lỗi khi kiểm tra session hiện có: $e');
    }
  }

  /// Xóa session data hết hạn
  static Future<void> _clearExpiredSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final sessionKeys = keys
          .where((key) =>
              key.startsWith('sb-') &&
              (key.contains('session') || key.contains('token')))
          .toList();

      for (final key in sessionKeys) {
        await prefs.remove(key);
        print('Đã xóa session key hết hạn: $key');
      }

      if (sessionKeys.isNotEmpty) {
        print('Đã xóa ${sessionKeys.length} session keys hết hạn');
      }
    } catch (e) {
      print('Lỗi khi xóa session data hết hạn: $e');
    }
  }

  /// Kiểm tra kết nối đến Supabase
  static Future<bool> _testSupabaseConnection() async {
    try {
      print("===== KIỂM TRA KẾT NỐI SUPABASE =====");

      // Bây giờ Supabase đã được khởi tạo, kiểm tra session có hợp lệ không
      await _validateExistingSession();

      // Lấy thông tin phiên hiện tại với error handling cải tiến
      try {
        final session = Supabase.instance.client.auth.currentSession;
        if (session != null) {
          // Kiểm tra thời hạn token trước khi gọi API
          final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
          final now = DateTime.now();

          if (expiresAt.isBefore(now)) {
            print("Phiên hiện tại: Token đã hết hạn (hết hạn lúc: $expiresAt)");
            await handleJWTError();
            return false;
          }

          // Token chưa hết hạn, kiểm tra tính hợp lệ
          await Supabase.instance.client.auth.getUser();
          print("Phiên hiện tại: Đã đăng nhập (token hợp lệ, hết hạn lúc: $expiresAt)");
        } else {
          print("Phiên hiện tại: Chưa đăng nhập");
        }
      } catch (e) {
        print("Lỗi kiểm tra session: $e");

        // Xử lý các loại lỗi JWT khác nhau
        if (e.toString().contains('invalid JWT') ||
            e.toString().contains('token is expired') ||
            e.toString().contains('bad_jwt') ||
            e.toString().contains('InvalidJWTToken') ||
            e.toString().contains('exp')) {
          print("🔧 Phát hiện lỗi JWT - đang xử lý...");
          await handleJWTError();
          return false;
        }
      }

      // Kiểm tra kết nối đến API
      final response = await http.get(
        Uri.parse(
            '${SupabaseConfig.url}/rest/v1/?apikey=${SupabaseConfig.anonKey}'),
        headers: {
          'apikey': SupabaseConfig.anonKey,
          'Authorization': 'Bearer ${SupabaseConfig.anonKey}',
        },
      ).timeout(const Duration(seconds: 5));

      print("Mã phản hồi: ${response.statusCode}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("✅ Kết nối đến Supabase thành công!");
        return true;
      } else {
        print(
            "❌ Không thể kết nối đến Supabase. Mã lỗi: ${response.statusCode}");
        print("Nội dung phản hồi: ${response.body}");
        return false;
      }
    } catch (e) {
      print("❌ Lỗi khi kiểm tra kết nối Supabase: $e");
      return false;
    }
  }

  /// Khởi tạo Supabase Interceptor
  static Future<void> _initializeSupabaseInterceptor() async {
    try {
      print('Đang khởi tạo Supabase Interceptor...');

      await SupabaseInterceptor.initialize();

      print('✅ Supabase Interceptor đã được khởi tạo thành công');
    } catch (e) {
      print('❌ Lỗi khởi tạo Supabase Interceptor: $e');
    }
  }

  /// Khởi tạo Supabase Integration Service (simplified)
  static Future<void> _initializeSupabaseIntegration() async {
    try {
      print('Đang khởi tạo Simple Supabase Integration Service...');

      final integrationService = SimpleSupabaseIntegrationService.instance;
      await integrationService.initialize();
      print('✅ Simple Supabase Integration Service đã được khởi tạo thành công');
    } catch (e) {
      print('❌ Lỗi khởi tạo Simple Supabase Integration Service: $e');
    }
  }

  /// Thực hiện migration dữ liệu (deprecated - không còn cần thiết)
  static Future<void> _performMigration() async {
    // Migration đã được thực hiện, bỏ qua bước này
    return;
  }

  /// Xử lý lỗi JWT token hết hạn với cơ chế thông minh cải tiến
  static Future<void> handleJWTError() async {
    try {
      print('🔧 Bắt đầu xử lý lỗi JWT...');

      final prefs = await SharedPreferences.getInstance();
      final rememberLogin = prefs.getBool('rememberLogin') ?? false;
      final userEmail = prefs.getString('user_email');

      // Nếu người dùng chọn ghi nhớ đăng nhập, thử refresh token trước
      if (rememberLogin && userEmail != null) {
        print('🔄 Người dùng đã chọn ghi nhớ ($userEmail) - thử refresh token...');

        try {
          // Thử refresh session với timeout ngắn hơn
          final refreshResult = await Supabase.instance.client.auth.refreshSession()
              .timeout(const Duration(seconds: 8));

          if (refreshResult.session != null &&
              refreshResult.session!.accessToken.isNotEmpty) {
            print('✅ Refresh token thành công - session đã được khôi phục');

            // Kiểm tra thời hạn token mới
            final expiresAt = DateTime.fromMillisecondsSinceEpoch(
                refreshResult.session!.expiresAt! * 1000);
            print('✅ Token mới hết hạn lúc: $expiresAt');
            return;
          }
        } catch (refreshError) {
          print('❌ Refresh token thất bại: $refreshError');

          // Nếu refresh thất bại nhưng user chọn ghi nhớ,
          // chỉ xóa session data nhưng giữ lại thông tin user để đăng nhập lại
          print('🔄 Giữ lại thông tin user để đăng nhập lại sau');
        }
      }

      // Đăng xuất và xóa session data
      print('🚪 Đăng xuất và xóa session data...');

      try {
        // Thử đăng xuất với timeout
        await Supabase.instance.client.auth.signOut()
            .timeout(const Duration(seconds: 5));
      } catch (signOutError) {
        print('⚠️ Lỗi khi đăng xuất: $signOutError');
        // Tiếp tục xóa local storage dù signOut thất bại
      }

      // Xóa session data nhưng giữ lại thông tin user nếu họ chọn ghi nhớ
      await _clearAllSessionData(prefs);

      // Chỉ reset "remember login" nếu refresh thất bại hoàn toàn
      if (!rememberLogin || userEmail == null) {
        await prefs.setBool('rememberLogin', false);
        print('✅ Đã reset setting "remember login"');
      } else {
        print('✅ Giữ lại setting "remember login" cho lần đăng nhập tiếp theo');
      }

      print('✅ Hoàn thành xử lý lỗi JWT');
    } catch (e) {
      print('❌ Lỗi khi xử lý JWT error: $e');

      // Fallback: Xóa tất cả dữ liệu local nếu có lỗi
      try {
        final prefs = await SharedPreferences.getInstance();
        await _clearAllSessionData(prefs);
        await prefs.setBool('rememberLogin', false);
        print('✅ Đã thực hiện fallback cleanup');
      } catch (fallbackError) {
        print('❌ Lỗi fallback cleanup: $fallbackError');
      }
    }
  }

  /// Xóa tất cả dữ liệu session
  static Future<void> _clearAllSessionData(SharedPreferences prefs) async {
    try {
      final keys = prefs.getKeys();
      final sessionKeys = keys.where((key) =>
        key.startsWith('sb-') ||
        key.contains('session') ||
        key.contains('token') ||
        key.contains('auth')
      ).toList();

      // Giữ lại một số thông tin cơ bản
      final keysToKeep = [
        'rememberLogin',
        'user_email',
        'user_id',
        'display_name',
        'photo_url',
        'app_version',
        'first_launch'
      ];

      for (final key in sessionKeys) {
        if (!keysToKeep.contains(key)) {
          await prefs.remove(key);
          print('🗑️ Đã xóa session key: $key');
        }
      }

      print('✅ Đã xóa ${sessionKeys.length} session keys');
    } catch (e) {
      print('❌ Lỗi khi xóa session data: $e');
    }
  }


}
