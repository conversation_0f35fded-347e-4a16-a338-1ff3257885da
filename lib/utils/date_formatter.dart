import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../l10n/app_localizations.dart';

/// Utility class để format date/time với localization support
class DateFormatter {
  /// Format ngày theo locale hiện tại
  static String formatDate(BuildContext context, DateTime date) {
    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('dd/MM/yyyy', 'vi').format(date);
    } else {
      return DateFormat('MM/dd/yyyy', 'en').format(date);
    }
  }

  /// Format ngày ngắn gọn (dd/MM hoặc MM/dd)
  static String formatShortDate(BuildContext context, DateTime date) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('dd/MM', 'vi').format(date);
    } else {
      return DateFormat('MM/dd', 'en').format(date);
    }
  }

  /// Format thời gian tương đối (vừa xong, 5 phút trước, etc.)
  static String formatRelativeTime(BuildContext context, DateTime dateTime) {
    final l10n = AppLocalizations.of(context);
    final locale = Localizations.localeOf(context);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return l10n?.justNow ?? 'Vừa xong';
    } else if (difference.inHours < 1) {
      final minutes = difference.inMinutes;
      if (locale.languageCode == 'vi') {
        return '$minutes phút trước';
      } else {
        return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
      }
    } else if (difference.inDays < 1) {
      final hours = difference.inHours;
      if (locale.languageCode == 'vi') {
        return '$hours giờ trước';
      } else {
        return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
      }
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      if (locale.languageCode == 'vi') {
        return '$days ngày trước';
      } else {
        return '$days ${days == 1 ? 'day' : 'days'} ago';
      }
    } else {
      // Hiển thị ngày cụ thể nếu quá 1 tuần
      return formatDate(context, dateTime);
    }
  }

  /// Format tên ngày trong tuần
  static String formatDayName(BuildContext context, DateTime date) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('EEEE', 'vi').format(date);
    } else {
      return DateFormat('EEEE', 'en').format(date);
    }
  }

  /// Format tên ngày trong tuần ngắn gọn
  static String formatShortDayName(BuildContext context, DateTime date) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('EEE', 'vi').format(date);
    } else {
      return DateFormat('EEE', 'en').format(date);
    }
  }

  /// Format tháng và năm
  static String formatMonthYear(BuildContext context, DateTime date) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('MMMM yyyy', 'vi').format(date);
    } else {
      return DateFormat('MMMM yyyy', 'en').format(date);
    }
  }

  /// Format thời gian (giờ:phút)
  static String formatTime(BuildContext context, DateTime dateTime) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('HH:mm', 'vi').format(dateTime);
    } else {
      return DateFormat('h:mm a', 'en').format(dateTime);
    }
  }

  /// Format ngày và thời gian đầy đủ
  static String formatDateTime(BuildContext context, DateTime dateTime) {
    final locale = Localizations.localeOf(context);
    
    if (locale.languageCode == 'vi') {
      return DateFormat('dd/MM/yyyy HH:mm', 'vi').format(dateTime);
    } else {
      return DateFormat('MM/dd/yyyy h:mm a', 'en').format(dateTime);
    }
  }

  /// Kiểm tra xem có phải hôm nay không
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// Kiểm tra xem có phải hôm qua không
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// Kiểm tra xem có phải ngày mai không
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && 
           date.month == tomorrow.month && 
           date.day == tomorrow.day;
  }

  /// Format ngày với context (hôm nay, hôm qua, ngày mai)
  static String formatDateWithContext(BuildContext context, DateTime date) {
    final l10n = AppLocalizations.of(context);
    
    if (isToday(date)) {
      return l10n?.today ?? 'Hôm nay';
    } else if (isYesterday(date)) {
      return l10n?.yesterday ?? 'Hôm qua';
    } else if (isTomorrow(date)) {
      return l10n?.tomorrow ?? 'Ngày mai';
    } else {
      return formatDate(context, date);
    }
  }
}
