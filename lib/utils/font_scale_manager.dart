import 'package:flutter/material.dart';
import 'dart:io';

/// Quản lý font scale theo chuẩn iOS Dynamic Type và Android Font Size
class FontScaleManager {
  FontScaleManager._();

  /// <PERSON><PERSON><PERSON> mức font scale theo chuẩn iOS Dynamic Type
  static const Map<String, double> _iosFontScales = {
    'xSmall': 0.82,      // iOS xSmall
    'Small': 0.88,       // iOS Small  
    'Medium': 0.94,      // iOS Medium
    'Large': 1.0,        // iOS Large (mặc định)
    'xLarge': 1.12,      // iOS xLarge
    'xxLarge': 1.23,     // iOS xxLarge
    'xxxLarge': 1.35,    // iOS xxxLarge
    'AX1': 1.4,          // Accessibility 1
    'AX2': 1.6,          // Accessibility 2 (giới hạn tối đa)
    'AX3': 1.9,          // Accessibility 3 (không hỗ trợ)
    'AX4': 2.35,         // Accessibility 4 (không hỗ trợ)
    'AX5': 2.76,         // Accessibility 5 (không hỗ trợ)
  };

  /// Lấy font scale hiện tại từ hệ thống
  static double getSystemFontScale(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    double systemScale = mediaQuery.textScaler.scale(1.0);
    
    // Giới hạn tối đa theo khuyến nghị
    if (Platform.isIOS) {
      // iOS: Giới hạn đến AX2 (1.6)
      return systemScale.clamp(0.82, 1.6);
    } else {
      // Android: Giới hạn fontScale ≤ 1.6f
      return systemScale.clamp(0.85, 1.6);
    }
  }

  /// Lấy tên mức font scale hiện tại (chỉ cho iOS)
  static String getCurrentFontScaleName(BuildContext context) {
    if (!Platform.isIOS) return 'Large';
    
    final currentScale = getSystemFontScale(context);
    
    // Tìm mức gần nhất
    String closestLevel = 'Large';
    double minDifference = double.infinity;
    
    for (final entry in _iosFontScales.entries) {
      final difference = (entry.value - currentScale).abs();
      if (difference < minDifference) {
        minDifference = difference;
        closestLevel = entry.key;
      }
    }
    
    return closestLevel;
  }

  /// Kiểm tra có phải font size lớn (accessibility) không
  static bool isAccessibilitySize(BuildContext context) {
    final scale = getSystemFontScale(context);
    return scale >= 1.4; // AX1 trở lên
  }

  /// Kiểm tra có phải font size rất lớn không
  static bool isExtraLargeSize(BuildContext context) {
    final scale = getSystemFontScale(context);
    return scale >= 1.6; // AX2 (giới hạn tối đa)
  }

  /// Tính toán font size responsive
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final scale = getSystemFontScale(context);
    return baseFontSize * scale;
  }

  /// Tính toán spacing responsive (margin, padding)
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final scale = getSystemFontScale(context);
    
    // Spacing tăng nhẹ hơn font để tránh layout quá rộng
    final spacingScale = 1.0 + ((scale - 1.0) * 0.5);
    return baseSpacing * spacingScale.clamp(1.0, 1.3);
  }

  /// Tính toán icon size responsive
  static double getResponsiveIconSize(BuildContext context, double baseIconSize) {
    final scale = getSystemFontScale(context);
    
    // Icon tăng ít hơn text để cân bằng
    final iconScale = 1.0 + ((scale - 1.0) * 0.3);
    return baseIconSize * iconScale.clamp(1.0, 1.2);
  }

  /// Lấy số dòng tối đa cho text dựa trên font scale
  static int getMaxLines(BuildContext context, int baseMaxLines) {
    if (isAccessibilitySize(context)) {
      // Font lớn → cho phép nhiều dòng hơn
      return (baseMaxLines * 1.5).round().clamp(baseMaxLines, baseMaxLines + 2);
    }
    return baseMaxLines;
  }

  /// Kiểm tra có nên chuyển layout sang dạng vertical không
  static bool shouldUseVerticalLayout(BuildContext context) {
    return isExtraLargeSize(context);
  }

  /// Debug info cho developer
  static Map<String, dynamic> getDebugInfo(BuildContext context) {
    final scale = getSystemFontScale(context);
    return {
      'platform': Platform.isIOS ? 'iOS' : 'Android',
      'systemFontScale': MediaQuery.of(context).textScaler.scale(1.0),
      'clampedFontScale': scale,
      'fontScaleName': getCurrentFontScaleName(context),
      'isAccessibilitySize': isAccessibilitySize(context),
      'isExtraLargeSize': isExtraLargeSize(context),
      'shouldUseVerticalLayout': shouldUseVerticalLayout(context),
    };
  }
}

/// Extension cho BuildContext để dễ sử dụng
extension FontScaleExtension on BuildContext {
  /// Lấy font scale hiện tại
  double get fontScale => FontScaleManager.getSystemFontScale(this);
  
  /// Kiểm tra có phải accessibility font không
  bool get isAccessibilityFont => FontScaleManager.isAccessibilitySize(this);
  
  /// Kiểm tra có phải font rất lớn không
  bool get isExtraLargeFont => FontScaleManager.isExtraLargeSize(this);
  
  /// Tính font size responsive
  double responsiveFontSize(double baseSize) => 
      FontScaleManager.getResponsiveFontSize(this, baseSize);
  
  /// Tính spacing responsive
  double responsiveSpacing(double baseSpacing) => 
      FontScaleManager.getResponsiveSpacing(this, baseSpacing);
  
  /// Tính icon size responsive
  double responsiveIconSize(double baseSize) => 
      FontScaleManager.getResponsiveIconSize(this, baseSize);
  
  /// Lấy max lines responsive
  int responsiveMaxLines(int baseMaxLines) => 
      FontScaleManager.getMaxLines(this, baseMaxLines);
  
  /// Kiểm tra có nên dùng vertical layout không
  bool get shouldUseVerticalLayout => 
      FontScaleManager.shouldUseVerticalLayout(this);
}
