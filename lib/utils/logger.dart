import 'dart:developer' as developer;

/// Utility class để logging thay thế cho print statements
class Logger {
  static const String _appName = 'CookSpark';
  
  /// Log thông tin debug (chỉ hiển thị trong debug mode)
  static void debug(String message, {String? tag}) {
    assert(() {
      developer.log(
        message,
        name: tag ?? _appName,
        level: 500, // Debug level
      );
      return true;
    }());
  }
  
  /// Log thông tin general
  static void info(String message, {String? tag}) {
    developer.log(
      message,
      name: tag ?? _appName,
      level: 800, // Info level
    );
  }
  
  /// Log cảnh báo
  static void warning(String message, {String? tag}) {
    developer.log(
      message,
      name: tag ?? _appName,
      level: 900, // Warning level
    );
  }
  
  /// Log lỗi
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    developer.log(
      message,
      name: tag ?? _appName,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
  }
}
