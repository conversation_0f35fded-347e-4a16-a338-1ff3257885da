import 'package:flutter/material.dart';

/// Modern page route với smooth transitions và animations
class ModernPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final ModernTransitionType transitionType;
  final Duration duration;
  final Curve curve;

  ModernPageRoute({
    required this.child,
    this.transitionType = ModernTransitionType.slideFromRight,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          settings: settings,
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (transitionType) {
      case ModernTransitionType.slideFromRight:
        return _buildSlideTransition(
          animation,
          secondaryAnimation,
          child,
          const Offset(1.0, 0.0),
        );
      case ModernTransitionType.slideFromLeft:
        return _buildSlideTransition(
          animation,
          secondaryAnimation,
          child,
          const Offset(-1.0, 0.0),
        );
      case ModernTransitionType.slideFromBottom:
        return _buildSlideTransition(
          animation,
          secondaryAnimation,
          child,
          const Offset(0.0, 1.0),
        );
      case ModernTransitionType.slideFromTop:
        return _buildSlideTransition(
          animation,
          secondaryAnimation,
          child,
          const Offset(0.0, -1.0),
        );
      case ModernTransitionType.fade:
        return _buildFadeTransition(animation, secondaryAnimation, child);
      case ModernTransitionType.scale:
        return _buildScaleTransition(animation, secondaryAnimation, child);
      case ModernTransitionType.rotation:
        return _buildRotationTransition(animation, secondaryAnimation, child);
      case ModernTransitionType.slideAndFade:
        return _buildSlideAndFadeTransition(animation, secondaryAnimation, child);
      case ModernTransitionType.scaleAndFade:
        return _buildScaleAndFadeTransition(animation, secondaryAnimation, child);
    }
  }

  Widget _buildSlideTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
    Offset begin,
  ) {
    final slideAnimation = Tween<Offset>(
      begin: begin,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final secondarySlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(-begin.dx * 0.3, -begin.dy * 0.3),
    ).animate(CurvedAnimation(
      parent: secondaryAnimation,
      curve: curve,
    ));

    return SlideTransition(
      position: slideAnimation,
      child: SlideTransition(
        position: secondarySlideAnimation,
        child: child,
      ),
    );
  }

  Widget _buildFadeTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final secondaryFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: secondaryAnimation,
      curve: curve,
    ));

    return FadeTransition(
      opacity: fadeAnimation,
      child: FadeTransition(
        opacity: secondaryFadeAnimation,
        child: child,
      ),
    );
  }

  Widget _buildScaleTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final secondaryScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: secondaryAnimation,
      curve: curve,
    ));

    return ScaleTransition(
      scale: scaleAnimation,
      child: ScaleTransition(
        scale: secondaryScaleAnimation,
        child: child,
      ),
    );
  }

  Widget _buildRotationTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final rotationAnimation = Tween<double>(
      begin: 0.1,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    return RotationTransition(
      turns: rotationAnimation,
      child: ScaleTransition(
        scale: scaleAnimation,
        child: FadeTransition(
          opacity: animation,
          child: child,
        ),
      ),
    );
  }

  Widget _buildSlideAndFadeTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: child,
      ),
    );
  }

  Widget _buildScaleAndFadeTransition(
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    final scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));

    return ScaleTransition(
      scale: scaleAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: child,
      ),
    );
  }
}

enum ModernTransitionType {
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  slideFromTop,
  fade,
  scale,
  rotation,
  slideAndFade,
  scaleAndFade,
}

/// Extension để dễ dàng navigate với modern transitions
extension ModernNavigation on BuildContext {
  /// Navigate với slide transition từ phải
  Future<T?> pushModern<T extends Object?>(
    Widget page, {
    ModernTransitionType transition = ModernTransitionType.slideFromRight,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return Navigator.of(this).push<T>(
      ModernPageRoute<T>(
        child: page,
        transitionType: transition,
        duration: duration,
        curve: curve,
      ),
    );
  }

  /// Replace current page với modern transition
  Future<T?> pushReplacementModern<T extends Object?, TO extends Object?>(
    Widget page, {
    ModernTransitionType transition = ModernTransitionType.slideFromRight,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    TO? result,
  }) {
    return Navigator.of(this).pushReplacement<T, TO>(
      ModernPageRoute<T>(
        child: page,
        transitionType: transition,
        duration: duration,
        curve: curve,
      ),
      result: result,
    );
  }

  /// Push và clear stack với modern transition
  Future<T?> pushAndClearModern<T extends Object?>(
    Widget page, {
    ModernTransitionType transition = ModernTransitionType.slideFromRight,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return Navigator.of(this).pushAndRemoveUntil<T>(
      ModernPageRoute<T>(
        child: page,
        transitionType: transition,
        duration: duration,
        curve: curve,
      ),
      (route) => false,
    );
  }
}
