import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_options.dart';

class OAuthHelper {
  final BuildContext context;
  final String provider;
  
  OAuthHelper(this.context, this.provider);
  
  Future<bool> signInWithOAuth() async {
    try {
      final redirectUrl = 'com.minhduc.naugiday://callback';
      
      // Đ<PERSON>y là phiên bản đơn giản hóa của điều kiện, loại bỏ null check không cần thiết
      if (redirectUrl.isNotEmpty) {
        print('Redirect URL: $redirectUrl');
      }
      
      // Gọi phương thức đăng nhập OAuth của Supabase
      final res = await Supabase.instance.client.auth.signInWithOAuth(
        _getOAuthProvider(),
        redirectTo: redirectUrl,
      );
      
      return res;
    } catch (e) {
      print('Lỗi khi đăng nhập OAuth: $e');
      // <PERSON><PERSON><PERSON> thị thông báo lỗi
      _showErrorDialog('<PERSON>ăng nhập thất bại', 'Không thể hoàn tất đăng nhập với $provider. Vui lòng thử lại sau.');
      return false;
    }
  }
  
  OAuthProvider _getOAuthProvider() {
    switch (provider.toLowerCase()) {
      case 'google':
        return OAuthProvider.google;
      default:
        return OAuthProvider.google;
    }
  }
  
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text('Đóng'),
          ),
        ],
      ),
    );
  }
  
  // Hỗ trợ xử lý URL deep link cho OAuth
  static Future<bool> handleDeepLink(String url) async {
    try {
      final client = Supabase.instance.client;
      await client.auth.getSessionFromUrl(Uri.parse(url));
      // Điều kiện response.session != null đã được loại bỏ theo cảnh báo
      // vì response.session được cho là không bao giờ null ở đây.
      // Việc gọi getSessionFromUrl thành công được coi là xử lý deep link thành công.
      return true;
    } catch (e) {
      print('Lỗi xử lý OAuth deeplink: $e');
      return false;
    }
  }
  
  // Mở URL trong trình duyệt
  static Future<bool> launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      return await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      print('Lỗi mở URL: $e');
      return false;
    }
  }
  
  // Tạo URL đăng nhập OAuth tùy chỉnh
  static String createOAuthLoginURL(OAuthProvider provider) {
    String providerName = provider.name.toLowerCase();
    return '${SupabaseConfig.url}/auth/v1/authorize?provider=$providerName&redirect_to=${SupabaseConfig.url}/auth/v1/callback';
  }
  
  // Hiển thị hộp thoại đăng nhập OAuth
  static Future<bool> showOAuthDialog(
    BuildContext context, 
    OAuthProvider provider
  ) async {
    bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Đăng nhập với ${provider.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Bạn sẽ được chuyển hướng đến trang đăng nhập của ${provider.name}.'),
            SizedBox(height: 16),
            Text('Sau khi đăng nhập, vui lòng quay lại ứng dụng để hoàn tất quá trình xác thực.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('Tiếp tục'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      final url = createOAuthLoginURL(provider);
      return await launchURL(url);
    }
    
    return false;
  }
}
