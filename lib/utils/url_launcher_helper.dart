import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlLauncherHelper {
  static Future<void> openUrl(String url, BuildContext context) async {
    if (url.isEmpty) {
      _showErrorSnackBar(context, 'URL không hợp lệ');
      return;
    }

    final Uri uri = Uri.parse(url);
    try {
      final canLaunch = await canLaunchUrl(uri);
      
      if (!canLaunch) {
        _showErrorSnackBar(context, 'Không thể mở URL này');
        return;
      }
      
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Lỗi khi mở URL: $e');
    }
  }

  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
} 