import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// AuthErrorWrapper bao bọc các widget authentication để xử lý lỗi
/// và cung cấp fallback UI khi có vấn đề với authentication
class AuthErrorWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onAuthError;
  final String? errorMessage;

  const AuthErrorWrapper({
    super.key,
    required this.child,
    this.onAuthError,
    this.errorMessage,
  });

  @override
  State<AuthErrorWrapper> createState() => _AuthErrorWrapperState();
}

class _AuthErrorWrapperState extends State<AuthErrorWrapper> {
  bool _hasError = false;
  String? _errorDetails;

  @override
  void initState() {
    super.initState();
    _checkAuthState();
  }

  void _checkAuthState() {
    try {
      // Kiểm tra trạng thái Supabase client
      final client = Supabase.instance.client;
      if (client.auth.currentUser == null) {
        print('AuthErrorWrapper: <PERSON><PERSON><PERSON>ng có user hiện tại');
      }
    } catch (e) {
      print('AuthErrorWrapper: Lỗi khi kiểm tra auth state: $e');
      setState(() {
        _hasError = true;
        _errorDetails = e.toString();
      });
      widget.onAuthError?.call();
    }
  }

  void _retryAuth() {
    setState(() {
      _hasError = false;
      _errorDetails = null;
    });
    _checkAuthState();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Lỗi Xác Thực',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.errorMessage ?? 
                  'Có lỗi xảy ra với hệ thống xác thực. Vui lòng thử lại.',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                if (_errorDetails != null) ...[
                  const SizedBox(height: 16),
                  ExpansionTile(
                    title: const Text('Chi tiết lỗi'),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          _errorDetails!,
                          style: const TextStyle(
                            fontSize: 12,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _retryAuth,
                  child: const Text('Thử Lại'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return widget.child;
  }
}
