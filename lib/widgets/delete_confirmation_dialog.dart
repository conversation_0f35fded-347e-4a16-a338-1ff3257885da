import 'package:flutter/material.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';

/// Custom confirmation dialog cho việc xóa video
class DeleteConfirmationDialog extends StatefulWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final IconData? icon;
  final Color? iconColor;

  const DeleteConfirmationDialog({
    Key? key,
    required this.title,
    required this.message,
    this.confirmText = 'Xóa',
    this.cancelText = 'Hủy',
    this.onConfirm,
    this.onCancel,
    this.icon = Icons.delete_outline,
    this.iconColor,
  }) : super(key: key);

  @override
  State<DeleteConfirmationDialog> createState() => _DeleteConfirmationDialogState();
}

class _DeleteConfirmationDialogState extends State<DeleteConfirmationDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _iconController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _iconAnimation;

  @override
  void initState() {
    super.initState();
    
    // Scale animation cho dialog
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // Icon animation
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _iconAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.bounceOut,
    ));

    // Bắt đầu animation
    _scaleController.forward();
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) _iconController.forward();
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  void _handleConfirm() {
    widget.onConfirm?.call();
    Navigator.of(context).pop(true);
  }

  void _handleCancel() {
    widget.onCancel?.call();
    Navigator.of(context).pop(false);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = widget.iconColor ?? Colors.red[600];

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 340),
          decoration: BoxDecoration(
            color: isDarkMode ? ModernColors.darkSurface4 : Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 30,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.red.withOpacity(0.1),
                blurRadius: 40,
                offset: const Offset(0, 0),
                spreadRadius: 10,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header với icon
              Container(
                padding: const EdgeInsets.fromLTRB(24, 32, 24, 16),
                child: Column(
                  children: [
                    // Animated Icon
                    ScaleTransition(
                      scale: _iconAnimation,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: iconColor?.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.icon,
                          size: 40,
                          color: iconColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Title
                    SafeText(
                      widget.title,
                      style: AppTextStyles.title2(context).copyWith(
                        fontWeight: FontWeight.w700,
                        color: isDarkMode ? Colors.white : Colors.grey[900],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    
                    // Message
                    SafeText(
                      widget.message,
                      style: AppTextStyles.body(context).copyWith(
                        color: isDarkMode ? Colors.white70 : Colors.grey[600],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              
              // Divider
              Container(
                height: 1,
                margin: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      (isDarkMode ? Colors.white : Colors.grey).withOpacity(0.2),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
              
              // Action buttons
              Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: isDarkMode 
                              ? Colors.grey[800] 
                              : Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isDarkMode 
                                ? Colors.grey[700]! 
                                : Colors.grey[300]!,
                            width: 1,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _handleCancel,
                            borderRadius: BorderRadius.circular(12),
                            child: Center(
                              child: SafeText(
                                widget.cancelText,
                                style: AppTextStyles.subhead(context).copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isDarkMode ? Colors.white70 : Colors.grey[700],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Confirm button
                    Expanded(
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.red[500]!,
                              Colors.red[600]!,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _handleConfirm,
                            borderRadius: BorderRadius.circular(12),
                            child: Center(
                              child: SafeText(
                                widget.confirmText,
                                style: AppTextStyles.subhead(context).copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Helper để hiển thị delete confirmation dialog
class DeleteConfirmationHelper {
  static Future<bool?> show(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Xóa',
    String cancelText = 'Hủy',
    IconData? icon = Icons.delete_outline,
    Color? iconColor,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) => DeleteConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        icon: icon,
        iconColor: iconColor,
      ),
    );
  }

  /// Hiển thị confirmation cho việc xóa video
  static Future<bool?> showDeleteVideo(
    BuildContext context, {
    required String videoTitle,
  }) {
    final l10n = AppLocalizations.of(context);
    return show(
      context,
      title: l10n?.deleteVideoSaved ?? 'Xóa video đã lưu',
      message: 'Bạn có chắc muốn xóa "$videoTitle" khỏi danh sách yêu thích?\n\nBạn có thể hoàn tác sau khi xóa.',
      confirmText: l10n?.deleteNow ?? 'Xóa ngay',
      cancelText: l10n?.cancelAction ?? 'Hủy bỏ',
      icon: Icons.delete_forever,
      iconColor: Colors.red[600],
    );
  }

  /// Hiển thị confirmation cho việc xóa tất cả
  static Future<bool?> showDeleteAll(
    BuildContext context, {
    required int count,
  }) {
    final l10n = AppLocalizations.of(context);
    return show(
      context,
      title: l10n?.deleteAllVideos ?? 'Xóa tất cả video',
      message: 'Bạn có chắc muốn xóa tất cả $count video đã lưu?\n\nHành động này không thể hoàn tác.',
      confirmText: l10n?.deleteAll ?? 'Xóa tất cả',
      cancelText: l10n?.cancelAction ?? 'Hủy bỏ',
      icon: Icons.delete_sweep,
      iconColor: Colors.red[700],
    );
  }

  /// Hiển thị confirmation cho việc đăng xuất
  static Future<bool?> showLogout(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return show(
      context,
      title: l10n?.logoutDialog ?? 'Đăng xuất',
      message: l10n?.confirmLogout ?? 'Bạn có chắc muốn đăng xuất khỏi tài khoản?\n\nDữ liệu đã lưu sẽ được giữ nguyên.',
      confirmText: l10n?.signOut ?? 'Đăng xuất',
      cancelText: l10n?.stayLoggedIn ?? 'Ở lại',
      icon: Icons.logout,
      iconColor: Colors.orange[600],
    );
  }

  /// Hiển thị confirmation cho việc reset dữ liệu
  static Future<bool?> showResetData(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return show(
      context,
      title: l10n?.deleteData ?? 'Xóa dữ liệu',
      message: l10n?.confirmDeleteData ?? 'Bạn có chắc muốn xóa tất cả dữ liệu ứng dụng?\n\nHành động này không thể hoàn tác.',
      confirmText: l10n?.deleteDataAction ?? 'Xóa dữ liệu',
      cancelText: l10n?.cancelAction ?? 'Hủy bỏ',
      icon: Icons.warning,
      iconColor: Colors.red[700],
    );
  }
}
