import 'package:flutter/material.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_spacing.dart';

/// Widget text có thể mở rộng để hiển thị đầy đủ nội dung
/// Tránh truncation và cải thiện trải nghiệm đọc
class ExpandableText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final int maxLines;
  final TextAlign? textAlign;
  final String expandText;
  final String collapseText;
  final Color? linkColor;
  final bool showExpandButton;

  const ExpandableText({
    super.key,
    required this.text,
    this.style,
    this.maxLines = 3,
    this.textAlign,
    this.expandText = 'Xem thêm',
    this.collapseText = 'Thu gọn',
    this.linkColor,
    this.showExpandButton = true,
  });

  @override
  State<ExpandableText> createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  bool _needsExpansion = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveLinkColor = widget.linkColor ?? theme.colorScheme.primary;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Tính toán xem text có cần expand không
        final textPainter = TextPainter(
          text: TextSpan(
            text: widget.text,
            style: widget.style,
          ),
          maxLines: widget.maxLines,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout(maxWidth: constraints.maxWidth);
        
        _needsExpansion = textPainter.didExceedMaxLines;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState: _isExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              firstChild: _buildCollapsedText(),
              secondChild: _buildExpandedText(),
            ),
            if (_needsExpansion && widget.showExpandButton) ...[
              AppSpacing.spaceXS,
              _buildExpandButton(effectiveLinkColor),
            ],
          ],
        );
      },
    );
  }

  Widget _buildCollapsedText() {
    return Text(
      widget.text,
      style: widget.style,
      maxLines: widget.maxLines,
      overflow: TextOverflow.ellipsis,
      textAlign: widget.textAlign,
    );
  }

  Widget _buildExpandedText() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Text(
        widget.text,
        style: widget.style,
        textAlign: widget.textAlign,
      ),
    );
  }

  Widget _buildExpandButton(Color linkColor) {
    return GestureDetector(
      onTap: _toggleExpansion,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(
          color: linkColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _isExpanded ? widget.collapseText : widget.expandText,
              style: AppTextStyles.labelSmall(context).copyWith(
                color: linkColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 4),
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: linkColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
}

/// Widget text với auto-wrap thông minh
/// Tự động xuống dòng và điều chỉnh layout để tránh overflow
class SmartText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final bool softWrap;
  final double? minFontSize;
  final double? maxFontSize;

  const SmartText({
    super.key,
    required this.text,
    this.style,
    this.textAlign,
    this.maxLines,
    this.softWrap = true,
    this.minFontSize,
    this.maxFontSize,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final effectiveStyle = style ?? DefaultTextStyle.of(context).style;
        final baseFontSize = effectiveStyle.fontSize ?? 14.0;
        
        // Tính toán font size phù hợp
        double fontSize = baseFontSize;
        if (minFontSize != null) {
          fontSize = fontSize.clamp(minFontSize!, maxFontSize ?? double.infinity);
        }

        // Kiểm tra xem text có fit không
        final textPainter = TextPainter(
          text: TextSpan(
            text: text,
            style: effectiveStyle.copyWith(fontSize: fontSize),
          ),
          maxLines: maxLines,
          textDirection: TextDirection.ltr,
        );
        textPainter.layout(maxWidth: constraints.maxWidth);

        // Nếu text quá dài và có minFontSize, giảm font size
        if (textPainter.didExceedMaxLines && minFontSize != null) {
          fontSize = minFontSize!;
        }

        return Text(
          text,
          style: effectiveStyle.copyWith(fontSize: fontSize),
          textAlign: textAlign,
          maxLines: maxLines,
          softWrap: softWrap,
          overflow: softWrap ? TextOverflow.visible : TextOverflow.ellipsis,
        );
      },
    );
  }
}

/// Widget container với text tự động điều chỉnh
/// Đảm bảo text luôn hiển thị đầy đủ trong container
class FlexibleTextContainer extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final int? maxLines;
  final TextAlign? textAlign;

  const FlexibleTextContainer({
    super.key,
    required this.text,
    this.style,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.maxLines,
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? AppSpacing.paddingMedium,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: IntrinsicHeight(
        child: SmartText(
          text: text,
          style: style,
          textAlign: textAlign,
          maxLines: maxLines,
          softWrap: true,
        ),
      ),
    );
  }
}

/// Widget cho title và description với layout tối ưu
class TitleDescriptionWidget extends StatelessWidget {
  final String title;
  final String? description;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;
  final int titleMaxLines;
  final int descriptionMaxLines;
  final bool expandable;
  final CrossAxisAlignment crossAxisAlignment;

  const TitleDescriptionWidget({
    super.key,
    required this.title,
    this.description,
    this.titleStyle,
    this.descriptionStyle,
    this.titleMaxLines = 3,
    this.descriptionMaxLines = 4,
    this.expandable = true,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment,
      children: [
        if (expandable)
          ExpandableText(
            text: title,
            style: titleStyle ?? AppTextStyles.titleMedium(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: titleMaxLines,
          )
        else
          SmartText(
            text: title,
            style: titleStyle ?? AppTextStyles.titleMedium(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: titleMaxLines,
          ),
        
        if (description != null && description!.isNotEmpty) ...[
          AppSpacing.spaceXS,
          if (expandable)
            ExpandableText(
              text: description!,
              style: descriptionStyle ?? AppTextStyles.bodyMedium(context).copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              maxLines: descriptionMaxLines,
            )
          else
            SmartText(
              text: description!,
              style: descriptionStyle ?? AppTextStyles.bodyMedium(context).copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              maxLines: descriptionMaxLines,
            ),
        ],
      ],
    );
  }
}
