import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../constants/app_text_styles.dart';
import 'safe_text.dart';

/// Dialog thông báo tính năng đang phát triển
/// Thi<PERSON><PERSON> kế theo quy chuẩn của app với animation và UI đẹp
class FeatureDevelopmentDialog extends StatefulWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color? iconColor;

  const FeatureDevelopmentDialog({
    super.key,
    required this.title,
    required this.message,
    this.icon = Icons.construction,
    this.iconColor,
  });

  /// Hiển thị dialog cho Weekly Menu feature
  static Future<void> showWeeklyMenuDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return showDialog(
      context: context,
      builder: (context) => FeatureDevelopmentDialog(
        title: l10n?.featureInDevelopment ?? 'Tính năng đang phát triển',
        message: l10n?.weeklyMenuComingSoon ?? 'T<PERSON><PERSON> năng thực đơn tuần đang được phát triển và sẽ sớm có mặt trong phiên bản tiếp theo!',
        icon: Icons.calendar_month,
        iconColor: Colors.orange,
      ),
    );
  }

  /// Hiển thị dialog cho Add to Weekly Menu feature
  static Future<void> showAddToWeeklyMenuDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return showDialog(
      context: context,
      builder: (context) => FeatureDevelopmentDialog(
        title: l10n?.featureInDevelopment ?? 'Tính năng đang phát triển',
        message: l10n?.addToWeeklyMenuComingSoon ?? 'Tính năng thêm vào thực đơn tuần đang được phát triển. Hãy theo dõi để cập nhật sớm nhất!',
        icon: Icons.add_circle_outline,
        iconColor: Colors.blue,
      ),
    );
  }

  @override
  State<FeatureDevelopmentDialog> createState() => _FeatureDevelopmentDialogState();
}

class _FeatureDevelopmentDialogState extends State<FeatureDevelopmentDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final l10n = AppLocalizations.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 10,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      colorScheme.surface,
                      colorScheme.surface.withOpacity(0.95),
                    ],
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Icon với animation
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: (widget.iconColor ?? colorScheme.primary).withOpacity(0.1),
                        border: Border.all(
                          color: widget.iconColor ?? colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        widget.icon,
                        size: 40,
                        color: widget.iconColor ?? colorScheme.primary,
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Title
                    SafeText(
                      widget.title,
                      style: AppTextStyles.title3(context).withWeight(FontWeight.bold),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Message
                    SafeText(
                      widget.message,
                      style: AppTextStyles.body(context).withColor(
                        colorScheme.onSurface.withOpacity(0.8),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 4,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: colorScheme.primary.withOpacity(0.3),
                                ),
                              ),
                            ),
                            child: SafeText(
                              l10n?.understood ?? 'Đã hiểu',
                              style: AppTextStyles.callout(context)
                                  .withWeight(FontWeight.w600)
                                  .withColor(colorScheme.primary),
                              maxLines: 1,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
