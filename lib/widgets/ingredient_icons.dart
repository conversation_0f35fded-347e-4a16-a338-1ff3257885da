import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/user_profile.dart';

/// Widget hiển thị icon SVG cho nguyên liệu
class IngredientIcon extends StatelessWidget {
  final MainIngredient ingredient;
  final double size;
  final Color? color;
  final bool isSelected;

  const IngredientIcon({
    super.key,
    required this.ingredient,
    this.size = 48,
    this.color,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = color ?? (isSelected 
        ? Theme.of(context).primaryColor 
        : Colors.grey[600]);

    return Container(
      width: size,
      height: size,
      child: _buildIngredientSvg(ingredient, iconColor!),
    );
  }

  Widget _buildIngredientSvg(MainIngredient ingredient, Color color) {
    switch (ingredient) {
      case MainIngredient.pork:
        return _buildPorkIcon(color);
      case MainIngredient.chicken:
        return _buildChickenIcon(color);
      case MainIngredient.beef:
        return _buildBeefIcon(color);
      case MainIngredient.fish:
        return _buildFishIcon(color);
      case MainIngredient.seafood:
        return _buildSeafoodIcon(color);
      case MainIngredient.vegetables:
        return _buildVegetablesIcon(color);
      case MainIngredient.tofu:
        return _buildTofuIcon(color);
      case MainIngredient.eggs:
        return _buildEggsIcon(color);
      case MainIngredient.noodles:
        return _buildNoodlesIcon(color);
      case MainIngredient.rice:
        return _buildRiceIcon(color);
    }
  }

  Widget _buildPorkIcon(Color color) {
    return CustomPaint(
      painter: PorkIconPainter(color),
    );
  }

  Widget _buildChickenIcon(Color color) {
    return CustomPaint(
      painter: ChickenIconPainter(color),
    );
  }

  Widget _buildBeefIcon(Color color) {
    return CustomPaint(
      painter: BeefIconPainter(color),
    );
  }

  Widget _buildFishIcon(Color color) {
    return CustomPaint(
      painter: FishIconPainter(color),
    );
  }

  Widget _buildSeafoodIcon(Color color) {
    return CustomPaint(
      painter: SeafoodIconPainter(color),
    );
  }

  Widget _buildVegetablesIcon(Color color) {
    return CustomPaint(
      painter: VegetablesIconPainter(color),
    );
  }

  Widget _buildTofuIcon(Color color) {
    return CustomPaint(
      painter: TofuIconPainter(color),
    );
  }

  Widget _buildEggsIcon(Color color) {
    return CustomPaint(
      painter: EggsIconPainter(color),
    );
  }

  Widget _buildNoodlesIcon(Color color) {
    return CustomPaint(
      painter: NoodlesIconPainter(color),
    );
  }

  Widget _buildRiceIcon(Color color) {
    return CustomPaint(
      painter: RiceIconPainter(color),
    );
  }
}

/// Custom painter cho icon thịt lợn
class PorkIconPainter extends CustomPainter {
  final Color color;

  PorkIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    
    // Vẽ hình thịt lợn stylized
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.3;
    
    // Body chính
    path.addOval(Rect.fromCenter(
      center: center,
      width: radius * 2,
      height: radius * 1.5,
    ));
    
    // Chân
    final legWidth = radius * 0.3;
    final legHeight = radius * 0.8;
    
    // Chân trái
    path.addRect(Rect.fromLTWH(
      center.dx - radius * 0.6,
      center.dy + radius * 0.4,
      legWidth,
      legHeight,
    ));
    
    // Chân phải
    path.addRect(Rect.fromLTWH(
      center.dx + radius * 0.3,
      center.dy + radius * 0.4,
      legWidth,
      legHeight,
    ));

    canvas.drawPath(path, paint);
    
    // Vẽ đường viền
    final strokePaint = Paint()
      ..color = color.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawPath(path, strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon thịt gà
class ChickenIconPainter extends CustomPainter {
  final Color color;

  ChickenIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.25;
    
    // Body
    path.addOval(Rect.fromCenter(
      center: center,
      width: radius * 2.2,
      height: radius * 1.8,
    ));
    
    // Head
    path.addOval(Rect.fromCenter(
      center: Offset(center.dx, center.dy - radius * 1.2),
      width: radius * 1.2,
      height: radius * 1.2,
    ));
    
    // Wing
    final wingPath = Path();
    wingPath.moveTo(center.dx + radius * 0.8, center.dy - radius * 0.3);
    wingPath.quadraticBezierTo(
      center.dx + radius * 1.5, center.dy,
      center.dx + radius * 0.8, center.dy + radius * 0.5,
    );
    wingPath.close();
    path.addPath(wingPath, Offset.zero);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon thịt bò
class BeefIconPainter extends CustomPainter {
  final Color color;

  BeefIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.3;
    
    // Vẽ miếng thịt bò hình chữ nhật bo góc
    final rect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: radius * 2.2,
        height: radius * 1.6,
      ),
      const Radius.circular(8),
    );
    
    canvas.drawRRect(rect, paint);
    
    // Vẽ các đường vân thịt
    final linePaint = Paint()
      ..color = color.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    for (int i = 0; i < 3; i++) {
      final y = center.dy - radius * 0.5 + (i * radius * 0.5);
      canvas.drawLine(
        Offset(center.dx - radius * 0.8, y),
        Offset(center.dx + radius * 0.8, y),
        linePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon cá
class FishIconPainter extends CustomPainter {
  final Color color;

  FishIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.25;
    
    // Body cá
    path.addOval(Rect.fromCenter(
      center: center,
      width: radius * 2.5,
      height: radius * 1.5,
    ));
    
    // Đuôi cá
    final tailPath = Path();
    tailPath.moveTo(center.dx + radius * 1.25, center.dy);
    tailPath.lineTo(center.dx + radius * 2, center.dy - radius * 0.8);
    tailPath.lineTo(center.dx + radius * 1.8, center.dy);
    tailPath.lineTo(center.dx + radius * 2, center.dy + radius * 0.8);
    tailPath.close();
    path.addPath(tailPath, Offset.zero);

    canvas.drawPath(path, paint);
    
    // Mắt cá
    final eyePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(center.dx - radius * 0.5, center.dy - radius * 0.3),
      radius * 0.2,
      eyePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon hải sản (tôm)
class SeafoodIconPainter extends CustomPainter {
  final Color color;

  SeafoodIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.2;
    
    // Body tôm cong
    path.moveTo(center.dx - radius * 1.5, center.dy + radius);
    path.quadraticBezierTo(
      center.dx, center.dy - radius * 1.5,
      center.dx + radius * 1.5, center.dy + radius,
    );
    path.quadraticBezierTo(
      center.dx, center.dy + radius * 0.5,
      center.dx - radius * 1.5, center.dy + radius,
    );

    canvas.drawPath(path, paint);
    
    // Râu tôm
    final antennaPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawLine(
      Offset(center.dx + radius * 1.5, center.dy + radius),
      Offset(center.dx + radius * 2.2, center.dy - radius * 0.5),
      antennaPaint,
    );
    
    canvas.drawLine(
      Offset(center.dx + radius * 1.5, center.dy + radius),
      Offset(center.dx + radius * 2.2, center.dy + radius * 1.5),
      antennaPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon rau củ
class VegetablesIconPainter extends CustomPainter {
  final Color color;

  VegetablesIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.2;
    
    // Cà rốt
    final carrotPath = Path();
    carrotPath.moveTo(center.dx - radius, center.dy + radius * 1.5);
    carrotPath.lineTo(center.dx - radius * 0.3, center.dy - radius);
    carrotPath.lineTo(center.dx, center.dy - radius);
    carrotPath.lineTo(center.dx - radius * 0.7, center.dy + radius * 1.5);
    carrotPath.close();
    
    canvas.drawPath(carrotPath, paint);
    
    // Lá cà rốt
    final leafPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;
    
    canvas.drawLine(
      Offset(center.dx - radius * 0.15, center.dy - radius),
      Offset(center.dx - radius * 0.15, center.dy - radius * 1.8),
      Paint()..color = Colors.green..strokeWidth = 3..style = PaintingStyle.stroke,
    );
    
    // Bông cải xanh
    final broccoliPaint = Paint()
      ..color = Colors.green.shade600
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(center.dx + radius * 0.8, center.dy - radius * 0.5),
      radius * 0.8,
      broccoliPaint,
    );
    
    // Thân bông cải
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(center.dx + radius * 0.8, center.dy + radius * 0.8),
        width: radius * 0.4,
        height: radius * 1.2,
      ),
      Paint()..color = Colors.brown.shade300,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon đậu phụ
class TofuIconPainter extends CustomPainter {
  final Color color;

  TofuIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.3;
    
    // Khối đậu phụ
    final rect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: radius * 2,
        height: radius * 1.5,
      ),
      const Radius.circular(4),
    );
    
    canvas.drawRRect(rect, paint);
    
    // Texture đậu phụ
    final texturePaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < 6; i++) {
      for (int j = 0; j < 4; j++) {
        final x = center.dx - radius * 0.7 + (i * radius * 0.28);
        final y = center.dy - radius * 0.5 + (j * radius * 0.33);
        canvas.drawCircle(Offset(x, y), 1.5, texturePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon trứng
class EggsIconPainter extends CustomPainter {
  final Color color;

  EggsIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.25;
    
    // Trứng 1
    final egg1Path = Path();
    egg1Path.addOval(Rect.fromCenter(
      center: Offset(center.dx - radius * 0.5, center.dy),
      width: radius * 1.2,
      height: radius * 1.8,
    ));
    
    // Trứng 2
    final egg2Path = Path();
    egg2Path.addOval(Rect.fromCenter(
      center: Offset(center.dx + radius * 0.5, center.dy),
      width: radius * 1.2,
      height: radius * 1.8,
    ));
    
    canvas.drawPath(egg1Path, paint);
    canvas.drawPath(egg2Path, paint);
    
    // Viền trứng
    final strokePaint = Paint()
      ..color = color.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawPath(egg1Path, strokePaint);
    canvas.drawPath(egg2Path, strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon mì/bún/phở
class NoodlesIconPainter extends CustomPainter {
  final Color color;

  NoodlesIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.3;
    
    // Vẽ các sợi mì cong
    for (int i = 0; i < 5; i++) {
      final path = Path();
      final startX = center.dx - radius + (i * radius * 0.5);
      final startY = center.dy - radius;
      
      path.moveTo(startX, startY);
      path.quadraticBezierTo(
        startX + radius * 0.3, center.dy,
        startX, center.dy + radius,
      );
      
      canvas.drawPath(path, paint);
    }
    
    // Vẽ tô
    final bowlPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    
    final bowlPath = Path();
    bowlPath.moveTo(center.dx - radius * 1.2, center.dy + radius * 0.8);
    bowlPath.quadraticBezierTo(
      center.dx, center.dy + radius * 1.5,
      center.dx + radius * 1.2, center.dy + radius * 0.8,
    );
    
    canvas.drawPath(bowlPath, bowlPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter cho icon cơm
class RiceIconPainter extends CustomPainter {
  final Color color;

  RiceIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.3;
    
    // Vẽ tô cơm
    final bowlPath = Path();
    bowlPath.moveTo(center.dx - radius, center.dy);
    bowlPath.quadraticBezierTo(
      center.dx, center.dy + radius * 1.2,
      center.dx + radius, center.dy,
    );
    bowlPath.lineTo(center.dx + radius * 0.8, center.dy - radius * 0.2);
    bowlPath.lineTo(center.dx - radius * 0.8, center.dy - radius * 0.2);
    bowlPath.close();
    
    canvas.drawPath(bowlPath, paint);
    
    // Vẽ hạt cơm
    final ricePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    for (int i = 0; i < 20; i++) {
      final x = center.dx - radius * 0.6 + (i % 5) * radius * 0.3;
      final y = center.dy - radius * 0.1 + (i ~/ 5) * radius * 0.15;
      canvas.drawOval(
        Rect.fromCenter(center: Offset(x, y), width: 3, height: 2),
        ricePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
