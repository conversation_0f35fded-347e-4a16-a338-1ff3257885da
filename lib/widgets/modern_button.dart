import 'package:flutter/material.dart';
import '../constants/modern_colors.dart';

/// Modern button với thiết kế hiện đại 2024
/// Hỗ trợ nhiều style: primary, secondary, outline, ghost, glassmorphism
class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonStyle style;
  final ModernButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? customColor;
  final Widget? child;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = ModernButtonStyle.primary,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  });

  const ModernButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : style = ModernButtonStyle.primary;

  const ModernButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : style = ModernButtonStyle.secondary;

  const ModernButton.outline({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : style = ModernButtonStyle.outline;

  const ModernButton.ghost({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : style = ModernButtonStyle.ghost;

  const ModernButton.glass({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.child,
  }) : style = ModernButtonStyle.glassmorphism;

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _resetPress();
  }

  void _onTapCancel() {
    _resetPress();
  }

  void _resetPress() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Container(
              width: widget.isFullWidth ? double.infinity : null,
              height: _getButtonHeight(),
              decoration: _getButtonDecoration(colorScheme, isDark),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(_getBorderRadius()),
                  onTap: widget.isLoading ? null : widget.onPressed,
                  child: Container(
                    padding: _getButtonPadding(),
                    child: _buildButtonContent(colorScheme, isDark),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 36;
      case ModernButtonSize.medium:
        return 48;
      case ModernButtonSize.large:
        return 56;
    }
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 8;
      case ModernButtonSize.medium:
        return 12;
      case ModernButtonSize.large:
        return 16;
    }
  }

  EdgeInsets _getButtonPadding() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case ModernButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ModernButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 12);
    }
  }

  BoxDecoration _getButtonDecoration(ColorScheme colorScheme, bool isDark) {
    final primaryColor = widget.customColor ?? colorScheme.primary;
    
    switch (widget.style) {
      case ModernButtonStyle.primary:
        return BoxDecoration(
          gradient: LinearGradient(
            colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: widget.onPressed != null && !widget.isLoading
              ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        );
        
      case ModernButtonStyle.secondary:
        return BoxDecoration(
          color: isDark 
              ? ModernColors.darkSurface3 
              : ModernColors.lightSurface2,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: isDark 
                ? ModernColors.borderPrimary 
                : ModernColors.lightSurface4,
          ),
        );
        
      case ModernButtonStyle.outline:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: primaryColor,
            width: 2,
          ),
        );
        
      case ModernButtonStyle.ghost:
        return BoxDecoration(
          color: _isPressed 
              ? primaryColor.withValues(alpha: 0.1) 
              : Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        );
        
      case ModernButtonStyle.glassmorphism:
        return BoxDecoration(
          color: isDark ? ModernColors.glassWhite : ModernColors.glassBlack,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: isDark 
                ? Colors.white.withValues(alpha: 0.2) 
                : Colors.black.withValues(alpha: 0.1),
          ),
        );
    }
  }

  Widget _buildButtonContent(ColorScheme colorScheme, bool isDark) {
    final textColor = _getTextColor(colorScheme, isDark);
    final fontSize = _getFontSize();
    
    if (widget.isLoading) {
      return Center(
        child: SizedBox(
          width: fontSize,
          height: fontSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textColor),
          ),
        ),
      );
    }

    if (widget.child != null) {
      return Center(child: widget.child!);
    }

    final content = <Widget>[];
    
    if (widget.icon != null) {
      content.add(Icon(
        widget.icon,
        size: fontSize,
        color: textColor,
      ));
      content.add(const SizedBox(width: 8));
    }
    
    content.add(Text(
      widget.text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
        color: textColor,
      ),
    ));

    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: content,
      ),
    );
  }

  Color _getTextColor(ColorScheme colorScheme, bool isDark) {
    final primaryColor = widget.customColor ?? colorScheme.primary;
    
    switch (widget.style) {
      case ModernButtonStyle.primary:
        return Colors.white;
      case ModernButtonStyle.secondary:
        return isDark ? Colors.white : Colors.black87;
      case ModernButtonStyle.outline:
      case ModernButtonStyle.ghost:
        return primaryColor;
      case ModernButtonStyle.glassmorphism:
        return isDark ? Colors.white : Colors.black87;
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return 14;
      case ModernButtonSize.medium:
        return 16;
      case ModernButtonSize.large:
        return 18;
    }
  }
}

enum ModernButtonStyle {
  primary,
  secondary,
  outline,
  ghost,
  glassmorphism,
}

enum ModernButtonSize {
  small,
  medium,
  large,
}
