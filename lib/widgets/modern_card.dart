import 'package:flutter/material.dart';
import '../constants/modern_colors.dart';

/// Modern card với thiết kế hiện đại 2024
/// Hỗ trợ nhiều style: elevated, outlined, filled, glassmorphism
class ModernCard extends StatefulWidget {
  final Widget child;
  final ModernCardStyle style;
  final double? elevation;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final VoidCallback? onTap;
  final Color? customColor;
  final double? borderRadius;
  final bool isInteractive;
  final bool showBorder;

  const ModernCard({
    super.key,
    required this.child,
    this.style = ModernCardStyle.elevated,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
    this.customColor,
    this.borderRadius,
    this.isInteractive = false,
    this.showBorder = false,
  });

  const ModernCard.elevated({
    super.key,
    required this.child,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
    this.customColor,
    this.borderRadius,
    this.isInteractive = false,
    this.showBorder = false,
  }) : style = ModernCardStyle.elevated;

  const ModernCard.outlined({
    super.key,
    required this.child,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
    this.customColor,
    this.borderRadius,
    this.isInteractive = false,
    this.showBorder = true,
  }) : style = ModernCardStyle.outlined;

  const ModernCard.filled({
    super.key,
    required this.child,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
    this.customColor,
    this.borderRadius,
    this.isInteractive = false,
    this.showBorder = false,
  }) : style = ModernCardStyle.filled;

  const ModernCard.glass({
    super.key,
    required this.child,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
    this.customColor,
    this.borderRadius,
    this.isInteractive = false,
    this.showBorder = true,
  }) : style = ModernCardStyle.glassmorphism;

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: _getBaseElevation(),
      end: _getBaseElevation() + 4,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getBaseElevation() {
    if (widget.elevation != null) return widget.elevation!;
    
    switch (widget.style) {
      case ModernCardStyle.elevated:
        return 4;
      case ModernCardStyle.outlined:
        return 0;
      case ModernCardStyle.filled:
        return 2;
      case ModernCardStyle.glassmorphism:
        return 8;
    }
  }

  void _onHover(bool isHovered) {
    if (!widget.isInteractive && widget.onTap == null) return;
    
    setState(() => _isHovered = isHovered);
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onTap == null) return;
    setState(() => _isPressed = true);
  }

  void _onTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() => _isPressed = false);
    }
  }

  void _onTapCancel() {
    if (_isPressed) {
      setState(() => _isPressed = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isPressed ? 0.98 : _scaleAnimation.value,
          child: Container(
            margin: widget.margin ?? const EdgeInsets.all(8),
            child: MouseRegion(
              onEnter: (_) => _onHover(true),
              onExit: (_) => _onHover(false),
              child: GestureDetector(
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                onTap: widget.onTap,
                child: Container(
                  decoration: _getCardDecoration(colorScheme, isDark),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      widget.borderRadius ?? 16,
                    ),
                    child: Container(
                      padding: widget.padding ?? const EdgeInsets.all(16),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _getCardDecoration(ColorScheme colorScheme, bool isDark) {
    final borderRadius = BorderRadius.circular(widget.borderRadius ?? 16);
    final customColor = widget.customColor;
    
    switch (widget.style) {
      case ModernCardStyle.elevated:
        return BoxDecoration(
          color: customColor ?? (isDark 
              ? ModernColors.darkSurface2 
              : ModernColors.lightSurface0),
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: isDark 
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.1),
              blurRadius: _elevationAnimation.value * 2,
              offset: Offset(0, _elevationAnimation.value / 2),
            ),
            if (isDark && _isHovered)
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 0),
              ),
          ],
          border: widget.showBorder
              ? Border.all(
                  color: isDark 
                      ? ModernColors.borderPrimary 
                      : ModernColors.lightSurface4,
                )
              : null,
        );
        
      case ModernCardStyle.outlined:
        return BoxDecoration(
          color: customColor ?? Colors.transparent,
          borderRadius: borderRadius,
          border: Border.all(
            color: _isHovered 
                ? colorScheme.primary 
                : (isDark 
                    ? ModernColors.borderPrimary 
                    : ModernColors.lightSurface4),
            width: _isHovered ? 2 : 1,
          ),
        );
        
      case ModernCardStyle.filled:
        return BoxDecoration(
          color: customColor ?? (isDark 
              ? ModernColors.darkSurface3 
              : ModernColors.lightSurface2),
          borderRadius: borderRadius,
          boxShadow: _isHovered
              ? [
                  BoxShadow(
                    color: isDark 
                        ? Colors.black.withValues(alpha: 0.2)
                        : Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        );
        
      case ModernCardStyle.glassmorphism:
        return BoxDecoration(
          color: customColor ?? (isDark 
              ? ModernColors.glassWhite 
              : ModernColors.glassBlack),
          borderRadius: borderRadius,
          border: Border.all(
            color: isDark 
                ? Colors.white.withValues(alpha: 0.2) 
                : Colors.black.withValues(alpha: 0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: isDark 
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.1),
              blurRadius: _elevationAnimation.value * 2,
              offset: Offset(0, _elevationAnimation.value / 2),
            ),
          ],
        );
    }
  }
}

enum ModernCardStyle {
  elevated,
  outlined,
  filled,
  glassmorphism,
}

/// Modern card với header và footer
class ModernCardWithHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final Widget child;
  final List<Widget>? actions;
  final ModernCardStyle style;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ModernCardWithHeader({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    required this.child,
    this.actions,
    this.style = ModernCardStyle.elevated,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ModernCard(
      style: style,
      onTap: onTap,
      padding: EdgeInsets.zero,
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || subtitle != null || leading != null || trailing != null)
            _buildHeader(theme),
          Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
          if (actions != null && actions!.isNotEmpty)
            _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Text(
                    title!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: 12),
            trailing!,
          ],
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: actions!
            .map((action) => Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: action,
                ))
            .toList(),
      ),
    );
  }
}
