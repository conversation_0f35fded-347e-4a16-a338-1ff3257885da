import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OtpInputWidget extends StatefulWidget {
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final TextEditingController? controller;
  final bool enabled;
  final String? errorText;

  const OtpInputWidget({
    super.key,
    required this.onCompleted,
    required this.onChanged,
    this.controller,
    this.enabled = true,
    this.errorText,
  });

  @override
  State<OtpInputWidget> createState() => _OtpInputWidgetState();
}

class _OtpInputWidgetState extends State<OtpInputWidget>
    with TickerProviderStateMixin {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;
  
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    
    // Khởi tạo controllers và focus nodes
    _controllers = List.generate(6, (index) => TextEditingController());
    _focusNodes = List.generate(6, (index) => FocusNode());
    
    // Animation cho hiệu ứng lắc khi có lỗi
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticIn,
    ));

    // Sync với controller từ bên ngoài nếu có
    if (widget.controller != null) {
      widget.controller!.addListener(_syncWithExternalController);
      _syncFromExternalController();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _animationController.dispose();
    widget.controller?.removeListener(_syncWithExternalController);
    super.dispose();
  }

  void _syncWithExternalController() {
    final text = widget.controller?.text ?? '';
    if (text.length <= 6) {
      for (int i = 0; i < 6; i++) {
        _controllers[i].text = i < text.length ? text[i] : '';
      }
    }
  }

  void _syncFromExternalController() {
    final text = widget.controller?.text ?? '';
    if (text.length <= 6) {
      for (int i = 0; i < 6; i++) {
        _controllers[i].text = i < text.length ? text[i] : '';
      }
    }
  }

  void _onTextChanged(String value, int index) {
    // Cập nhật controller bên ngoài
    final fullOtp = _controllers.map((c) => c.text).join();
    widget.controller?.text = fullOtp;
    widget.onChanged(fullOtp);

    if (value.isNotEmpty) {
      // Di chuyển đến ô tiếp theo
      if (index < 5) {
        _currentIndex = index + 1;
        _focusNodes[index + 1].requestFocus();
      } else {
        // Hoàn thành OTP
        _focusNodes[index].unfocus();
        widget.onCompleted(fullOtp);
      }
    }
  }

  void _onBackspace(int index) {
    if (_controllers[index].text.isEmpty && index > 0) {
      _currentIndex = index - 1;
      _focusNodes[index - 1].requestFocus();
    }
  }

  // Xóa tất cả OTP
  void clear() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _currentIndex = 0;
    _focusNodes[0].requestFocus();
    widget.controller?.clear();
    widget.onChanged('');
  }

  void _handlePaste(String pastedText) {
    // Xử lý paste text
    final cleanText = pastedText.replaceAll(RegExp(r'[^0-9]'), '');
    if (cleanText.length <= 6) {
      for (int i = 0; i < 6; i++) {
        _controllers[i].text = i < cleanText.length ? cleanText[i] : '';
      }
      
      final fullOtp = _controllers.map((c) => c.text).join();
      widget.controller?.text = fullOtp;
      widget.onChanged(fullOtp);
      
      if (cleanText.length == 6) {
        _focusNodes[5].unfocus();
        widget.onCompleted(fullOtp);
      } else if (cleanText.isNotEmpty) {
        final nextIndex = cleanText.length < 6 ? cleanText.length : 5;
        _currentIndex = nextIndex;
        _focusNodes[nextIndex].requestFocus();
      }
    }
  }

  void shake() {
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        AnimatedBuilder(
          animation: _shakeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(_shakeAnimation.value, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(6, (index) {
                  final isActive = _currentIndex == index;
                  final hasText = _controllers[index].text.isNotEmpty;
                  final hasError = widget.errorText != null;
                  
                  return Container(
                    width: 45,
                    height: 55,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: hasError
                            ? Colors.red
                            : isActive
                                ? colorScheme.primary
                                : hasText
                                    ? colorScheme.primary.withOpacity(0.5)
                                    : colorScheme.outline.withOpacity(0.3),
                        width: hasError || isActive ? 2 : 1,
                      ),
                      color: hasError
                          ? Colors.red.withOpacity(0.05)
                          : isActive
                              ? colorScheme.primary.withOpacity(0.05)
                              : hasText
                                  ? colorScheme.primary.withOpacity(0.02)
                                  : colorScheme.surface,
                      boxShadow: isActive
                          ? [
                              BoxShadow(
                                color: colorScheme.primary.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : hasError
                              ? [
                                  BoxShadow(
                                    color: Colors.red.withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                              : null,
                    ),
                    child: TextField(
                      controller: _controllers[index],
                      focusNode: _focusNodes[index],
                      enabled: widget.enabled,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      maxLength: 1,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: hasError
                            ? Colors.red
                            : hasText
                                ? colorScheme.onSurface
                                : colorScheme.onSurface.withOpacity(0.6),
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        counterText: '',
                        contentPadding: EdgeInsets.zero,
                      ),
                      onChanged: (value) {
                        if (value.length > 1) {
                          // Xử lý paste
                          _handlePaste(value);
                        } else {
                          _onTextChanged(value, index);
                        }
                      },
                      onTap: () {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                      onSubmitted: (value) {
                        if (value.isEmpty && index > 0) {
                          _onBackspace(index);
                        }
                      },
                    ),
                  );
                }),
              ),
            );
          },
        ),
        
        // Error message
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 16,
                  color: Colors.red,
                ),
                const SizedBox(width: 6),
                Text(
                  widget.errorText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // Hướng dẫn
        const SizedBox(height: 12),
        Text(
          'Nhập mã 6 chữ số được gửi đến email của bạn',
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
