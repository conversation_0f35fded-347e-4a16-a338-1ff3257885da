import 'package:flutter/material.dart';
import 'dart:async';

class RateLimitWidget extends StatefulWidget {
  final int initialMinutes;
  final VoidCallback? onCountdownComplete;
  final String? customMessage;

  const RateLimitWidget({
    super.key,
    required this.initialMinutes,
    this.onCountdownComplete,
    this.customMessage,
  });

  @override
  State<RateLimitWidget> createState() => _RateLimitWidgetState();
}

class _RateLimitWidgetState extends State<RateLimitWidget> {
  late int _remainingMinutes;
  late int _remainingSeconds;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _remainingMinutes = widget.initialMinutes;
    _remainingSeconds = 0;
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else if (_remainingMinutes > 0) {
          _remainingMinutes--;
          _remainingSeconds = 59;
        } else {
          // Countdown hoàn thành
          timer.cancel();
          widget.onCountdownComplete?.call();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_remainingMinutes <= 0 && _remainingSeconds <= 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Icon và tiêu đề
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.schedule,
                  color: Colors.orange.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Giới hạn gửi email',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      widget.customMessage ?? 'Vui lòng đợi để gửi lại',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Countdown timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.timer,
                  color: Colors.orange.shade700,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _formatTime(_remainingMinutes, _remainingSeconds),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade800,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Progress bar
          LinearProgressIndicator(
            value: _calculateProgress(),
            backgroundColor: Colors.orange.shade100,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade600),
          ),
          
          const SizedBox(height: 8),
          
          // Hướng dẫn
          Text(
            'Bạn có thể thử lại sau khi hết thời gian chờ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange.shade600,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatTime(int minutes, int seconds) {
    final minutesStr = minutes.toString().padLeft(2, '0');
    final secondsStr = seconds.toString().padLeft(2, '0');
    return '$minutesStr:$secondsStr';
  }

  double _calculateProgress() {
    final totalSeconds = widget.initialMinutes * 60;
    final remainingSeconds = (_remainingMinutes * 60) + _remainingSeconds;
    final elapsed = totalSeconds - remainingSeconds;
    return elapsed / totalSeconds;
  }
}

// Widget hiển thị thông tin rate limit đơn giản
class RateLimitInfo extends StatelessWidget {
  final int remainingAttempts;
  final int maxAttempts;

  const RateLimitInfo({
    super.key,
    required this.remainingAttempts,
    required this.maxAttempts,
  });

  @override
  Widget build(BuildContext context) {
    if (remainingAttempts >= maxAttempts) {
      return const SizedBox.shrink();
    }

    final isWarning = remainingAttempts <= 1;

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isWarning ? Colors.red.shade50 : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isWarning ? Colors.red.shade200 : Colors.blue.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isWarning ? Icons.warning_amber : Icons.info_outline,
            color: isWarning ? Colors.red.shade700 : Colors.blue.shade700,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Còn lại $remainingAttempts/$maxAttempts lần gửi email',
              style: TextStyle(
                color: isWarning ? Colors.red.shade700 : Colors.blue.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
