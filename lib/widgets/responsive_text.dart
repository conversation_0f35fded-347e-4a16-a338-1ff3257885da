import 'package:flutter/material.dart';
import '../utils/font_scale_manager.dart';
import '../constants/app_text_styles.dart';

/// Text widget responsive với Dynamic Font Size
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool softWrap;
  final double? textScaleFactor;

  const ResponsiveText(
    this.text, {
    Key? key,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.softWrap = true,
    this.textScaleFactor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Tính toán maxLines responsive
    final responsiveMaxLines = maxLines != null 
        ? context.responsiveMaxLines(maxLines!)
        : null;

    // Overflow strategy dựa trên font scale
    final responsiveOverflow = overflow ?? 
        (context.isAccessibilityFont ? TextOverflow.visible : TextOverflow.ellipsis);

    return Text(
      text,
      style: style,
      maxLines: responsiveMaxLines,
      overflow: responsiveOverflow,
      textAlign: textAlign,
      softWrap: softWrap,
      textScaler: textScaleFactor != null 
          ? TextScaler.linear(textScaleFactor!)
          : null,
    );
  }
}

/// Container responsive với Dynamic Font Size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.decoration,
    this.alignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Tính toán responsive padding/margin
    EdgeInsetsGeometry? responsivePadding;
    EdgeInsetsGeometry? responsiveMargin;

    if (padding != null) {
      if (padding is EdgeInsets) {
        final p = padding as EdgeInsets;
        responsivePadding = EdgeInsets.only(
          left: context.responsiveSpacing(p.left),
          top: context.responsiveSpacing(p.top),
          right: context.responsiveSpacing(p.right),
          bottom: context.responsiveSpacing(p.bottom),
        );
      } else {
        responsivePadding = padding;
      }
    }

    if (margin != null) {
      if (margin is EdgeInsets) {
        final m = margin as EdgeInsets;
        responsiveMargin = EdgeInsets.only(
          left: context.responsiveSpacing(m.left),
          top: context.responsiveSpacing(m.top),
          right: context.responsiveSpacing(m.right),
          bottom: context.responsiveSpacing(m.bottom),
        );
      } else {
        responsiveMargin = margin;
      }
    }

    return Container(
      padding: responsivePadding,
      margin: responsiveMargin,
      width: width,
      height: height,
      decoration: decoration,
      alignment: alignment,
      child: child,
    );
  }
}

/// Row/Column adaptive dựa trên font size
class AdaptiveRowColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool forceVertical;

  const AdaptiveRowColumn({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.forceVertical = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final shouldUseColumn = forceVertical || context.shouldUseVerticalLayout;

    if (shouldUseColumn) {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children,
      );
    } else {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children,
      );
    }
  }
}

/// Icon responsive với Dynamic Font Size
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;

  const ResponsiveIcon(
    this.icon, {
    Key? key,
    this.size,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final responsiveSize = size != null 
        ? context.responsiveIconSize(size!)
        : null;

    return Icon(
      icon,
      size: responsiveSize,
      color: color,
    );
  }
}

/// Button responsive với Dynamic Font Size
class ResponsiveElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const ResponsiveElevatedButton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Tăng padding cho accessibility
    final basePadding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
    final responsivePadding = EdgeInsets.symmetric(
      horizontal: context.responsiveSpacing(basePadding.horizontal / 2),
      vertical: context.responsiveSpacing(basePadding.vertical / 2),
    );

    final responsiveStyle = style?.copyWith(
      padding: WidgetStateProperty.all(responsivePadding),
    ) ?? ElevatedButton.styleFrom(
      padding: responsivePadding,
    );

    return ElevatedButton(
      onPressed: onPressed,
      style: responsiveStyle,
      child: child,
    );
  }
}

/// SizedBox responsive với Dynamic Font Size
class ResponsiveSizedBox extends StatelessWidget {
  final double? width;
  final double? height;
  final Widget? child;

  const ResponsiveSizedBox({
    Key? key,
    this.width,
    this.height,
    this.child,
  }) : super(key: key);

  const ResponsiveSizedBox.height(double height, {Key? key})
      : width = null,
        height = height,
        child = null,
        super(key: key);

  const ResponsiveSizedBox.width(double width, {Key? key})
      : width = width,
        height = null,
        child = null,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width != null ? context.responsiveSpacing(width!) : null,
      height: height != null ? context.responsiveSpacing(height!) : null,
      child: child,
    );
  }
}

/// Debug widget để hiển thị thông tin font scale
class FontScaleDebugInfo extends StatelessWidget {
  const FontScaleDebugInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final debugInfo = FontScaleManager.getDebugInfo(context);
    
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: debugInfo.entries.map((entry) {
          return Text(
            '${entry.key}: ${entry.value}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontFamily: 'monospace',
            ),
          );
        }).toList(),
      ),
    );
  }
}
