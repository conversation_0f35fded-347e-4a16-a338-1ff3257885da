import 'package:flutter/material.dart';
import '../utils/font_scale_manager.dart';

/// Widget Text an toàn, không bao giờ overflow
/// Luôn có overflow protection và responsive
class SafeText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final bool softWrap;
  final double? textScaleFactor;
  final StrutStyle? strutStyle;

  const SafeText(
    this.text, {
    super.key,
    this.style,
    this.maxLines,
    this.textAlign,
    this.overflow, // Will be determined by font scale
    this.softWrap = true, // Always allow wrapping
    this.textScaleFactor,
    this.strutStyle,
  });

  @override
  Widget build(BuildContext context) {
    // Tính toán maxLines responsive cho accessibility
    final responsiveMaxLines = maxLines != null
        ? context.responsiveMaxLines(maxLines!)
        : null;

    // Overflow strategy dựa trên font scale
    final responsiveOverflow = overflow ??
        (context.isAccessibilityFont ? TextOverflow.visible : TextOverflow.ellipsis);

    return Text(
      text,
      style: style,
      maxLines: responsiveMaxLines,
      textAlign: textAlign,
      overflow: responsiveOverflow,
      softWrap: softWrap,
      textScaler: textScaleFactor != null
          ? TextScaler.linear(textScaleFactor!)
          : null,
      strutStyle: strutStyle,
    );
  }
}

/// Widget Text với constrained width để đảm bảo không overflow
class ConstrainedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow overflow;
  final double? maxWidth;
  final double? maxWidthPercent; // Percent của screen width

  const ConstrainedText(
    this.text, {
    super.key,
    this.style,
    this.maxLines = 2,
    this.textAlign,
    this.overflow = TextOverflow.ellipsis,
    this.maxWidth,
    this.maxWidthPercent = 0.8, // Default 80% screen width
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final effectiveMaxWidth = maxWidth ?? (screenWidth * (maxWidthPercent ?? 0.8));

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: effectiveMaxWidth,
      ),
      child: SafeText(
        text,
        style: style,
        maxLines: maxLines,
        textAlign: textAlign,
        overflow: overflow,
      ),
    );
  }
}

/// Widget cho layout Row an toàn - tự động wrap text
class SafeRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const SafeRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children.map((child) {
        // Tự động wrap Text widget với Flexible
        if (child is Text || child is SafeText || child is ConstrainedText) {
          return Flexible(child: child);
        }
        return child;
      }).toList(),
    );
  }
}

/// Widget cho layout Column an toàn - có thể scroll nếu cần
class SafeColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool scrollable;

  const SafeColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    final column = Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );

    if (scrollable) {
      return SingleChildScrollView(child: column);
    }
    return column;
  }
}

/// Layout utilities để tránh overflow
class SafeLayout {
  /// Tạo Row an toàn với automatic text wrapping
  static Widget row({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return SafeRow(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }

  /// Tạo text với width constraint
  static Widget constrainedText(
    String text, {
    required BuildContext context,
    TextStyle? style,
    double? maxWidth,
    int? maxLines = 2,
    TextAlign? textAlign,
  }) {
    return ConstrainedText(
      text,
      style: style,
      maxWidth: maxWidth,
      maxLines: maxLines,
      textAlign: textAlign,
    );
  }

  /// Padding responsive theo screen size
  static EdgeInsets responsivePadding(BuildContext context, {
    double horizontal = 16.0,
    double vertical = 16.0,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    double horizontalMultiplier = 1.0;
    if (screenWidth < 350) {
      horizontalMultiplier = 0.8; // Ít padding hơn cho màn hình nhỏ
    } else if (screenWidth > 600) {
      horizontalMultiplier = 1.2; // Nhiều padding hơn cho tablet
    }

    return EdgeInsets.symmetric(
      horizontal: horizontal * horizontalMultiplier,
      vertical: vertical,
    );
  }

  /// Container width responsive
  static double responsiveWidth(BuildContext context, {
    double percent = 0.9,
    double? maxWidth,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth * percent;
    
    if (maxWidth != null && calculatedWidth > maxWidth) {
      return maxWidth;
    }
    
    return calculatedWidth;
  }

  /// Check if screen is small
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 400;
  }

  /// Check if screen is tablet
  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.width >= 600;
  }
}

/// Mixin cho StatefulWidget để dễ dàng access safe layout methods
mixin SafeLayoutMixin<T extends StatefulWidget> on State<T> {
  bool get isSmallScreen => SafeLayout.isSmallScreen(context);
  bool get isTablet => SafeLayout.isTablet(context);
  
  EdgeInsets responsivePadding({double horizontal = 16.0, double vertical = 16.0}) {
    return SafeLayout.responsivePadding(context, horizontal: horizontal, vertical: vertical);
  }
  
  double responsiveWidth({double percent = 0.9, double? maxWidth}) {
    return SafeLayout.responsiveWidth(context, percent: percent, maxWidth: maxWidth);
  }
}
