import 'package:flutter/material.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../widgets/safe_text.dart';
import '../l10n/app_localizations.dart';

/// Custom notification widget cho việc lưu video
class SaveVideoNotification extends StatefulWidget {
  final String message;
  final bool isSuccess;
  final VoidCallback? onViewList;
  final VoidCallback? onDismiss;

  const SaveVideoNotification({
    Key? key,
    required this.message,
    this.isSuccess = true,
    this.onViewList,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<SaveVideoNotification> createState() => _SaveVideoNotificationState();
}

class _SaveVideoNotificationState extends State<SaveVideoNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    // Slide animation từ trên xuống
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // Scale animation cho icon
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.bounceOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.0, 0.5),
    ));

    // Bắt đầu animation
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _scaleController.forward();
    });

    // Tự động ẩn sau 4 giây
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) _dismiss();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _slideController.reverse();
    if (widget.onDismiss != null) {
      widget.onDismiss!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);
    
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _opacityAnimation,
        child: Container(
          margin: const EdgeInsets.fromLTRB(16, 60, 16, 0),
          decoration: BoxDecoration(
            color: isDarkMode ? ModernColors.darkSurface4 : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: widget.isSuccess 
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                blurRadius: 30,
                offset: const Offset(0, 0),
                spreadRadius: 5,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _dismiss,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Animated Icon
                    ScaleTransition(
                      scale: _scaleAnimation,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: widget.isSuccess 
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.isSuccess 
                              ? Icons.bookmark_added
                              : Icons.error_outline,
                          color: widget.isSuccess 
                              ? Colors.green[600]
                              : Colors.red[600],
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SafeText(
                            widget.isSuccess
                                ? (l10n?.savedSuccessfully ?? 'Đã lưu thành công!')
                                : (l10n?.saveError ?? 'Lỗi lưu video'),
                            style: AppTextStyles.subhead(context).copyWith(
                              fontWeight: FontWeight.w600,
                              color: widget.isSuccess
                                  ? Colors.green[700]
                                  : Colors.red[700],
                            ),
                          ),
                          const SizedBox(height: 4),
                          SafeText(
                            widget.message,
                            style: AppTextStyles.body(context).copyWith(
                              color: isDarkMode ? Colors.white70 : Colors.grey[600],
                              height: 1.3,
                            ),
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),
                    
                    // Action buttons
                    if (widget.isSuccess && widget.onViewList != null) ...[
                      const SizedBox(width: 12),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // View list button
                          Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  _dismiss();
                                  widget.onViewList?.call();
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  child: SafeText(
                                    'Xem',
                                    style: AppTextStyles.caption1(context).copyWith(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          // Close button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: _dismiss,
                                borderRadius: BorderRadius.circular(16),
                                child: Padding(
                                  padding: const EdgeInsets.all(4),
                                  child: Icon(
                                    Icons.close,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      // Close button only
                      const SizedBox(width: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _dismiss,
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: Icon(
                                Icons.close,
                                size: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom notification widget cho việc xóa video
class DeleteVideoNotification extends StatefulWidget {
  final String message;
  final bool isSuccess;
  final VoidCallback? onUndo;
  final VoidCallback? onDismiss;

  const DeleteVideoNotification({
    Key? key,
    required this.message,
    this.isSuccess = true,
    this.onUndo,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<DeleteVideoNotification> createState() => _DeleteVideoNotificationState();
}

class _DeleteVideoNotificationState extends State<DeleteVideoNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // Slide animation từ trên xuống
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Scale animation cho icon
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.bounceOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.0, 0.5),
    ));

    // Bắt đầu animation
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _scaleController.forward();
    });

    // Tự động ẩn sau 5 giây (lâu hơn để có thời gian undo)
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) _dismiss();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _slideController.reverse();
    if (widget.onDismiss != null) {
      widget.onDismiss!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _opacityAnimation,
        child: Container(
          margin: const EdgeInsets.fromLTRB(16, 60, 16, 0),
          decoration: BoxDecoration(
            color: isDarkMode ? ModernColors.darkSurface4 : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: widget.isSuccess
                    ? Colors.orange.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                blurRadius: 30,
                offset: const Offset(0, 0),
                spreadRadius: 5,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _dismiss,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Animated Icon
                    ScaleTransition(
                      scale: _scaleAnimation,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: widget.isSuccess
                              ? Colors.orange.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.isSuccess
                              ? Icons.delete_sweep
                              : Icons.error_outline,
                          color: widget.isSuccess
                              ? Colors.orange[600]
                              : Colors.red[600],
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SafeText(
                            widget.isSuccess ? 'Đã xóa thành công!' : 'Lỗi xóa video',
                            style: AppTextStyles.subhead(context).copyWith(
                              fontWeight: FontWeight.w600,
                              color: widget.isSuccess
                                  ? Colors.orange[700]
                                  : Colors.red[700],
                            ),
                          ),
                          const SizedBox(height: 4),
                          SafeText(
                            widget.message,
                            style: AppTextStyles.body(context).copyWith(
                              color: isDarkMode ? Colors.white70 : Colors.grey[600],
                              height: 1.3,
                            ),
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),

                    // Action buttons
                    if (widget.isSuccess && widget.onUndo != null) ...[
                      const SizedBox(width: 12),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Undo button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  _dismiss();
                                  widget.onUndo?.call();
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  child: SafeText(
                                    'Hoàn tác',
                                    style: AppTextStyles.caption1(context).copyWith(
                                      color: Colors.orange[600],
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          // Close button
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: _dismiss,
                                borderRadius: BorderRadius.circular(16),
                                child: Padding(
                                  padding: const EdgeInsets.all(4),
                                  child: Icon(
                                    Icons.close,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      // Close button only
                      const SizedBox(width: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _dismiss,
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: Icon(
                                Icons.close,
                                size: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Helper để hiển thị save notification
class SaveVideoNotificationHelper {
  static OverlayEntry? _currentOverlay;

  static void show(
    BuildContext context, {
    required String message,
    bool isSuccess = true,
    VoidCallback? onViewList,
  }) {
    // Ẩn notification hiện tại nếu có
    hide();

    final overlay = Overlay.of(context);
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: 0,
        left: 0,
        right: 0,
        child: SaveVideoNotification(
          message: message,
          isSuccess: isSuccess,
          onViewList: onViewList,
          onDismiss: hide,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);
  }

  /// Hiển thị notification cho việc xóa video
  static void showDelete(
    BuildContext context, {
    required String message,
    bool isSuccess = true,
    VoidCallback? onUndo,
  }) {
    // Ẩn notification hiện tại nếu có
    hide();

    final overlay = Overlay.of(context);
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: 0,
        left: 0,
        right: 0,
        child: DeleteVideoNotification(
          message: message,
          isSuccess: isSuccess,
          onUndo: onUndo,
          onDismiss: hide,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);
  }

  static void hide() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
