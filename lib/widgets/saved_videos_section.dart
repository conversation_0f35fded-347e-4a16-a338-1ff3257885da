import 'package:flutter/material.dart';
import '../services/saved_video_service.dart';
import '../models/saved_video.dart';
import '../screens/youtube_player_screen.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../widgets/safe_text.dart';

/// Widget hiển thị section món ăn đã lưu trên home screen
class SavedVideosSection extends StatefulWidget {
  const SavedVideosSection({Key? key}) : super(key: key);

  @override
  State<SavedVideosSection> createState() => _SavedVideosSectionState();
}

class _SavedVideosSectionState extends State<SavedVideosSection> {
  final SavedVideoService _savedVideoService = SavedVideoService();
  List<SavedVideo> _savedVideos = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedVideos();
  }

  Future<void> _loadSavedVideos() async {
    try {
      final videos = await _savedVideoService.getSavedVideos();
      if (mounted) {
        setState(() {
          _savedVideos = videos.take(5).toList(); // Chỉ hiển thị 5 video gần nhất
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Lỗi tải saved videos: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _openVideo(SavedVideo savedVideo) {
    Navigator.of(context).pushNamed(
      YouTubePlayerScreen.routeName,
      arguments: savedVideo.video,
    );
  }

  void _viewAllSavedVideos() {
    // TODO: Navigate to saved videos screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng xem tất cả món ăn đã lưu sẽ được phát triển'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (_isLoading) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? ModernColors.darkSurface2 : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_savedVideos.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? ModernColors.darkSurface2 : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.bookmark_outline,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                SafeText(
                  'Món ăn đã lưu',
                  style: AppTextStyles.title3(context).copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SafeText(
              'Chưa có món ăn nào được lưu.\nHãy tìm kiếm và lưu những món ăn yêu thích!',
              style: AppTextStyles.body(context).copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? ModernColors.darkSurface2 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.bookmark,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  SafeText(
                    'Món ăn đã lưu',
                    style: AppTextStyles.title3(context).copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: _viewAllSavedVideos,
                child: SafeText(
                  'Xem tất cả',
                  style: AppTextStyles.caption1(context).copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Video list
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _savedVideos.length,
              itemBuilder: (context, index) {
                final savedVideo = _savedVideos[index];
                return _buildVideoCard(savedVideo, isDarkMode);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(SavedVideo savedVideo, bool isDarkMode) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 12),
      child: GestureDetector(
        onTap: () => _openVideo(savedVideo),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail
            Container(
              height: 90,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: DecorationImage(
                  image: NetworkImage(savedVideo.video.thumbnailUrl),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Play button overlay
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  // Duration badge
                  Positioned(
                    bottom: 4,
                    right: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: SafeText(
                        _formatDuration(savedVideo.video.duration),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            
            // Title
            SafeText(
              savedVideo.video.title,
              style: AppTextStyles.caption1(context).copyWith(
                fontWeight: FontWeight.w500,
                height: 1.3,
              ),
              maxLines: 3,
              overflow: TextOverflow.visible,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }
}
