import 'package:flutter/material.dart';

/// Widget tạo background pattern trang trí
/// Sử dụng chung cho cả màn thêm mới và chỉnh sửa món ăn
class BackgroundPatternWidget extends StatelessWidget {
  final Color primaryColor;
  final bool isDarkMode;
  final Widget child;

  const BackgroundPatternWidget({
    super.key,
    required this.primaryColor,
    required this.isDarkMode,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Stack(
      children: [
        // Background color
        Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      const Color(0xFF1A1A1A),
                      const Color(0xFF2D2D2D),
                    ]
                  : [
                      Colors.grey.shade50,
                      Colors.white,
                    ],
            ),
          ),
        ),

        // Pattern trang trí - Hình tròn lớn phía trên bên phải
        Positioned(
          top: -screenHeight * 0.1,
          right: -screenWidth * 0.2,
          child: Opacity(
            opacity: isDarkMode ? 0.03 : 0.04,
            child: Container(
              width: screenWidth * 0.8,
              height: screenWidth * 0.8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: primaryColor,
              ),
            ),
          ),
        ),

        // Pattern trang trí - Hình tròn nhỏ phía dưới bên trái
        Positioned(
          bottom: -screenHeight * 0.05,
          left: -screenWidth * 0.3,
          child: Opacity(
            opacity: isDarkMode ? 0.04 : 0.05,
            child: Container(
              width: screenWidth * 0.7,
              height: screenWidth * 0.7,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: primaryColor,
              ),
            ),
          ),
        ),

        // Pattern trang trí - Hình tròn trung bình ở giữa
        Positioned(
          top: screenHeight * 0.3,
          left: -screenWidth * 0.1,
          child: Opacity(
            opacity: isDarkMode ? 0.02 : 0.03,
            child: Container(
              width: screenWidth * 0.4,
              height: screenWidth * 0.4,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: primaryColor,
              ),
            ),
          ),
        ),

        // Geometric patterns - Tam giác
        Positioned(
          top: screenHeight * 0.15,
          right: screenWidth * 0.1,
          child: Opacity(
            opacity: isDarkMode ? 0.02 : 0.03,
            child: CustomPaint(
              size: Size(screenWidth * 0.15, screenWidth * 0.15),
              painter: TrianglePainter(color: primaryColor),
            ),
          ),
        ),

        // Geometric patterns - Hình vuông xoay
        Positioned(
          bottom: screenHeight * 0.2,
          right: screenWidth * 0.05,
          child: Opacity(
            opacity: isDarkMode ? 0.025 : 0.035,
            child: Transform.rotate(
              angle: 0.785398, // 45 degrees
              child: Container(
                width: screenWidth * 0.12,
                height: screenWidth * 0.12,
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),

        // Main content
        child,
      ],
    );
  }
}

/// Custom painter để vẽ hình tam giác
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// Widget container với background pattern và shadow
class PatternedContainer extends StatelessWidget {
  final Widget child;
  final Color primaryColor;
  final bool isDarkMode;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const PatternedContainer({
    super.key,
    required this.child,
    required this.primaryColor,
    required this.isDarkMode,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding ?? const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.1),
            blurRadius: 40,
            offset: const Offset(0, 16),
          ),
        ],
      ),
      child: child,
    );
  }
}
