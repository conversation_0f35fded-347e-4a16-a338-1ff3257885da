import 'package:flutter/material.dart';

/// Widget navigation buttons cho wizard flow
/// Sử dụng chung cho cả màn thêm mới và chỉnh sửa món ăn
class WizardNavigationWidget extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final bool canGoNext;
  final bool canGoPrevious;
  final bool isLoading;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final VoidCallback? onComplete;
  final Color primaryColor;
  final bool isDarkMode;
  final String? nextButtonText;
  final String? completeButtonText;

  const WizardNavigationWidget({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.canGoNext,
    required this.canGoPrevious,
    required this.isLoading,
    this.onPrevious,
    this.onNext,
    this.onComplete,
    required this.primaryColor,
    required this.isDarkMode,
    this.nextButtonText,
    this.completeButtonText,
  });

  bool get isLastStep => currentStep >= totalSteps;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, -3),
            blurRadius: 6,
          )
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Previous button
            if (canGoPrevious && currentStep > 1)
              Expanded(
                flex: 1,
                child: _buildPreviousButton(),
              )
            else
              const SizedBox(width: 8),
            
            SizedBox(width: canGoPrevious && currentStep > 1 ? 16 : 0),
            
            // Next/Complete button
            Expanded(
              flex: 2,
              child: _buildNextButton(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviousButton() {
    return ElevatedButton.icon(
      onPressed: isLoading ? null : onPrevious,
      style: ElevatedButton.styleFrom(
        backgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[200],
        foregroundColor: isDarkMode ? Colors.white : Colors.black87,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 0,
      ),
      icon: const Icon(Icons.arrow_back, size: 18),
      label: const Text(
        'Trước',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildNextButton() {
    final buttonText = isLastStep 
        ? (completeButtonText ?? 'Hoàn thành')
        : (nextButtonText ?? 'Tiếp theo');
    
    final buttonIcon = isLastStep 
        ? Icons.check_circle
        : Icons.arrow_forward;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: canGoNext && !isLoading
            ? [
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
        gradient: canGoNext && !isLoading
            ? LinearGradient(
                colors: [
                  primaryColor,
                  primaryColor.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
      ),
      child: ElevatedButton(
        onPressed: isLoading || !canGoNext 
            ? null 
            : isLastStep 
                ? onComplete 
                : onNext,
        style: ElevatedButton.styleFrom(
          backgroundColor: canGoNext && !isLoading 
              ? Colors.transparent 
              : Colors.grey.withValues(alpha: 0.3),
          disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.5,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    buttonText,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    buttonIcon,
                    size: 18,
                    color: Colors.white,
                  ),
                ],
              ),
      ),
    );
  }
}
