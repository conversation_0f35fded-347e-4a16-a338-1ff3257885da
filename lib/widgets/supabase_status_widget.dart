import 'package:flutter/material.dart';
import '../services/simple_supabase_integration_service.dart';
import '../utils/date_formatter.dart';

class SupabaseStatusWidget extends StatefulWidget {
  const SupabaseStatusWidget({super.key});

  @override
  State<SupabaseStatusWidget> createState() => _SupabaseStatusWidgetState();
}

class _SupabaseStatusWidgetState extends State<SupabaseStatusWidget> {
  final SimpleSupabaseIntegrationService _integrationService = SimpleSupabaseIntegrationService.instance;
  Map<String, dynamic>? _healthStatus;
  Map<String, dynamic>? _usageStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatus();
  }

  Future<void> _loadStatus() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    try {
      print('🔄 SupabaseStatusWidget: Bắt đầu tải trạng thái...');

      // Tải health status với timeout và logging
      print('🔄 SupabaseStatusWidget: Đang tải health status...');
      Map<String, dynamic>? healthResult;
      try {
        healthResult = await _integrationService.checkConnection()
            .timeout(const Duration(seconds: 10));
        print('✅ SupabaseStatusWidget: Health status tải thành công');
      } catch (e) {
        print('❌ SupabaseStatusWidget: Lỗi tải health status: $e');
        healthResult = {
          'success': false,
          'message': 'Lỗi tải health status: $e',
          'health': {},
        };
      }

      // Tải usage stats với timeout và logging
      print('🔄 SupabaseStatusWidget: Đang tải usage stats...');
      Map<String, dynamic>? statsResult;
      try {
        statsResult = await _integrationService.getStatus()
            .timeout(const Duration(seconds: 10));
        print('✅ SupabaseStatusWidget: Usage stats tải thành công');
      } catch (e) {
        print('❌ SupabaseStatusWidget: Lỗi tải usage stats: $e');
        statsResult = {
          'success': false,
          'message': 'Lỗi tải usage stats: $e',
          'stats': {},
        };
      }

      if (mounted) {
        setState(() {
          _healthStatus = healthResult;
          _usageStats = statsResult;
          _isLoading = false;
        });
      }

      print('✅ SupabaseStatusWidget: Hoàn thành tải trạng thái');

      // Kiểm tra và hiển thị cảnh báo nếu có lỗi
      if (_healthStatus != null && !_healthStatus!['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Cảnh báo health: ${_healthStatus!['message']}'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }

      if (_usageStats != null && !_usageStats!['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Cảnh báo stats: ${_usageStats!['message']}'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }

    } catch (e) {
      print('❌ SupabaseStatusWidget: Lỗi tổng thể: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Đặt giá trị mặc định để tránh crash
          _healthStatus = {
            'success': false,
            'message': 'Không thể tải trạng thái: $e',
            'health': {},
          };
          _usageStats = {
            'success': false,
            'message': 'Không thể tải thống kê: $e',
            'stats': {},
          };
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi tải trạng thái: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _syncData() async {
    try {
      final result = await _integrationService.checkConnection();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Đồng bộ hoàn tất'),
            backgroundColor: result['success'] ? Colors.green : Colors.red,
          ),
        );
      }
      
      if (result['success']) {
        _loadStatus(); // Reload status after sync
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đồng bộ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearLocalData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận'),
        content: const Text('Bạn có chắc muốn xóa tất cả dữ liệu local? Hành động này không thể hoàn tác.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Xóa', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final result = await _integrationService.clearAllLocalData();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Xóa dữ liệu hoàn tất'),
              backgroundColor: result['success'] ? Colors.green : Colors.red,
            ),
          );
        }
        
        if (result['success']) {
          _loadStatus(); // Reload status after clearing
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi xóa dữ liệu: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Trạng thái Supabase',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadStatus,
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              _buildHealthSection(),
              const SizedBox(height: 16),
              _buildStatsSection(),
              const SizedBox(height: 16),
              _buildActionsSection(),
            ]
          ],
        ),
      ),
    );
  }

  Widget _buildHealthSection() {
    if (_healthStatus == null) {
      return const ListTile(
        leading: CircularProgressIndicator(),
        title: Text('Đang tải trạng thái...'),
      );
    }

    if (!_healthStatus!['success']) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Tình trạng dịch vụ:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.error, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _healthStatus!['message'] ?? 'Không thể tải thông tin tình trạng dịch vụ',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    final health = _healthStatus!['health'] as Map<String, dynamic>? ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tình trạng dịch vụ:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // Supabase connection
        _buildStatusItem(
          'Kết nối Supabase',
          health['supabase']?['success'] ?? false,
          Icons.cloud,
        ),

        // Auth status
        _buildStatusItem(
          'Đăng nhập',
          health['auth']?['isLoggedIn'] ?? false,
          Icons.person,
        ),

        // Sync capability
        _buildStatusItem(
          'Đồng bộ hóa',
          health['sync']?['canSync'] ?? false,
          Icons.sync,
        ),

        // Storage
        _buildStatusItem(
          'Lưu trữ',
          health['storage']?['isHealthy'] ?? false,
          Icons.storage,
        ),

        // Cache
        _buildStatusItem(
          'Cache hình ảnh',
          health['cache']?['isInitialized'] ?? false,
          Icons.image,
        ),
      ],
    );
  }

  Widget _buildStatusItem(String title, bool isHealthy, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: isHealthy ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Text(title),
          const Spacer(),
          Icon(
            isHealthy ? Icons.check_circle : Icons.error,
            size: 16,
            color: isHealthy ? Colors.green : Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    if (_usageStats == null) {
      return const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thống kê sử dụng:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Đang tải thống kê...'),
        ],
      );
    }

    if (!_usageStats!['success']) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Thống kê sử dụng:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _usageStats!['message'] ?? 'Không thể tải thống kê',
                    style: const TextStyle(color: Colors.orange, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    final stats = _usageStats!['stats'] as Map<String, dynamic>? ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thống kê sử dụng:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        _buildStatItem('Món ăn yêu thích', stats['favoritesCount'] ?? 0),
        _buildStatItem('Công thức nấu ăn', stats['recipesCount'] ?? 0),
        _buildStatItem('Menu tuần', stats['weekMenusCount'] ?? 0),

        if (stats['cache'] != null && stats['cache'] is Map) ...[
          _buildStatItem('Hình ảnh cache', stats['cache']['totalFiles'] ?? 0),
          _buildStatItem('Dung lượng cache', '${((stats['cache']['totalSize'] ?? 0) / 1024 / 1024).toStringAsFixed(1)} MB'),
        ],

        if (stats['lastSync'] != null)
          _buildStatItem('Lần đồng bộ cuối', _formatDateTime(stats['lastSync'])),
      ],
    );
  }

  Widget _buildStatItem(String title, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(title),
          const Spacer(),
          Text(
            value.toString(),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Text(
          'Hành động:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _syncData,
                icon: const Icon(Icons.sync),
                label: const Text('Đồng bộ'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _clearLocalData,
                icon: const Icon(Icons.delete),
                label: const Text('Xóa local'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatDateTime(dynamic dateTimeValue) {
    if (dateTimeValue == null) return 'Chưa có';

    try {
      DateTime dateTime;

      if (dateTimeValue is DateTime) {
        dateTime = dateTimeValue;
      } else if (dateTimeValue is String) {
        dateTime = DateTime.parse(dateTimeValue);
      } else {
        return 'Không xác định';
      }

      return DateFormatter.formatRelativeTime(context, dateTime);
    } catch (e) {
      return 'Không xác định';
    }
  }
}