import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/supabase_auth_service.dart';
import '../screens/user_profile_screen.dart';

class UserInfoWidget extends StatefulWidget {
  const UserInfoWidget({super.key});

  @override
  State<UserInfoWidget> createState() => _UserInfoWidgetState();
}

class _UserInfoWidgetState extends State<UserInfoWidget> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  
  Map<String, dynamic>? _userInfo;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Lấy thông tin từ Supabase Auth
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        _userInfo = {
          'uid': currentUser.id,
          'email': currentUser.email,
          'displayName': currentUser.userMetadata?['full_name'],
          'photoURL': currentUser.userMetadata?['avatar_url'],
        };
      }

      // Lấy thông tin từ SharedPreferences để backup
      final prefs = await SharedPreferences.getInstance();
      final savedDisplayName = prefs.getString('display_name');
      final savedPhotoUrl = prefs.getString('photo_url');
      final savedEmail = prefs.getString('user_email');
      
      if (_userInfo != null) {
        // Sử dụng thông tin từ SharedPreferences nếu không có từ Supabase
        _userInfo!['displayName'] ??= savedDisplayName;
        _userInfo!['photoURL'] ??= savedPhotoUrl;
        _userInfo!['email'] ??= savedEmail;
      } else if (savedEmail != null) {
        // Tạo userInfo từ SharedPreferences nếu không có từ Supabase
        _userInfo = {
          'email': savedEmail,
          'displayName': savedDisplayName,
          'photoURL': savedPhotoUrl,
        };
      }

    } catch (e) {
      print('Lỗi khi tải thông tin người dùng: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildUserAvatar() {
    final photoUrl = _userInfo?['photoURL'];
    final displayName = _userInfo?['displayName'] ?? 'Người dùng';

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipOval(
        child: photoUrl != null && photoUrl.isNotEmpty
            ? Image.network(
                photoUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar(displayName);
                },
              )
            : _buildDefaultAvatar(displayName),
      ),
    );
  }

  Widget _buildDefaultAvatar(String displayName) {
    // Tạo avatar từ chữ cái đầu của tên
    String initials = '';
    if (displayName.isNotEmpty) {
      final words = displayName.trim().split(' ');
      if (words.length >= 2) {
        initials = '${words.first[0]}${words.last[0]}'.toUpperCase();
      } else {
        initials = displayName[0].toUpperCase();
      }
    } else {
      initials = 'U';
    }

    return Container(
      color: Colors.white.withValues(alpha: 0.2),
      child: Center(
        child: Text(
          initials,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _navigateToProfile() {
    Navigator.of(context).pushNamed(UserProfileScreen.routeName);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
            ],
          ),
        ),
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    if (_userInfo == null) {
      return Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            'Không thể tải thông tin người dùng',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    final displayName = _userInfo!['displayName'] ?? 'Người dùng';
    final email = _userInfo!['email'] ?? 'Không có email';

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode
              ? [
                  Theme.of(context).primaryColor.withValues(alpha: 0.9),
                  Theme.of(context).primaryColor.withValues(alpha: 0.7),
                ]
              : [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _navigateToProfile,
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
              child: Row(
              children: [
                _buildUserAvatar(),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        email,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text(
                            'Xem thông tin chi tiết',
                            style: TextStyle(
                              color: Colors.white60,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white60,
                            size: 12,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            ),
          ),
        ),
      ),
    );
  }
}
