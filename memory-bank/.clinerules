# Project Intelligence Rules - Nấu gì đây?

## Code Conventions & Patterns

### Naming Conventions
- **Files**: snake_case (e.g., `dish_service.dart`, `meal_plan_provider.dart`)
- **Classes**: PascalCase (e.g., `DishService`, `MealPlanProvider`)
- **Variables & Functions**: camelCase (e.g., `getUserDishes()`, `dishList`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_IMAGE_SIZE`)
- **Private members**: Prefix with underscore (e.g., `_supabase`, `_initializeServices()`)

### File Organization Patterns
```
lib/
├── data/           # Static data & repositories
├── models/         # Data models only
├── providers/      # State management (Provider pattern)
├── screens/        # Full-screen UI components
├── services/       # Business logic & API calls
├── utils/          # Helper functions & utilities
├── widgets/        # Reusable UI components
└── main.dart       # App entry point
```

### Service Layer Architecture
- **Rule**: All external API calls go through services
- **Pattern**: One service per domain (DishService, AuthService, etc.)
- **Singleton**: Services that manage global state use singleton pattern
- **Dependency**: Services can depend on other services, but avoid circular dependencies

### Error Handling Strategy
```dart
// Always use try-catch for async operations
try {
  final result = await _supabase.from('table').select();
  return result.map((json) => Model.fromJson(json)).toList();
} on PostgrestException catch (e) {
  throw Exception('Database error: ${e.message}');
} catch (e) {
  throw Exception('Network error: $e');
}
```

### State Management Rules
- **Provider Pattern**: Use for all state management
- **ChangeNotifier**: Extend for complex state objects
- **Consumer vs Selector**: Use Selector when only part of state changes
- **Context**: Always check if widget is still mounted before calling notifyListeners()

## Supabase Integration Patterns

### Database Naming
- **Tables**: snake_case, plural (e.g., `user_dishes`, `meal_plans`)
- **Columns**: snake_case (e.g., `created_at`, `user_id`)
- **Foreign Keys**: Always end with `_id` (e.g., `user_id`, `dish_id`)

### Row Level Security (RLS)
- **Rule**: ALWAYS enable RLS on user data tables
- **Pattern**: Use `auth.uid() = user_id` for user-specific data
- **Policies**: Create separate policies for SELECT, INSERT, UPDATE, DELETE

### API Call Patterns
```dart
// Standard CRUD pattern
class SomeService {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  // GET with user filtering
  Future<List<Model>> getUserItems() async {
    final response = await _supabase
        .from('table_name')
        .select()
        .eq('user_id', _supabase.auth.currentUser!.id);
    return response.map((json) => Model.fromJson(json)).toList();
  }
  
  // POST with user_id injection
  Future<Model> createItem(Model item) async {
    final data = item.toJson();
    data['user_id'] = _supabase.auth.currentUser!.id;
    
    final response = await _supabase
        .from('table_name')
        .insert(data)
        .select()
        .single();
    return Model.fromJson(response);
  }
}
```

### Storage Patterns
- **Bucket Structure**: `user_id/category/filename`
- **File Naming**: Use UUID for uniqueness
- **Image Optimization**: Always compress before upload
- **Public URLs**: Cache URLs locally to avoid repeated API calls

## UI/UX Patterns

### Screen Structure
```dart
class SomeScreen extends StatefulWidget {
  @override
  _SomeScreenState createState() => _SomeScreenState();
}

class _SomeScreenState extends State<SomeScreen> {
  // Always initialize loading state
  bool _isLoading = true;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _loadData();
  }
  
  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });
      
      // Load data
      
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) return LoadingWidget();
    if (_error != null) return ErrorWidget(_error!);
    
    return ActualContent();
  }
}
```

### Widget Patterns
- **Stateless vs Stateful**: Prefer StatelessWidget when possible
- **Key Usage**: Always provide keys for list items
- **Context Usage**: Pass context down rather than accessing globally
- **Responsive Design**: Use MediaQuery for screen size adaptations

### Navigation Patterns
```dart
// Always use named routes for main screens
Navigator.pushNamed(context, '/dish-detail', arguments: dish);

// Use push for temporary screens
Navigator.push(context, MaterialPageRoute(
  builder: (context) => SomeScreen(),
));

// Always handle back button for forms
WillPopScope(
  onWillPop: () async {
    if (_hasUnsavedChanges) {
      return await _showDiscardDialog();
    }
    return true;
  },
  child: YourForm(),
)
```

## Image Handling Rules

### Image Processing Pipeline
1. **Pick Image**: Use image_picker
2. **Validate**: Check size and format
3. **Compress**: Always compress before upload
4. **Upload**: To Supabase Storage
5. **Cache**: Store local copy for offline access

### Image Optimization
```dart
// Standard compression settings
final compressed = img.encodeJpg(image, quality: 85);

// Resize if too large
if (image.width > 1024 || image.height > 1024) {
  image = img.copyResize(image, width: 1024, height: 1024);
}
```

### Caching Strategy
- **Memory Cache**: For frequently accessed images
- **Disk Cache**: For offline access
- **Cache Expiry**: 7 days for user images, 30 days for default images

## Data Synchronization Rules

### Sync Strategy
1. **Local First**: Always save locally first
2. **Background Sync**: Upload to server in background
3. **Conflict Resolution**: Last-write-wins with timestamp
4. **Retry Logic**: Exponential backoff for failed syncs

### Offline Support
```dart
// Always check connectivity before sync operations
if (await _hasInternetConnection()) {
  await _syncToServer();
} else {
  await _saveToLocalQueue();
}
```

### Data Consistency
- **Optimistic Updates**: Update UI immediately, sync later
- **Rollback**: Revert changes if sync fails
- **Validation**: Validate data before and after sync

## Testing Patterns

### Unit Test Structure
```dart
void main() {
  group('ServiceName', () {
    late ServiceName service;
    
    setUp(() {
      service = ServiceName();
    });
    
    tearDown(() {
      // Cleanup
    });
    
    test('should do something', () async {
      // Arrange
      final input = SomeInput();
      
      // Act
      final result = await service.doSomething(input);
      
      // Assert
      expect(result, equals(expectedResult));
    });
  });
}
```

### Widget Test Patterns
- **Golden Tests**: For visual regression testing
- **Interaction Tests**: Test user interactions
- **State Tests**: Verify state changes

## Performance Optimization Rules

### Memory Management
- **Dispose Controllers**: Always dispose TextEditingController, AnimationController
- **Cancel Subscriptions**: Cancel StreamSubscription in dispose()
- **Image Memory**: Use cacheWidth/cacheHeight for large images

### Build Optimization
```dart
// Use const constructors when possible
const Text('Static text')

// Extract widgets to avoid rebuilds
class _StaticWidget extends StatelessWidget {
  const _StaticWidget();
  
  @override
  Widget build(BuildContext context) {
    return Container(/* static content */);
  }
}
```

### List Performance
- **ListView.builder**: For large lists
- **Pagination**: Load data in chunks
- **Keys**: Use unique keys for list items

## Security Best Practices

### Authentication
- **Token Storage**: Use secure storage for tokens
- **Session Management**: Handle token expiry gracefully
- **Logout**: Clear all local data on logout

### Data Protection
- **Input Validation**: Validate all user inputs
- **SQL Injection**: Use parameterized queries (Supabase handles this)
- **XSS Prevention**: Sanitize user-generated content

### API Security
- **Rate Limiting**: Implement client-side rate limiting
- **Error Messages**: Don't expose sensitive information in errors
- **Logging**: Don't log sensitive data

## Deployment & Release Rules

### Version Management
- **Semantic Versioning**: MAJOR.MINOR.PATCH+BUILD
- **Build Numbers**: Increment for each release
- **Changelog**: Document all changes

### Release Process
1. **Testing**: Run all tests
2. **Code Review**: Review all changes
3. **Build**: Create release builds
4. **Deploy**: Deploy to app stores
5. **Monitor**: Monitor for crashes and issues

### Environment Management
- **Development**: Local Supabase instance
- **Staging**: Staging Supabase project
- **Production**: Production Supabase project

## Debugging & Monitoring

### Logging Strategy
```dart
// Use different log levels
print('DEBUG: $message');           // Development only
debugPrint('INFO: $message');       // General information
FlutterError.reportError(details);  // Errors
```

### Error Tracking
- **Crash Reports**: Implement crash reporting
- **User Feedback**: Collect user feedback
- **Performance Monitoring**: Monitor app performance

### Debug Tools
- **Flutter Inspector**: For widget debugging
- **Network Inspector**: For API debugging
- **Performance Overlay**: For performance debugging

## Migration & Maintenance

### Database Migrations
- **Versioning**: Version all schema changes
- **Backward Compatibility**: Maintain compatibility during transitions
- **Data Validation**: Validate data after migrations

### Code Maintenance
- **Refactoring**: Regular code cleanup
- **Dependencies**: Keep dependencies updated
- **Documentation**: Update documentation with changes

### Legacy Support
- **Graceful Degradation**: Support older app versions
- **Feature Flags**: Use feature flags for gradual rollouts
- **Sunset Strategy**: Plan for deprecating old features

---

## Project-Specific Learnings

### What Works Well
1. **Provider + Services**: Clean separation of concerns
2. **Supabase RLS**: Excellent security with minimal code
3. **Material Design 3**: Consistent, modern UI
4. **Image Compression**: Essential for mobile performance
5. **Offline-First**: Better user experience

### Common Pitfalls to Avoid
1. **Sync Without Validation**: Always validate before syncing
2. **Large Images**: Always compress images before upload
3. **Memory Leaks**: Dispose controllers and cancel subscriptions
4. **Network Assumptions**: Always handle offline scenarios
5. **Generic Errors**: Provide specific, actionable error messages

### Performance Lessons
1. **Image Caching**: Critical for smooth scrolling
2. **Lazy Loading**: Essential for large datasets
3. **Background Sync**: Don't block UI for sync operations
4. **Memory Management**: Monitor memory usage on low-end devices

### User Experience Insights
1. **Loading States**: Always show loading indicators
2. **Error Recovery**: Provide ways to retry failed operations
3. **Offline Feedback**: Clearly indicate offline status
4. **Form Validation**: Validate inputs in real-time
5. **Confirmation Dialogs**: Confirm destructive actions

---

*Last Updated: December 2024*
*Version: 1.0*
*Maintainer: Development Team*