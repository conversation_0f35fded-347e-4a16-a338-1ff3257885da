# Product Context - Nấu gì đây?

## Vấn đề cần giải quyết

### Vấn đề chính
**"Hôm nay nấu gì?"** - Câu hỏi hằng ngày của hàng triệu người Việt Nam.

### <PERSON><PERSON><PERSON> thách thức cụ thể:
1. **Thiếu ý tưởng món ăn**: Người dùng thường bí ý tưởng cho bữa tối hằng ngày
2. **Không cá nhân hóa**: Các ứng dụng hiện tại chỉ gợi ý món ăn chung chung, không phù hợp với sở thích cá nhân
3. **Mất kết nối với món ăn đã nấu**: Người dùng không có cách lưu trữ và tái sử dụng các món ăn đã từng nấu thành công
4. **Thiế<PERSON> tính liên tục**: <PERSON>hông có hệ thống lập kế hoạch thực đơn dài hạn
5. **<PERSON><PERSON> thuộc vào dữ liệu mặc định**: <PERSON><PERSON><PERSON>ng dụng hiện tại chỉ dựa vào database có sẵn, không học hỏi từ thói quen người dùng

## Giải pháp của chúng ta

### Cách tiếp cận độc đáo:
**"Biến người dùng thành đầu bếp của chính mình"**

1. **Cá nhân hóa thực sự**: 
   - Người dùng tự tạo và quản lý món ăn riêng
   - Hệ thống học hỏi từ sở thích cá nhân
   - Gợi ý dựa trên lịch sử nấu ăn

2. **Tích hợp liền mạch**:
   - Món ăn cá nhân được đưa vào gợi ý tự động
   - Kết hợp món ăn mặc định với món ăn tự tạo
   - Đồng bộ giữa các thiết bị

3. **Lập kế hoạch thông minh**:
   - Gợi ý thực đơn tuần tự động
   - Cân bằng dinh dưỡng và đa dạng món ăn
   - Lưu trữ lịch sử để tránh lặp lại

## Mục tiêu trải nghiệm người dùng

### Journey lý tưởng:

#### Giai đoạn 1: Khám phá (Tuần đầu)
- Người dùng đăng ký và khám phá món ăn mặc định
- Thử nghiệm tính năng gợi ý thực đơn
- Bắt đầu thêm món ăn đầu tiên

#### Giai đoạn 2: Xây dựng (Tháng đầu)
- Tích cực thêm món ăn đã nấu
- Chụp ảnh và ghi chú công thức
- Thấy món ăn riêng xuất hiện trong gợi ý

#### Giai đoạn 3: Thành thạo (Sau 1 tháng)
- Có bộ sưu tập món ăn cá nhân phong phú
- Gợi ý thực đơn phù hợp với sở thích
- Chia sẻ và lưu trữ công thức gia đình

### Cảm xúc mục tiêu:
- **Tự tin**: "Tôi biết mình sẽ nấu gì hôm nay"
- **Tự hào**: "Đây là món ăn tôi đã tạo ra"
- **Tiện lợi**: "Ứng dụng hiểu tôi và giúp tôi"
- **Kết nối**: "Tôi có thể lưu giữ công thức gia đình"

## Đối tượng người dùng chính

### Primary Persona: "Chị Lan - Mẹ bận rộn"
- **Tuổi**: 28-45
- **Nghề nghiệp**: Nhân viên văn phòng, có gia đình
- **Thách thức**: Thiếu thời gian và ý tưởng cho bữa tối
- **Mục tiêu**: Nấu ăn ngon, đa dạng cho gia đình
- **Hành vi**: Thường tìm kiếm công thức trên mạng, lưu ảnh món ăn

### Secondary Persona: "Anh Minh - Người trẻ độc thân"
- **Tuổi**: 22-35
- **Nghề nghiệp**: Sinh viên, nhân viên mới đi làm
- **Thách thức**: Mới học nấu ăn, muốn cải thiện kỹ năng
- **Mục tiêu**: Học nấu ăn, tiết kiệm chi phí
- **Hành vi**: Thích thử nghiệm, chia sẻ trên mạng xã hội

## Metrics thành công

### Engagement Metrics:
- **Retention**: >60% người dùng quay lại sau 7 ngày
- **Usage**: Trung bình 3-4 lần mở app/tuần
- **Content Creation**: >50% người dùng thêm ít nhất 1 món ăn/tháng

### Value Metrics:
- **Personalization**: >70% gợi ý thực đơn bao gồm món ăn cá nhân
- **Satisfaction**: Rating trung bình >4.2/5
- **Problem Solving**: >80% người dùng báo cáo giảm thời gian suy nghĩ "nấu gì"

## Competitive Advantage

### Điểm khác biệt:
1. **Tập trung vào cá nhân hóa**: Không chỉ gợi ý mà còn học hỏi
2. **Tích hợp liền mạch**: Món ăn tự tạo trở thành phần của hệ thống
3. **Đồng bộ đám mây**: Dữ liệu an toàn, truy cập mọi lúc
4. **UI/UX Việt Nam**: Thiết kế phù hợp văn hóa và thói quen địa phương
5. **Offline-first**: Hoạt động tốt ngay cả khi mạng yếu