# Project Brief - Nấu gì đây?

## Tổng quan Dự án

**Tên dự án**: Nấu gì đây? - Ứng dụng quản lý món ăn cá nhân

**Mục tiêu cốt lõi**: Tạo ra một ứng dụng Flutter giúp người dùng quản lý và cá nhân hóa các món ăn đã nấu, đồng thời tích hợp chúng vào gợi ý thực đơn hằng tuần.

## Yêu cầu chính

### 1. Quản lý món ăn cá nhân
- Thêm món ăn với thông tin chi tiết (tê<PERSON>, nguyên liệu, cách nấu, ảnh)
- Chỉnh sửa và xóa món ăn dễ dàng
- Tích hợp món ăn do người dùng tạo vào danh sách gợi ý

### 2. <PERSON><PERSON> thống xác thực và đồng bộ
- <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> ký, kh<PERSON><PERSON> phục mật khẩu với Supabase
- <PERSON><PERSON><PERSON> bộ hóa dữ liệu giữa các thiết bị
- Lưu trữ đám mây an toàn

### 3. Lập kế hoạch thực đơn
- Gợi ý thực đơn hằng tuần tự động
- Tích hợp món ăn cá nhân vào gợi ý
- Quản lý lịch sử bữa ăn

### 4. Trải nghiệm người dùng
- Giao diện Material You responsive
- Hỗ trợ chế độ sáng/tối
- Hoạt động trên nhiều kích thước màn hình

## Phạm vi Dự án

### Trong phạm vi:
- Ứng dụng di động Flutter đa nền tảng
- Tích hợp Supabase cho backend
- Quản lý món ăn và thực đơn
- Xác thực và đồng bộ người dùng
- Lưu trữ và quản lý hình ảnh

### Ngoài phạm vi:
- Ứng dụng web độc lập
- Tích hợp mạng xã hội
- Chia sẻ công khai món ăn
- Hệ thống đánh giá/bình luận

## Thành công được định nghĩa như thế nào?

1. **Tính cá nhân hóa cao**: Người dùng có thể dễ dàng thêm và quản lý món ăn riêng
2. **Đồng bộ đáng tin cậy**: Dữ liệu được lưu trữ và đồng bộ an toàn
3. **Gợi ý thông minh**: Hệ thống gợi ý thực đơn kết hợp món ăn mặc định và cá nhân
4. **Trải nghiệm mượt mà**: Giao diện thân thiện, phản hồi nhanh
5. **Độ tin cậy**: Ứng dụng hoạt động ổn định trên nhiều thiết bị

## Các ràng buộc quan trọng

- **Công nghệ**: Flutter/Dart >=3.0.0
- **Backend**: Supabase (bắt buộc)
- **Thiết kế**: Material Design 3
- **Hiệu năng**: Thời gian tải < 3 giây
- **Bảo mật**: Row Level Security cho tất cả dữ liệu người dùng