# System Patterns & Architecture - Nấu gì đây?

## Kiến trúc Tổng thể

### Architectural Pattern: Clean Architecture + MVVM

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Screens   │  │   Widgets   │  │     Providers       │ │
│  │             │  │             │  │  (State Management) │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    Services                             │ │
│  │  • AuthService        • DishService                     │ │
│  │  • SyncService        • MealPlanService                 │ │
│  │  • StorageService     • WeekMenuService                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Models    │  │ Repositories│  │    Data Sources     │ │
│  │             │  │             │  │ • Supabase          │ │
│  │             │  │             │  │ • SharedPreferences │ │
│  │             │  │             │  │ • Local Storage     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Core Design Patterns

### 1. Singleton Pattern
**Sử dụng cho**: Services quản lý trạng thái toàn cục

```dart
class SupabaseIntegrationService {
  static final SupabaseIntegrationService _instance = 
      SupabaseIntegrationService._internal();
  factory SupabaseIntegrationService() => _instance;
  SupabaseIntegrationService._internal();
}
```

### 2. Local-First Data Management Pattern
**Sử dụng cho**: Chức năng không cần đồng bộ với server

```dart
// Pattern cho meal eaten confirmation - chỉ local storage
class MealHistoryProvider {
  final List<String> _eatenMealIds = [];
  
  Future<void> addMealToHistory(String date, String mealType, Dish dish) async {
    // Chỉ lưu local, không đồng bộ với Supabase
    final mealKey = _createEatenMealKey(date, mealType, dish.id);
    if (!_eatenMealIds.contains(mealKey)) {
      _eatenMealIds.add(mealKey);
      await _saveEatenMeals(); // Local storage only
    }
  }
}
```

**Áp dụng cho**:
- `SupabaseIntegrationService`
- `AuthService`
- `ImageCacheService`
- `StorageService`

### 2. Provider Pattern (State Management)
**Sử dụng cho**: Quản lý trạng thái ứng dụng

```dart
class MenuProvider extends ChangeNotifier {
  List<Dish> _dishes = [];
  
  void updateDishes(List<Dish> dishes) {
    _dishes = dishes;
    notifyListeners();
  }
}
```

**Providers chính**:
- `MenuProvider`: Quản lý danh sách món ăn
- `MealPlanProvider`: Quản lý kế hoạch bữa ăn
- `MealHistoryProvider`: Quản lý lịch sử bữa ăn
- `ThemeProvider`: Quản lý theme ứng dụng

### 3. Repository Pattern
**Sử dụng cho**: Trừu tượng hóa data access

```dart
class DishRepository {
  final SupabaseClient _supabase;
  final SharedPreferences _prefs;
  
  Future<List<Dish>> getDishes() async {
    // Kiểm tra cache local trước
    // Nếu không có, fetch từ Supabase
    // Cache kết quả
  }
}
```

### 4. Service Layer Pattern
**Sử dụng cho**: Tách biệt business logic

**Cấu trúc Services**:
```
services/
├── auth_service.dart           # Xác thực người dùng
├── dish_service.dart           # Quản lý món ăn
├── meal_plan_service.dart      # Lập kế hoạch bữa ăn
├── sync_service.dart           # Đồng bộ dữ liệu
├── storage_service.dart        # Lưu trữ file
├── supabase_integration_service.dart  # Tích hợp Supabase
└── week_menu_service.dart      # Quản lý menu tuần
```

### 5. Factory Pattern
**Sử dụng cho**: Tạo objects phức tạp

```dart
class Dish {
  factory Dish.fromJson(Map<String, dynamic> json) {
    return Dish(
      id: json['id'],
      name: json['name'],
      // ... mapping logic
    );
  }
  
  factory Dish.fromSupabase(Map<String, dynamic> data) {
    // Specific mapping for Supabase data
  }
}
```

## Data Flow Architecture

### 1. Offline-First Pattern
```
User Action → Provider → Service → Repository → Cache Check → 
                                                     ↓
                                              Local Storage
                                                     ↓
                                              Background Sync
                                                     ↓
                                                Supabase
```

### 2. Sync Strategy
**Two-way Sync Pattern**:
1. **Upload**: Local changes → Supabase
2. **Download**: Supabase changes → Local
3. **Conflict Resolution**: Last-write-wins với timestamp

```dart
class SyncService {
  Future<void> syncUserDishes() async {
    // 1. Upload local changes
    await _uploadPendingChanges();
    
    // 2. Download remote changes
    await _downloadRemoteChanges();
    
    // 3. Resolve conflicts
    await _resolveConflicts();
  }
}
```

## Security Patterns

### 1. Row Level Security (RLS)
**Tất cả bảng Supabase đều có RLS**:
```sql
-- Policy cho user_dishes
CREATE POLICY "Users can only see their own dishes" 
ON user_dishes FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert their own dishes" 
ON user_dishes FOR INSERT 
WITH CHECK (auth.uid() = user_id);
```

### 2. Authentication Flow
```
App Start → Check Auth State → 
    ↓                    ↓
Authenticated      Not Authenticated
    ↓                    ↓
Main App          Login Screen
    ↓                    ↓
Auto Sync         Manual Login
```

## Performance Patterns

### 1. Lazy Loading
```dart
class DishService {
  List<Dish>? _cachedDishes;
  
  Future<List<Dish>> getDishes() async {
    if (_cachedDishes != null) {
      return _cachedDishes!;
    }
    
    _cachedDishes = await _loadDishes();
    return _cachedDishes!;
  }
}
```

### 2. Image Caching
```dart
class ImageCacheService {
  final Map<String, String> _cache = {};
  
  Future<String> getCachedImagePath(String url) async {
    if (_cache.containsKey(url)) {
      return _cache[url]!;
    }
    
    final path = await _downloadAndCache(url);
    _cache[url] = path;
    return path;
  }
}
```

### 3. Pagination Pattern
```dart
class DishRepository {
  Future<List<Dish>> getDishes({
    int page = 0,
    int limit = 20,
  }) async {
    return await _supabase
        .from('dishes')
        .select()
        .range(page * limit, (page + 1) * limit - 1);
  }
}
```

## Error Handling Patterns

### 1. Graceful Degradation
```dart
class DishService {
  Future<List<Dish>> getDishes() async {
    try {
      // Try Supabase first
      return await _getFromSupabase();
    } catch (e) {
      // Fallback to local cache
      return await _getFromLocal();
    }
  }
}
```

### 2. Retry Pattern
```dart
class NetworkService {
  Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (e) {
        if (i == maxRetries - 1) rethrow;
        await Future.delayed(Duration(seconds: 2 << i));
      }
    }
    throw Exception('Max retries exceeded');
  }
}
```

## Migration Patterns

### 1. Data Migration Strategy
```dart
class MigrationHelper {
  Future<void> migrateToSupabase() async {
    // 1. Check migration status
    if (await _isMigrationComplete()) return;
    
    // 2. Load local data
    final localDishes = await _loadLocalDishes();
    
    // 3. Upload to Supabase
    await _uploadDishes(localDishes);
    
    // 4. Mark migration complete
    await _markMigrationComplete();
  }
}
```

## Component Communication

### 1. Event-Driven Architecture
```dart
// Provider notifies widgets
Provider.of<MenuProvider>(context, listen: true)

// Service notifies providers
class DishService {
  final StreamController<List<Dish>> _dishesController = 
      StreamController.broadcast();
  
  Stream<List<Dish>> get dishesStream => _dishesController.stream;
}
```

### 2. Dependency Injection
```dart
// Services inject dependencies
class DishService {
  final SupabaseClient _supabase;
  final SharedPreferences _prefs;
  
  DishService(this._supabase, this._prefs);
}
```

## Testing Patterns

### 1. Mock Pattern
```dart
class MockDishService implements DishService {
  @override
  Future<List<Dish>> getDishes() async {
    return [
      Dish(id: 'test1', name: 'Test Dish'),
    ];
  }
}
```

### 2. Widget Testing
```dart
testWidgets('DishCard displays dish information', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: DishCard(dish: testDish),
    ),
  );
  
  expect(find.text('Test Dish'), findsOneWidget);
});
```