# Tech Context - Nấu gì đây?

## Technology Stack

### Frontend Framework
**Flutter/Dart >=3.0.0**
- **L<PERSON> do chọn**: Cross-platform, hiệu năng cao, ecosystem phong phú
- **Version**: SDK >=3.0.0 <4.0.0
- **Build version**: 1.0.0+5

### Backend & Database
**Supabase** (PostgreSQL + Real-time + Auth + Storage)
- **Database**: PostgreSQL với Row Level Security
- **Authentication**: Supabase Auth (Email/Password, OAuth)
- **Storage**: Supabase Storage cho hình ảnh
- **Real-time**: WebSocket subscriptions
- **API**: Auto-generated REST API

### State Management
**Provider Pattern**
- **Package**: `provider: ^6.0.5`
- **L<PERSON> do chọn**: Đơn gi<PERSON>n, hi<PERSON><PERSON> qu<PERSON>, t<PERSON>ch hợ<PERSON> tốt với Flutter
- **Alternative considered**: Bloc, Riverpod

## Core Dependencies

### Essential Packages
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.0.5
  
  # Backend Integration
  supabase_flutter: ^2.5.6
  
  # Local Storage
  shared_preferences: ^2.5.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.2
  
  # Image Handling
  image_picker: ^1.0.4
  image: ^4.1.3
  
  # UI/UX
  google_fonts: ^6.2.1
  flutter_staggered_animations: ^1.1.1
  table_calendar: ^3.1.3
  fl_chart: ^0.71.0
  
  # Utilities
  uuid: ^4.2.1
  intl: ^0.20.2
  http: ^1.1.0
  url_launcher: ^6.1.11
```

### Development Dependencies
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
```

## Architecture Constraints

### Platform Support
- **Primary**: Android, iOS
- **Secondary**: Web (limited functionality)
- **Future**: Windows, macOS, Linux

### Performance Requirements
- **App startup**: < 3 seconds
- **Image loading**: < 2 seconds với caching
- **Sync operations**: Background, non-blocking
- **Memory usage**: < 150MB trên thiết bị trung bình

### Security Requirements
- **Data encryption**: TLS 1.3 cho network
- **Local storage**: Encrypted SharedPreferences
- **Authentication**: JWT tokens với refresh
- **API security**: Row Level Security trên Supabase

## Development Environment

### Required Tools
```bash
# Flutter SDK
flutter --version
# Flutter 3.x.x • channel stable

# Dart SDK (included with Flutter)
dart --version
# Dart SDK version: 3.x.x

# Android Studio / VS Code
# Android SDK 33+
# Xcode 14+ (for iOS)
```

### Environment Setup
```bash
# Install dependencies
flutter pub get

# Generate code (for Hive)
flutter packages pub run build_runner build

# Run on device
flutter run

# Build release
flutter build apk --release
flutter build ios --release
```

### Configuration Files
- **`pubspec.yaml`**: Dependencies và assets
- **`analysis_options.yaml`**: Linting rules
- **`android/app/google-services.json`**: Firebase config
- **`lib/supabase_options.dart`**: Supabase configuration

## Database Schema

### Supabase Tables
```sql
-- Core tables
CREATE TABLE dishes (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  ingredients JSONB,
  instructions JSONB,
  calories INTEGER,
  suitable_for JSONB,
  categories JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_dishes (
  id TEXT PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  ingredients JSONB,
  instructions JSONB,
  calories INTEGER,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE meal_plans (
  id TEXT PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  date DATE NOT NULL,
  meal_type TEXT NOT NULL,
  dish_id TEXT,
  dish_name TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE favorites (
  id TEXT PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  dish_id TEXT NOT NULL,
  dish_type TEXT NOT NULL, -- 'default' or 'user'
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Row Level Security Policies
```sql
-- Default dishes policies (Updated December 2024)
ALTER TABLE dishes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view default dishes" ON dishes
  FOR SELECT USING (auth.role() = 'authenticated');

-- User dishes policies
ALTER TABLE user_dishes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own dishes" ON user_dishes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own dishes" ON user_dishes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own dishes" ON user_dishes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own dishes" ON user_dishes
  FOR DELETE USING (auth.uid() = user_id);

-- Meal plans policies
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own meal plans" ON meal_plans
  FOR ALL USING (auth.uid() = user_id);

-- Favorites policies
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own favorites" ON favorites
  FOR ALL USING (auth.uid() = user_id);
```

## Storage Strategy

### Local Storage
```dart
// SharedPreferences - Settings & Cache
SharedPreferences prefs = await SharedPreferences.getInstance();
prefs.setString('user_preferences', json);

// Hive - Complex Objects
@HiveType(typeId: 0)
class CachedDish extends HiveObject {
  @HiveField(0)
  String id;
  
  @HiveField(1)
  String name;
  
  @HiveField(2)
  DateTime cachedAt;
}

// File System - Images
final directory = await getApplicationDocumentsDirectory();
final imagePath = '${directory.path}/images/$dishId.jpg';
```

### Cloud Storage
```dart
// Supabase Storage
final supabase = Supabase.instance.client;

// Upload image
final response = await supabase.storage
    .from('dish_images')
    .upload('user_id/dish_id.jpg', imageFile);

// Get public URL
final imageUrl = supabase.storage
    .from('dish_images')
    .getPublicUrl('user_id/dish_id.jpg');
```

## API Integration

### Supabase Client Setup
```dart
// lib/supabase_options.dart
class SupabaseOptions {
  static const String url = 'YOUR_SUPABASE_URL';
  static const String anonKey = 'YOUR_SUPABASE_ANON_KEY';
}

// main.dart
await Supabase.initialize(
  url: SupabaseOptions.url,
  anonKey: SupabaseOptions.anonKey,
);
```

### API Patterns
```dart
class DishService {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  // GET with error handling
  Future<List<Dish>> getUserDishes() async {
    try {
      final response = await _supabase
          .from('user_dishes')
          .select()
          .eq('user_id', _supabase.auth.currentUser!.id);
      
      return response.map((json) => Dish.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw Exception('Database error: ${e.message}');
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
  
  // POST with validation
  Future<Dish> createDish(Dish dish) async {
    final dishData = dish.toJson();
    dishData['user_id'] = _supabase.auth.currentUser!.id;
    
    final response = await _supabase
        .from('user_dishes')
        .insert(dishData)
        .select()
        .single();
    
    return Dish.fromJson(response);
  }
}
```

## Build & Deployment

### Android Build
```bash
# Debug build
flutter build apk --debug

# Release build
flutter build apk --release --split-per-abi

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release

# Archive for App Store
flutter build ipa --release
```

### Build Configuration
```yaml
# android/app/build.gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt')
        }
    }
}
```

## Testing Strategy

### Unit Tests
```dart
// test/services/dish_service_test.dart
void main() {
  group('DishService', () {
    late DishService dishService;
    
    setUp(() {
      dishService = DishService();
    });
    
    test('should create dish successfully', () async {
      final dish = Dish(id: 'test', name: 'Test Dish');
      final result = await dishService.createDish(dish);
      
      expect(result.id, equals('test'));
      expect(result.name, equals('Test Dish'));
    });
  });
}
```

### Widget Tests
```dart
// test/widgets/dish_card_test.dart
void main() {
  testWidgets('DishCard displays dish information', (tester) async {
    final dish = Dish(id: '1', name: 'Test Dish');
    
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: DishCard(dish: dish),
        ),
      ),
    );
    
    expect(find.text('Test Dish'), findsOneWidget);
  });
}
```

### Integration Tests
```dart
// integration_test/app_test.dart
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('App Integration Tests', () {
    testWidgets('complete user flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // Test login flow
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      // Test dish creation
      await tester.tap(find.byKey(Key('add_dish_button')));
      await tester.pumpAndSettle();
    });
  });
}
```

## Performance Optimization

### Image Optimization
```dart
class ImageOptimizer {
  static Future<File> compressImage(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);
    
    // Resize if too large
    final resized = img.copyResize(
      image!,
      width: image.width > 1024 ? 1024 : null,
      height: image.height > 1024 ? 1024 : null,
    );
    
    // Compress
    final compressed = img.encodeJpg(resized, quality: 85);
    
    // Save to temp file
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await tempFile.writeAsBytes(compressed);
    
    return tempFile;
  }
}
```

### Memory Management
```dart
class ImageCacheService {
  static const int maxCacheSize = 50; // MB
  final Map<String, Uint8List> _cache = {};
  int _currentCacheSize = 0;
  
  void _evictOldestIfNeeded() {
    while (_currentCacheSize > maxCacheSize * 1024 * 1024) {
      final oldestKey = _cache.keys.first;
      final removedSize = _cache[oldestKey]!.length;
      _cache.remove(oldestKey);
      _currentCacheSize -= removedSize;
    }
  }
}
```

## Monitoring & Analytics

### Error Tracking
```dart
// Using Flutter's built-in error handling
void main() {
  FlutterError.onError = (FlutterErrorDetails details) {
    // Log to crash reporting service
    print('Flutter Error: ${details.exception}');
  };
  
  runZonedGuarded(() {
    runApp(MyApp());
  }, (error, stackTrace) {
    // Log Dart errors
    print('Dart Error: $error');
  });
}
```

## Recent Technical Updates (December 2024)

### Authentication Architecture Changes
```dart
// Updated MenuProvider initialization
class MenuProvider extends ChangeNotifier {
  Future<void> initialize() async {
    final currentUser = _supabase.auth.currentUser;
    if (currentUser != null) {
      // Load both default dishes from Supabase and custom dishes from SharedPreferences
      await _dishRepository.loadDishes();
      await _dishRepository.loadCustomDishesFromSharedPreferences();
    } else {
      // Throw error for unauthenticated users
      throw Exception('Vui lòng đăng nhập để sử dụng ứng dụng');
    }
  }
}
```

### Data Repository Optimization
```dart
// Enhanced DishRepository for authenticated-only operation
class DishRepository {
  Future<void> loadDishes() async {
    final currentUser = _supabase.auth.currentUser;
    if (currentUser == null) {
      throw Exception('Người dùng chưa đăng nhập. Vui lòng đăng nhập để sử dụng ứng dụng.');
    }
    
    // Load default dishes from Supabase only
    await _loadDefaultDishes();
    
    // Load user's custom dishes from SharedPreferences
    await _loadCustomDishesFromSharedPreferences();
  }
}
```

### Code Quality Improvements
- **Removed**: `default_dishes_local.dart` - No longer needed with mandatory authentication
- **Cleaned**: Unused imports and methods related to local data fallback
- **Enhanced**: Error handling for unauthenticated access scenarios
- **Streamlined**: Data loading flow to focus on Supabase integration

### Security Enhancements
- **Enforced**: Mandatory authentication for all app features
- **Updated**: RLS policies to ensure authenticated access to default dishes
- **Removed**: Local data fallback mechanisms that could bypass authentication
- **Improved**: Error messages for better user guidance on authentication requirements

### Performance Monitoring
```dart
class PerformanceMonitor {
  static void trackOperation(String operation, Function() callback) {
    final stopwatch = Stopwatch()..start();
    
    try {
      callback();
    } finally {
      stopwatch.stop();
      print('$operation took ${stopwatch.elapsedMilliseconds}ms');
    }
  }
}
```