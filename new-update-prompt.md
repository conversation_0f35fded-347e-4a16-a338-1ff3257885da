Task: tôi sẽ có big update app để phù hợp với nhu cầu người dùng hơn như sau:

1. Cập nhật định hướng của app:
   1. Người không biết ăn gì -> cần 1 người trợ lý giúp họ tìm ra những món ăn phù hợp và nhanh trong tương lai họ muốn trợ lý này sẽ gợi ý theo tuần để họ khỏi mất công suy nghĩ
   2. đố<PERSON> tượng: Nhân viên nữ VP bận rộn, mẹ, người có gia đình chú trọng đến bữa ăn
   3. gi<PERSON> trị cốt lõi: thông minh gọn chính xác
2. Những cải tiến mới:
   1. <PERSON>hi người dùng vừa đăng nhập vào lần đầu sẽ hỏi bạn là ai và đưa ra lần lượt các câu hỏi để người dùng ấn next, bộ câu hỏi bao gồm:
      1. <PERSON><PERSON><PERSON> là nam hay nữ
      2. bạn hướng tới sự đơn giản hay cầu kỳ.
   2. Mỗi lần vào app sẽ hiện lên nổi bật nhất là nút gợi ý món ăn, khi người dùng click vào thì sẽ có bộ câu hỏi để người dùng chọn và next như sau:
      1. Bạn muốn chuẩn bị bữa sáng trưa hay tối?
      2. Bạn có ăn chay không?
      3. Nguyên liệu chính bạn muốn là gì?
         1. Đưa ra các box các nguyên liệu chính như thịt lợn, gà, thịt bò, vv...v.., mỗi gợi ý sẽ kèm hình vẽ minh hoạ svg sinh động để người dùng tích vào, cho phép tích nhiều nhưng nổi bật nhất sẽ là nút tự hệ thống tự gợi ý, khi click vào thì sẽ tự động tích tất cả các nguyên liệu
    4. Sau khi chọn xong sẽ hiện ra danh sách các món ăn gợi ý, cơ chế danh sách gợi ý này sẽ được lấy từ những bài của youtube search bằng cách cộng các chuỗi thu thập trên người dùng ở trên tuy nhiên khi thiết kế giao diện sẽ ghi đây là tính năng AI và chỉ hiện gợi ý các bài youtube về món ăn phù hợp-> khi người dùng click vào món ăn thì hiện luôn video youtube đó ngay trên app sau khi tích vào lưu vào danh sách thực đơn hàng tuần của họ 
3. Lưu ý: thiết kế hiện đại, app dễ sử dụng nhất có thể những code cũ của app phần nào lấy được thì lấy không lấy được hãy xoá bỏ đi 
