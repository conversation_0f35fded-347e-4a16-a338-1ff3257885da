name: nau_gi_day
description: CookSpark AI - Ứng dụng gợi ý món ăn thông minh với AI.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+5

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  url_launcher: ^6.1.11
  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  shared_preferences: ^2.5.3
  path_provider: ^2.1.2
  path: ^1.8.3
  image_picker: ^1.0.4
  table_calendar: ^3.1.3
  fl_chart: ^0.71.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  google_fonts: ^6.2.1
  flutter_staggered_animations: ^1.1.1
  uuid: ^4.2.1
  http: ^1.1.0
  vector_math: 2.1.4
  image: ^4.1.3
  flutter_localizations:
    sdk: flutter
  # Thay Firebase bằng Supabase
  supabase_flutter: ^2.8.4
  flutter_svg: ^2.0.9
  # Thêm app_links với phiên bản tương thích
  app_links: ^3.5.1
  permission_handler: ^12.0.0+1
  # Thêm Google Sign In cho OAuth
  google_sign_in: ^6.1.5
  # YouTube API cho tích hợp video
  youtube_explode_dart: ^2.2.1
  # YouTube Player cho embedded video
  youtube_player_flutter: ^9.0.3
  # WebView cho fallback
  webview_flutter: ^4.4.4
  # Google Generative AI cho Gemini
  google_generative_ai: ^0.4.6
  # HTML rendering cho comments
  flutter_html: ^3.0.0

# Khắc phục lỗi AppLinks từ supabase_flutter - downgrade về phiên bản tương thích
dependency_overrides:
  app_links: ^3.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  mocktail: ^1.0.4
  mockito: ^5.4.6

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Roboto-Light.ttf
  #         weight: 300
  #       - asset: assets/fonts/Roboto-Medium.ttf
  #         weight: 500
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#FFFFFF"
    theme_color: "#4CAF50"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48
