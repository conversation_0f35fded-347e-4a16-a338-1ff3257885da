# CookSpark - Quy tắc Code và Kiến trúc Dự án

## Ngày tạo: 2024-12-19

## Tổng quan

Thư mục này chứa các quy tắc code và kiến trúc cho dự án CookSpark - một ứng dụng Flutter để quản lý món ăn và lập kế hoạch bữa ăn.

## Cấu trúc Quy tắc

### 📱 [Frontend Rules](./frontend_rules.md)
Quy tắc cho phần giao diện người dùng (Flutter/Dart)
- Cấu trúc thư mục và tổ chức code
- Widget patterns và state management
- UI/UX guidelines và theming
- Performance optimization

### ⚙️ [Backend Rules](./backend_rules.md)
Quy tắc cho tầng logic nghiệp vụ (Services Layer)
- Service architecture và patterns
- Error handling và logging
- API integration patterns
- Data transformation và validation

### 🗄️ [Database Rules](./database_rules.md)
Quy tắc cho cơ sở dữ liệu (Supabase/PostgreSQL)
- Schema design và naming conventions
- Security với Row Level Security (RLS)
- Performance optimization và indexing
- Migration và backup strategies

## Kiến trúc Tổng thể

```
CookSpark Architecture
├── Frontend (Flutter)
│   ├── Screens (UI Pages)
│   ├── Widgets (Reusable Components)
│   ├── Providers (State Management)
│   └── Models (Data Models)
├── Backend (Services Layer)
│   ├── Auth Services
│   ├── Business Logic Services
│   ├── Data Services
│   └── Integration Services
└── Database (Supabase)
    ├── PostgreSQL Tables
    ├── Row Level Security
    ├── Real-time Subscriptions
    └── Storage Buckets
```

## Nguyên tắc Thiết kế

### 1. Separation of Concerns
- **Screens**: Chỉ chứa UI logic và user interactions
- **Services**: Chứa business logic và data operations
- **Models**: Chỉ chứa data structure và basic validation
- **Providers**: Quản lý state và notify UI changes

### 2. Single Responsibility Principle
- Mỗi class/function chỉ có một trách nhiệm duy nhất
- Services được chia theo chức năng cụ thể
- Widgets được tách nhỏ và tái sử dụng

### 3. Dependency Injection
- Services không phụ thuộc trực tiếp vào concrete implementations
- Sử dụng constructor injection cho testability
- Provider pattern cho state management

### 4. Error Handling
- Consistent error handling patterns across all layers
- User-friendly error messages
- Proper logging cho debugging

## Quy tắc Comment

### Mô tả ý nghĩa và ngày tháng
```dart
// Khởi tạo ứng dụng với AppInit (chỉ khởi tạo Supabase, không load dishes)
// Ngày cập nhật: 2024-12-19
// Lý do: Tối ưu performance bằng cách load data sau khi đăng nhập
await AppInit.initialize();

// TODO: Refactor route handling để giảm code duplication - 2024-12-19
// FIXME: Duplicate route definition cần được xử lý - 2024-12-19
// NOTE: Temporary workaround cho argument passing issue - 2024-12-19
```

### Các loại comment cần thiết:
1. **Mô tả chức năng**: Giải thích tại sao code này tồn tại
2. **Business logic**: Giải thích quy tắc nghiệp vụ phức tạp
3. **Workarounds**: Giải thích temporary solutions
4. **TODOs**: Những việc cần làm trong tương lai
5. **FIXMEs**: Những bugs hoặc issues cần fix
6. **Performance notes**: Giải thích optimizations

## Coding Standards

### Dart/Flutter
- Tuân thủ [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Sử dụng `dart format` để format code
- Sử dụng `dart analyze` để check code quality
- Prefer const constructors khi có thể
- Sử dụng meaningful variable names

### SQL/Database
- Sử dụng snake_case cho table và column names
- Luôn sử dụng explicit JOIN syntax
- Sử dụng meaningful constraint names
- Comment cho complex queries

## Git Workflow

### Branch Naming
```
feature/add-meal-planning
bugfix/fix-image-upload
hotfix/critical-auth-issue
refactor/improve-service-layer
```

### Commit Messages
```
feat: thêm chức năng lập kế hoạch bữa ăn
fix: sửa lỗi upload ảnh không thành công
refactor: cải thiện cấu trúc service layer
docs: cập nhật quy tắc code cho database
test: thêm unit tests cho AuthService
```

## Testing Strategy

### Unit Tests
- Test business logic trong services
- Test data models và validation
- Test utility functions

### Widget Tests
- Test UI components
- Test user interactions
- Test state changes

### Integration Tests
- Test complete user flows
- Test database operations
- Test API integrations

## Performance Guidelines

### Frontend
- Sử dụng const constructors
- Implement proper dispose methods
- Lazy loading cho large lists
- Image caching và optimization

### Backend
- Database query optimization
- Proper indexing strategy
- Connection pooling
- Caching frequently accessed data

### Database
- Regular VACUUM và ANALYZE
- Monitor slow queries
- Optimize indexes based on query patterns
- Use appropriate data types

## Security Considerations

### Frontend
- Validate user input
- Sanitize data before display
- Secure storage cho sensitive data
- Proper error handling (không expose internal details)

### Backend
- Input validation và sanitization
- Proper authentication và authorization
- Rate limiting cho API calls
- Secure logging (không log sensitive data)

### Database
- Row Level Security (RLS) cho tất cả user tables
- Principle of least privilege
- Regular security audits
- Encrypted connections

## Deployment

### Environment Configuration
```
Development:
- Local Supabase instance
- Debug logging enabled
- Hot reload enabled

Staging:
- Staging Supabase instance
- Limited logging
- Performance monitoring

Production:
- Production Supabase instance
- Error logging only
- Full monitoring và analytics
```

## Monitoring và Logging

### Application Logs
```dart
// Success operations
print('✅ Operation thành công: $result');

// Errors
print('❌ Lỗi: $error');

// Warnings
print('⚠️ Cảnh báo: $warning');

// Info
print('ℹ️ Thông tin: $info');
```

### Database Monitoring
- Query performance tracking
- Connection monitoring
- Storage usage tracking
- Error rate monitoring

## Maintenance

### Regular Tasks
1. **Code Review**: Review code changes theo quy tắc này
2. **Performance Review**: Monitor và optimize performance
3. **Security Review**: Regular security audits
4. **Documentation Update**: Cập nhật docs khi có changes
5. **Dependency Update**: Update packages và dependencies

### Quarterly Reviews
1. **Architecture Review**: Đánh giá và cải thiện architecture
2. **Code Quality Review**: Refactor code cũ theo standards mới
3. **Performance Optimization**: Optimize based on metrics
4. **Security Audit**: Comprehensive security review

---

**Lưu ý**: Các quy tắc này là living documents và sẽ được cập nhật theo sự phát triển của dự án. Mọi thay đổi cần được document với ngày tháng và lý do.