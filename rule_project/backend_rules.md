# Quy tắc Backend - CookSpark Services Layer

## Ng<PERSON>y tạo: 2024-12-19

## 1. Cấu trúc Services

### Mô tả: Tổ chức services theo chức năng và responsibility
```
services/
├── auth_service.dart           # Authentication và user management
├── supabase_auth_service.dart  # Supabase-specific auth implementation
├── dish_service.dart           # Dish CRUD operations
├── user_dish_service.dart      # User-specific dish operations
├── meal_plan_service.dart      # Meal planning logic
├── meal_history_service.dart   # Meal history tracking
├── storage_service.dart        # File storage operations
├── image_cache_service.dart    # Image caching logic
├── sync_service.dart           # Data synchronization
└── supabase_integration_service.dart # Supabase integration
```

## 2. Service Class Structure

### Mô tả: Cấu trúc nhất quán cho tất cả service classes
```dart
class AuthService {
  // 1. Dependencies và clients
  final supabase = Supabase.instance.client;
  
  // 2. Constructor với initialization logging
  AuthService() {
    try {
      print('AuthService: Đã khởi tạo Supabase client');
    } catch (e) {
      print('AuthService: Lỗi khởi tạo: $e');
      rethrow;
    }
  }
  
  // 3. Private validation methods
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  // 4. Public getters
  User? get currentUser => supabase.auth.currentUser;
  
  // 5. Public methods
  Future<Map<String, dynamic>> signInWithGoogle() async {
    // Implementation
  }
}
```

## 3. Error Handling Pattern

### Mô tả: Xử lý lỗi nhất quán với proper logging và user feedback
```dart
Future<Map<String, dynamic>> performOperation() async {
  try {
    print('=== BẮT ĐẦU OPERATION ===');
    
    // Business logic here
    final result = await someAsyncOperation();
    
    print('Operation thành công: $result');
    return {
      'success': true,
      'data': result,
      'message': 'Thao tác thành công'
    };
    
  } on SpecificException catch (e) {
    print('Lỗi cụ thể: $e');
    return {
      'success': false,
      'error': e.toString(),
      'message': 'Lỗi cụ thể xảy ra'
    };
    
  } catch (e) {
    print('Lỗi không xác định: $e');
    return {
      'success': false,
      'error': e.toString(),
      'message': 'Đã xảy ra lỗi không xác định'
    };
  }
}
```

## 4. Async/Await Patterns

### Mô tả: Sử dụng async/await nhất quán với proper error handling
```dart
// Correct pattern
Future<bool> saveData(Map<String, dynamic> data) async {
  try {
    await repository.save(data);
    return true;
  } catch (e) {
    print('Lỗi lưu dữ liệu: $e');
    return false;
  }
}

// Avoid this pattern
Future<bool> saveDataBad(Map<String, dynamic> data) {
  return repository.save(data).then((result) {
    return true;
  }).catchError((e) {
    print('Lỗi: $e');
    return false;
  });
}
```

## 5. Validation Rules

### Mô tả: Validation logic phải được centralized trong services
```dart
class AuthService {
  // Email validation với regex pattern
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  // Password validation với minimum requirements
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }
  
  // Comprehensive validation method
  Map<String, dynamic> validateUserInput(String email, String password) {
    if (!_isValidEmail(email)) {
      return {
        'isValid': false,
        'message': 'Email không hợp lệ'
      };
    }
    
    if (!_isValidPassword(password)) {
      return {
        'isValid': false,
        'message': 'Mật khẩu phải có ít nhất 6 ký tự'
      };
    }
    
    return {'isValid': true};
  }
}
```

## 6. Logging Standards

### Mô tả: Logging nhất quán để debug và monitor
```dart
// Success logging
print('✅ Operation thành công: $result');

// Error logging
print('❌ Lỗi operation: $error');

// Info logging
print('ℹ️ Thông tin: $info');

// Debug logging (chỉ trong development)
print('🐛 Debug: $debugInfo');

// Warning logging
print('⚠️ Cảnh báo: $warning');

// Process logging
print('=== BẮT ĐẦU PROCESS_NAME ===');
// ... process logic
print('=== KẾT THÚC PROCESS_NAME ===');
```

## 7. Return Value Patterns

### Mô tả: Standardized return values cho consistency
```dart
// Success response
return {
  'success': true,
  'data': result,
  'message': 'Thao tác thành công',
  'timestamp': DateTime.now().toIso8601String()
};

// Error response
return {
  'success': false,
  'error': errorDetails,
  'message': 'Mô tả lỗi cho user',
  'errorCode': 'ERROR_CODE_001',
  'timestamp': DateTime.now().toIso8601String()
};

// Validation response
return {
  'isValid': false,
  'errors': ['Lỗi 1', 'Lỗi 2'],
  'message': 'Dữ liệu không hợp lệ'
};
```

## 8. Dependency Injection

### Mô tả: Sử dụng dependency injection cho testability
```dart
class DishService {
  final SupabaseClient _supabase;
  final StorageService _storageService;
  
  // Constructor injection
  DishService({
    SupabaseClient? supabase,
    StorageService? storageService,
  }) : _supabase = supabase ?? Supabase.instance.client,
       _storageService = storageService ?? StorageService();
  
  // Factory constructor cho default dependencies
  factory DishService.defaultInstance() {
    return DishService();
  }
}
```

## 9. Caching Strategy

### Mô tả: Implement caching để improve performance
```dart
class ImageCacheService {
  static final Map<String, String> _cache = {};
  
  // Cache với expiration
  static void cacheImage(String key, String value) {
    _cache[key] = value;
    // Implement expiration logic
  }
  
  // Retrieve từ cache với fallback
  static String? getCachedImage(String key) {
    return _cache[key];
  }
  
  // Clear cache method
  static void clearCache() {
    _cache.clear();
    print('✅ Cache đã được xóa');
  }
}
```

## 10. API Integration Patterns

### Mô tả: Patterns cho external API calls
```dart
class SupabaseIntegrationService {
  final SupabaseClient _client;
  
  SupabaseIntegrationService(this._client);
  
  // Generic API call method
  Future<Map<String, dynamic>> makeApiCall<T>({
    required Future<T> Function() apiCall,
    required String operationName,
  }) async {
    try {
      print('=== BẮT ĐẦU $operationName ===');
      
      final result = await apiCall();
      
      print('✅ $operationName thành công');
      return {
        'success': true,
        'data': result,
      };
      
    } catch (e) {
      print('❌ Lỗi $operationName: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
```

## 11. Data Transformation

### Mô tả: Consistent data transformation patterns
```dart
class DataTransformService {
  // Transform từ API response sang model
  static Dish fromApiResponse(Map<String, dynamic> json) {
    try {
      return Dish(
        id: json['id'] ?? '',
        name: json['name'] ?? 'Không có tên',
        description: json['description'] ?? '',
        // ... other fields
      );
    } catch (e) {
      print('❌ Lỗi transform data: $e');
      rethrow;
    }
  }
  
  // Transform từ model sang API request
  static Map<String, dynamic> toApiRequest(Dish dish) {
    return {
      'id': dish.id,
      'name': dish.name,
      'description': dish.description,
      // ... other fields
    };
  }
}
```

## 12. Testing Considerations

### Mô tả: Code structure để support unit testing
```dart
// Testable service structure
class TestableService {
  final ApiClient _apiClient;
  final Logger _logger;
  
  TestableService(this._apiClient, this._logger);
  
  // Pure functions cho business logic
  bool validateInput(String input) {
    return input.isNotEmpty && input.length > 3;
  }
  
  // Async operations với clear dependencies
  Future<Result> processData(String data) async {
    if (!validateInput(data)) {
      return Result.error('Invalid input');
    }
    
    try {
      final response = await _apiClient.post(data);
      return Result.success(response);
    } catch (e) {
      _logger.error('Process failed: $e');
      return Result.error(e.toString());
    }
  }
}
```