# Quy tắc Database - CookSpark Supabase Integration

## Ngày tạo: 2024-12-19

## 1. Database Architecture

### Mô tả: Sử dụng Supabase PostgreSQL với real-time capabilities
```
Supabase Configuration:
- URL: https://jypmpcvdjhdnenaifamc.supabase.co
- Authentication: JWT-based với anonymous key
- Real-time: WebSocket connections cho live updates
- Storage: Supabase Storage cho images và files
```

## 2. Table Naming Conventions

### Mô tả: Sử dụng snake_case cho table và column names
```sql
-- Table names: singular, snake_case
CREATE TABLE dish (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    video_url TEXT,
    is_custom BOOLEAN DEFAULT false,
    is_network_image BOOLEAN DEFAULT true,
    calories INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction tables: table1_table2
CREATE TABLE dish_category (
    dish_id UUID REFERENCES dish(id),
    category_id UUID REFERENCES category(id),
    PRIMARY KEY (dish_id, category_id)
);
```

## 3. Primary Keys và Relationships

### Mô tả: Sử dụng UUID cho primary keys và proper foreign key constraints
```sql
-- Primary key pattern
id UUID PRIMARY KEY DEFAULT gen_random_uuid()

-- Foreign key pattern
user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
dish_id UUID REFERENCES dish(id) ON DELETE CASCADE

-- Composite keys cho junction tables
PRIMARY KEY (user_id, dish_id)

-- Indexes cho performance
CREATE INDEX idx_dish_user_id ON user_dish(user_id);
CREATE INDEX idx_dish_created_at ON dish(created_at);
CREATE INDEX idx_meal_plan_date ON meal_plan(plan_date);
```

## 4. Row Level Security (RLS)

### Mô tả: Implement RLS cho data security và user isolation
```sql
-- Enable RLS
ALTER TABLE user_dish ENABLE ROW LEVEL SECURITY;

-- Policy cho user chỉ access data của mình
CREATE POLICY "Users can only access their own dishes" ON user_dish
    FOR ALL USING (auth.uid() = user_id);

-- Policy cho public dishes (readable by all)
CREATE POLICY "Public dishes are readable by all" ON dish
    FOR SELECT USING (is_custom = false OR auth.uid() IS NOT NULL);

-- Policy cho custom dishes (chỉ owner)
CREATE POLICY "Users can manage their custom dishes" ON dish
    FOR ALL USING (
        is_custom = true AND 
        id IN (
            SELECT dish_id FROM user_dish 
            WHERE user_id = auth.uid()
        )
    );
```

## 5. Data Types và Constraints

### Mô tả: Sử dụng appropriate data types với proper constraints
```sql
-- Text fields với length constraints
name VARCHAR(255) NOT NULL CHECK (length(name) > 0),
description TEXT,

-- Numeric fields với range constraints
calories INTEGER DEFAULT 0 CHECK (calories >= 0),
serving_size INTEGER DEFAULT 1 CHECK (serving_size > 0),

-- Boolean fields với defaults
is_custom BOOLEAN DEFAULT false,
is_active BOOLEAN DEFAULT true,

-- JSON fields cho flexible data
ingredients JSONB DEFAULT '[]'::jsonb,
instructions JSONB DEFAULT '[]'::jsonb,
nutrients JSONB DEFAULT '{}'::jsonb,
notes JSONB DEFAULT '{}'::jsonb,

-- Enum types cho controlled values
CREATE TYPE meal_type AS ENUM ('breakfast', 'lunch', 'dinner', 'any');
suitable_for meal_type[] DEFAULT '{any}',

-- Timestamp fields
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
```

## 6. Triggers và Functions

### Mô tả: Sử dụng database triggers cho automated tasks
```sql
-- Function để update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger cho auto-update timestamp
CREATE TRIGGER update_dish_updated_at 
    BEFORE UPDATE ON dish 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function để validate JSON structure
CREATE OR REPLACE FUNCTION validate_ingredients_json(ingredients JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Kiểm tra ingredients là array of strings
    RETURN jsonb_typeof(ingredients) = 'array' AND
           (SELECT bool_and(jsonb_typeof(value) = 'string') 
            FROM jsonb_array_elements(ingredients));
END;
$$ LANGUAGE plpgsql;

-- Constraint sử dụng validation function
ALTER TABLE dish ADD CONSTRAINT valid_ingredients 
    CHECK (validate_ingredients_json(ingredients));
```

## 7. Indexing Strategy

### Mô tả: Tối ưu performance với proper indexing
```sql
-- Indexes cho frequent queries
CREATE INDEX idx_dish_name_search ON dish USING gin(to_tsvector('english', name));
CREATE INDEX idx_dish_categories ON dish USING gin(categories);
CREATE INDEX idx_dish_suitable_for ON dish USING gin(suitable_for);

-- Composite indexes cho complex queries
CREATE INDEX idx_user_dish_lookup ON user_dish(user_id, dish_id);
CREATE INDEX idx_meal_plan_user_date ON meal_plan(user_id, plan_date);

-- Partial indexes cho filtered queries
CREATE INDEX idx_active_dishes ON dish(created_at) WHERE is_active = true;
CREATE INDEX idx_custom_dishes ON dish(user_id) WHERE is_custom = true;
```

## 8. Data Migration Patterns

### Mô tả: Safe migration practices cho schema changes
```sql
-- Migration script template
-- Migration: add_nutrition_info_to_dishes
-- Date: 2024-12-19
-- Description: Thêm thông tin dinh dưỡng vào bảng dish

BEGIN;

-- 1. Add new columns
ALTER TABLE dish ADD COLUMN IF NOT EXISTS protein DECIMAL(5,2) DEFAULT 0;
ALTER TABLE dish ADD COLUMN IF NOT EXISTS carbs DECIMAL(5,2) DEFAULT 0;
ALTER TABLE dish ADD COLUMN IF NOT EXISTS fat DECIMAL(5,2) DEFAULT 0;

-- 2. Add constraints
ALTER TABLE dish ADD CONSTRAINT check_protein_positive 
    CHECK (protein >= 0);

-- 3. Create indexes if needed
CREATE INDEX IF NOT EXISTS idx_dish_nutrition 
    ON dish(protein, carbs, fat) WHERE protein > 0;

-- 4. Update existing data if needed
UPDATE dish SET protein = 0, carbs = 0, fat = 0 
WHERE protein IS NULL;

COMMIT;
```

## 9. Backup và Recovery

### Mô tả: Backup strategy cho data protection
```sql
-- Backup important tables
pg_dump --table=dish --table=user_dish --table=meal_plan 
        --data-only --file=data_backup_$(date +%Y%m%d).sql

-- Point-in-time recovery setup
-- Supabase tự động handle backup, nhưng cần:
-- 1. Regular exports của critical data
-- 2. Test restore procedures
-- 3. Document recovery steps

-- Export user data function
CREATE OR REPLACE FUNCTION export_user_data(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'dishes', (SELECT json_agg(d.*) FROM dish d 
                  JOIN user_dish ud ON d.id = ud.dish_id 
                  WHERE ud.user_id = user_uuid),
        'meal_plans', (SELECT json_agg(mp.*) FROM meal_plan mp 
                      WHERE mp.user_id = user_uuid),
        'meal_history', (SELECT json_agg(mh.*) FROM meal_history mh 
                        WHERE mh.user_id = user_uuid)
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 10. Performance Monitoring

### Mô tả: Monitor database performance và optimize queries
```sql
-- Query performance analysis
EXPLAIN (ANALYZE, BUFFERS) 
SELECT d.*, array_agg(c.name) as category_names
FROM dish d
LEFT JOIN dish_category dc ON d.id = dc.dish_id
LEFT JOIN category c ON dc.category_id = c.id
WHERE d.is_active = true
GROUP BY d.id
ORDER BY d.created_at DESC
LIMIT 20;

-- Slow query identification
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100  -- queries taking > 100ms
ORDER BY mean_time DESC;

-- Index usage monitoring
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_scan = 0  -- unused indexes
ORDER BY schemaname, tablename;
```

## 11. Data Validation Rules

### Mô tả: Comprehensive data validation tại database level
```sql
-- Email validation
CREATE DOMAIN email AS TEXT
CHECK (VALUE ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

-- URL validation
CREATE DOMAIN url AS TEXT
CHECK (VALUE ~ '^https?://[^\s/$.?#].[^\s]*$');

-- JSON schema validation
CREATE OR REPLACE FUNCTION validate_dish_nutrients(nutrients JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Kiểm tra structure của nutrients object
    RETURN (
        jsonb_typeof(nutrients) = 'object' AND
        (nutrients ? 'protein' OR nutrients ? 'carbs' OR nutrients ? 'fat') AND
        (nutrients->>'protein')::NUMERIC >= 0 AND
        (nutrients->>'carbs')::NUMERIC >= 0 AND
        (nutrients->>'fat')::NUMERIC >= 0
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
```

## 12. Security Best Practices

### Mô tả: Database security guidelines
```sql
-- 1. Không store sensitive data trong plain text
-- 2. Sử dụng RLS cho tất cả user tables
-- 3. Limit database permissions theo principle of least privilege
-- 4. Regular security audits

-- Audit trail table
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (table_name, operation, old_values, new_values, user_id)
    VALUES (
        TG_TABLE_NAME,
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
        auth.uid()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit trigger to sensitive tables
CREATE TRIGGER audit_dish_changes
    AFTER INSERT OR UPDATE OR DELETE ON dish
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```