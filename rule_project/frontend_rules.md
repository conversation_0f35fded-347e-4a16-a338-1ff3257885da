# Quy tắc Frontend - CookSpark Flutter App

## Ng<PERSON>y tạo: 2024-12-19

## 1. <PERSON><PERSON><PERSON> trú<PERSON> thư mục

### Mô tả: Tổ chức code theo kiến trúc MVC với Provider pattern
```
lib/
├── data/           # Repository và data sources
├── models/         # Data models và business logic
├── providers/      # State management với Provider
├── screens/        # UI screens/pages
├── services/       # Business logic và API calls
├── utils/          # Utility functions và helpers
└── widgets/        # Reusable UI components
```

## 2. Quy tắc đặt tên

### Mô tả: Sử dụng snake_case cho files, PascalCase cho classes
- **Files**: `dish_card.dart`, `meal_history_provider.dart`
- **Classes**: `DishCard`, `MealHistoryProvider`
- **Variables**: `camelCase` - `dishName`, `imageFile`
- **Constants**: `UPPER_SNAKE_CASE` hoặc `kConstantName`

## 3. Quy tắc Import

### Mô tả: Sắp xếp imports theo thứ tự ưu tiên
```dart
// 1. Dart core libraries
import 'dart:io';

// 2. Flutter framework
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party packages
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

// 4. Local imports (relative paths)
import '../models/dish.dart';
import '../services/auth_service.dart';
```

## 4. Quy tắc Comment

### Mô tả: Comments phải mô tả ý nghĩa và bao gồm ngày tháng khi cần thiết
```dart
// Khởi tạo ứng dụng với AppInit (chỉ khởi tạo Supabase, không load dishes)
// Ngày cập nhật: 2024-12-19
await AppInit.initialize();

// Vô hiệu hóa các cảnh báo OpenGL
// Lý do: Tránh spam console với lỗi không ảnh hưởng đến functionality
FlutterError.onError = (FlutterErrorDetails details) {
  // Implementation...
};

// TODO: Cần refactor route handling - 2024-12-19
// FIXME: Duplicate route definition cần được xử lý - 2024-12-19
```

## 5. Widget Structure

### Mô tả: Cấu trúc widget theo pattern nhất quán
```dart
class DishCard extends StatelessWidget {
  // 1. Final fields trước
  final Dish dish;
  final VoidCallback onTap;
  final VoidCallback? onChangePress;
  
  // 2. Constructor với named parameters
  const DishCard({
    super.key,
    required this.dish,
    required this.onTap,
    this.onChangePress,
  });
  
  // 3. Build method
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: _buildCard(),
    );
  }
  
  // 4. Private helper methods
  Widget _buildCard() {
    // Implementation
  }
}
```

## 6. State Management

### Mô tả: Sử dụng Provider pattern cho state management
```dart
// Provider setup trong main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(
      create: (ctx) => MenuProvider()..initialize(),
    ),
    ChangeNotifierProvider(
      create: (ctx) => MealHistoryProvider()..initialize(),
    ),
  ],
  child: const MyApp(),
)

// Sử dụng Provider trong widgets
Consumer<ThemeProvider>(
  builder: (context, themeProvider, child) {
    return MaterialApp(
      theme: themeProvider.themeData,
      // ...
    );
  },
)
```

## 7. Error Handling

### Mô tả: Xử lý lỗi nhất quán với try-catch và user feedback
```dart
void clearAppCache(BuildContext context) async {
  try {
    final menuProvider = Provider.of<MenuProvider>(context, listen: false);
    await menuProvider.clearCache();
    
    // Success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã xóa cache thành công'),
        backgroundColor: Colors.green,
      ),
    );
  } catch (e) {
    // Error logging
    print('Lỗi khi xóa cache: $e');
    
    // Error feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Lỗi khi xóa cache: $e'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
```

## 8. Routing

### Mô tả: Sử dụng named routes với argument passing
```dart
// Route definition
static const routeName = '/add-dish-screen-2';

// Route registration trong main.dart
AddDishScreen2Name.routeName: (ctx) {
  final Object? arguments = ModalRoute.of(ctx)?.settings.arguments;
  // Type checking và null safety
  File? imageFile;
  if (arguments is File) {
    imageFile = arguments;
  } else {
    print('Unexpected argument type: ${arguments.runtimeType}');
    imageFile = null;
  }
  return AddDishScreen2Name(imageFile: imageFile ?? File(''));
},
```

## 9. Theming

### Mô tả: Sử dụng Material 3 với custom color scheme
```dart
// Theme configuration
ThemeData(
  colorScheme: ColorScheme.fromSeed(
    seedColor: const Color(0xFF4CAF50),
    primary: const Color(0xFF81C784),
    secondary: const Color(0xFFFF9800),
    tertiary: const Color(0xFF64B5F6),
  ),
  useMaterial3: true,
  textTheme: GoogleFonts.montserratTextTheme(),
)
```

## 10. Localization

### Mô tả: Hỗ trợ đa ngôn ngữ với ưu tiên tiếng Việt
```dart
localizationsDelegates: const [
  GlobalMaterialLocalizations.delegate,
  GlobalWidgetsLocalizations.delegate,
  GlobalCupertinoLocalizations.delegate,
],
supportedLocales: const [
  Locale('vi', 'VN'), // Vietnamese - Primary
  Locale('en', 'US'), // English - Secondary
],
locale: const Locale('vi', 'VN'),
```

## 11. Performance

### Mô tả: Tối ưu hiệu suất với lazy loading và caching
- Không load dishes trong main() - load sau khi đăng nhập
- Sử dụng const constructors khi có thể
- Implement proper dispose methods trong StatefulWidgets
- Cache images và data khi cần thiết

## 12. Code Style

### Mô tả: Tuân thủ Dart/Flutter conventions
- Sử dụng `super.key` trong constructors
- Prefer const constructors
- Use trailing commas cho better formatting
- Avoid print() trong production - sử dụng proper logging
- Implement proper null safety với `?` và `??` operators