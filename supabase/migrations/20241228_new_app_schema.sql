-- Migration cho định hướng mới của CookSpark
-- Từ app quản lý công thức nấu ăn sang trợ lý gợi ý món ăn

-- 1. Tạo bảng user_profiles cho thông tin onboarding
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name TEXT,
    email TEXT,
    photo_url TEXT,
    
    -- Thông tin từ onboarding
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    cooking_preference TEXT CHECK (cooking_preference IN ('simple', 'elaborate')),
    favorite_ingredients TEXT[] DEFAULT '{}',
    dietary_restrictions TEXT[] DEFAULT '{}',
    is_vegetarian BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    is_onboarding_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id)
);

-- 2. Tạ<PERSON> bảng weekly_menu_items cho thực đơn hàng tuần
CREATE TABLE IF NOT EXISTS weekly_menu_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Thông tin thời gian và loại bữa ăn
    date DATE NOT NULL,
    meal_type TEXT NOT NULL CHECK (meal_type IN ('breakfast', 'lunch', 'dinner')),
    
    -- Thông tin video YouTube
    video_id TEXT NOT NULL,
    video_title TEXT NOT NULL,
    video_description TEXT DEFAULT '',
    video_thumbnail_url TEXT DEFAULT '',
    video_channel_title TEXT DEFAULT '',
    video_duration_seconds INTEGER DEFAULT 0,
    video_view_count INTEGER DEFAULT 0,
    video_published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Thông tin bổ sung
    notes TEXT DEFAULT '',
    is_completed BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, date, meal_type)
);

-- 3. Tạo bảng meal_suggestion_history cho lịch sử gợi ý
CREATE TABLE IF NOT EXISTS meal_suggestion_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Thông tin query
    meal_type TEXT NOT NULL CHECK (meal_type IN ('breakfast', 'lunch', 'dinner')),
    is_vegetarian BOOLEAN DEFAULT FALSE,
    preferred_ingredients TEXT[] DEFAULT '{}',
    auto_suggest BOOLEAN DEFAULT FALSE,
    search_query TEXT NOT NULL,
    
    -- Kết quả
    results_count INTEGER DEFAULT 0,
    selected_video_id TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Tạo bảng user_preferences cho cài đặt người dùng
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Cài đặt giao diện
    theme_mode TEXT DEFAULT 'system' CHECK (theme_mode IN ('light', 'dark', 'system')),
    language TEXT DEFAULT 'vi' CHECK (language IN ('vi', 'en')),
    
    -- Cài đặt thông báo
    notifications_enabled BOOLEAN DEFAULT TRUE,
    meal_reminders_enabled BOOLEAN DEFAULT TRUE,
    weekly_planning_reminders BOOLEAN DEFAULT TRUE,
    
    -- Cài đặt gợi ý
    default_meal_type TEXT CHECK (default_meal_type IN ('breakfast', 'lunch', 'dinner')),
    auto_suggest_enabled BOOLEAN DEFAULT FALSE,
    preferred_video_duration_max INTEGER DEFAULT 1800, -- 30 phút
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id)
);

-- 5. Tạo indexes cho performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(user_id, is_onboarding_completed);

CREATE INDEX IF NOT EXISTS idx_weekly_menu_items_user_id ON weekly_menu_items(user_id);
CREATE INDEX IF NOT EXISTS idx_weekly_menu_items_date ON weekly_menu_items(user_id, date);
CREATE INDEX IF NOT EXISTS idx_weekly_menu_items_meal_type ON weekly_menu_items(user_id, meal_type);
CREATE INDEX IF NOT EXISTS idx_weekly_menu_items_completed ON weekly_menu_items(user_id, is_completed);

CREATE INDEX IF NOT EXISTS idx_meal_suggestion_history_user_id ON meal_suggestion_history(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_suggestion_history_created_at ON meal_suggestion_history(user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- 6. Tạo RLS policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE weekly_menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_suggestion_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Policies cho user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profile" ON user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Policies cho weekly_menu_items
CREATE POLICY "Users can view own weekly menu items" ON weekly_menu_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own weekly menu items" ON weekly_menu_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own weekly menu items" ON weekly_menu_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own weekly menu items" ON weekly_menu_items
    FOR DELETE USING (auth.uid() = user_id);

-- Policies cho meal_suggestion_history
CREATE POLICY "Users can view own suggestion history" ON meal_suggestion_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own suggestion history" ON meal_suggestion_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies cho user_preferences
CREATE POLICY "Users can view own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- 7. Tạo functions hữu ích

-- Function để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Tạo triggers cho auto-update updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_weekly_menu_items_updated_at 
    BEFORE UPDATE ON weekly_menu_items 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function để lấy thống kê user
CREATE OR REPLACE FUNCTION get_user_statistics(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_menu_items', (
            SELECT COUNT(*) FROM weekly_menu_items 
            WHERE user_id = user_uuid
        ),
        'completed_menu_items', (
            SELECT COUNT(*) FROM weekly_menu_items 
            WHERE user_id = user_uuid AND is_completed = true
        ),
        'total_suggestions', (
            SELECT COUNT(*) FROM meal_suggestion_history 
            WHERE user_id = user_uuid
        ),
        'current_week_items', (
            SELECT COUNT(*) FROM weekly_menu_items 
            WHERE user_id = user_uuid 
            AND date >= date_trunc('week', CURRENT_DATE)
            AND date < date_trunc('week', CURRENT_DATE) + INTERVAL '7 days'
        ),
        'profile_completion', (
            SELECT CASE 
                WHEN is_onboarding_completed THEN 100
                ELSE 50
            END
            FROM user_profiles 
            WHERE user_id = user_uuid
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để cleanup dữ liệu cũ
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Xóa lịch sử gợi ý cũ hơn 6 tháng
    DELETE FROM meal_suggestion_history 
    WHERE created_at < NOW() - INTERVAL '6 months';
    
    -- Xóa menu items cũ hơn 1 năm
    DELETE FROM weekly_menu_items 
    WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Tạo view để dễ dàng query

-- View cho thống kê tuần hiện tại
CREATE OR REPLACE VIEW current_week_menu_stats AS
SELECT 
    user_id,
    COUNT(*) as total_items,
    COUNT(*) FILTER (WHERE is_completed = true) as completed_items,
    COUNT(*) FILTER (WHERE meal_type = 'breakfast') as breakfast_items,
    COUNT(*) FILTER (WHERE meal_type = 'lunch') as lunch_items,
    COUNT(*) FILTER (WHERE meal_type = 'dinner') as dinner_items,
    ROUND(
        (COUNT(*) FILTER (WHERE is_completed = true)::float / COUNT(*)::float) * 100, 
        2
    ) as completion_percentage
FROM weekly_menu_items 
WHERE date >= date_trunc('week', CURRENT_DATE)
AND date < date_trunc('week', CURRENT_DATE) + INTERVAL '7 days'
GROUP BY user_id;

-- View cho user profile đầy đủ
CREATE OR REPLACE VIEW user_profiles_complete AS
SELECT 
    up.*,
    COALESCE(pref.theme_mode, 'system') as theme_mode,
    COALESCE(pref.notifications_enabled, true) as notifications_enabled,
    COALESCE(pref.auto_suggest_enabled, false) as auto_suggest_enabled
FROM user_profiles up
LEFT JOIN user_preferences pref ON up.user_id = pref.user_id;

-- Thêm comment cho documentation
COMMENT ON TABLE user_profiles IS 'Thông tin profile người dùng từ onboarding';
COMMENT ON TABLE weekly_menu_items IS 'Món ăn trong thực đơn hàng tuần';
COMMENT ON TABLE meal_suggestion_history IS 'Lịch sử các lần gợi ý món ăn';
COMMENT ON TABLE user_preferences IS 'Cài đặt và preferences của người dùng';

COMMENT ON COLUMN user_profiles.gender IS 'Giới tính: male, female, other';
COMMENT ON COLUMN user_profiles.cooking_preference IS 'Xu hướng nấu ăn: simple, elaborate';
COMMENT ON COLUMN weekly_menu_items.meal_type IS 'Loại bữa ăn: breakfast, lunch, dinner';
COMMENT ON COLUMN weekly_menu_items.video_id IS 'ID của video YouTube';
