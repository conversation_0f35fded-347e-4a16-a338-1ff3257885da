-- T<PERSON><PERSON> bảng saved_videos để lưu video đã lưu của người dùng
CREATE TABLE IF NOT EXISTS saved_videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    video_id TEXT NOT NULL,
    video_title TEXT NOT NULL,
    video_description TEXT DEFAULT '',
    video_thumbnail_url TEXT DEFAULT '',
    video_channel_title TEXT DEFAULT '',
    video_duration_seconds INTEGER DEFAULT 0,
    video_view_count INTEGER DEFAULT 0,
    video_published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    tags TEXT[] DEFAULT '{}',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- <PERSON><PERSON><PERSON> bảo mỗi user chỉ lưu một video một lần
    UNIQUE(user_id, video_id)
);

-- Tạo index để tăng tốc độ truy vấn
CREATE INDEX IF NOT EXISTS idx_saved_videos_user_id ON saved_videos(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_created_at ON saved_videos(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_saved_videos_tags ON saved_videos USING GIN(tags);

-- Tạo function để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_saved_videos_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger để tự động cập nhật updated_at
CREATE TRIGGER trigger_update_saved_videos_updated_at
    BEFORE UPDATE ON saved_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_saved_videos_updated_at();

-- Thiết lập RLS (Row Level Security)
ALTER TABLE saved_videos ENABLE ROW LEVEL SECURITY;

-- Policy cho phép user chỉ xem video của chính họ
CREATE POLICY "Users can view their own saved videos" ON saved_videos
    FOR SELECT USING (auth.uid() = user_id);

-- Policy cho phép user chỉ thêm video cho chính họ
CREATE POLICY "Users can insert their own saved videos" ON saved_videos
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy cho phép user chỉ cập nhật video của chính họ
CREATE POLICY "Users can update their own saved videos" ON saved_videos
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy cho phép user chỉ xóa video của chính họ
CREATE POLICY "Users can delete their own saved videos" ON saved_videos
    FOR DELETE USING (auth.uid() = user_id);

-- Thêm comment cho bảng
COMMENT ON TABLE saved_videos IS 'Bảng lưu trữ video YouTube đã lưu của người dùng';
COMMENT ON COLUMN saved_videos.user_id IS 'ID của người dùng';
COMMENT ON COLUMN saved_videos.video_id IS 'ID của video YouTube';
COMMENT ON COLUMN saved_videos.video_title IS 'Tiêu đề video';
COMMENT ON COLUMN saved_videos.video_description IS 'Mô tả video';
COMMENT ON COLUMN saved_videos.video_thumbnail_url IS 'URL thumbnail của video';
COMMENT ON COLUMN saved_videos.video_channel_title IS 'Tên kênh YouTube';
COMMENT ON COLUMN saved_videos.video_duration_seconds IS 'Thời lượng video tính bằng giây';
COMMENT ON COLUMN saved_videos.video_view_count IS 'Số lượt xem video';
COMMENT ON COLUMN saved_videos.video_published_at IS 'Thời gian video được đăng';
COMMENT ON COLUMN saved_videos.tags IS 'Các tag tự động tạo cho video';
COMMENT ON COLUMN saved_videos.notes IS 'Ghi chú của người dùng về video';
