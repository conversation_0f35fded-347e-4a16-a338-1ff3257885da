-- Migration: <PERSON><PERSON><PERSON><PERSON> bảng saved_videos cho tính năng lưu video YouTube
-- Date: 2024-12-29
-- Description: T<PERSON><PERSON> bảng để lưu trữ video YouTube mà user đã lưu

-- 1. <PERSON><PERSON><PERSON> bảng saved_videos
CREATE TABLE IF NOT EXISTS saved_videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    video_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    channel_name TEXT,
    duration TEXT,
    view_count TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, video_id)
);

-- 2. T<PERSON>o indexes cho performance
CREATE INDEX IF NOT EXISTS idx_saved_videos_user_id ON saved_videos(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_video_id ON saved_videos(video_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_created_at ON saved_videos(created_at DESC);

-- 3. Tạo trigger để auto-update updated_at
CREATE OR REPLACE FUNCTION update_saved_videos_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_saved_videos_updated_at_trigger
    BEFORE UPDATE ON saved_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_saved_videos_updated_at();

-- 4. Enable RLS (Row Level Security)
ALTER TABLE saved_videos ENABLE ROW LEVEL SECURITY;

-- 5. Tạo RLS policies
CREATE POLICY "Users can view their own saved videos" ON saved_videos
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own saved videos" ON saved_videos
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own saved videos" ON saved_videos
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own saved videos" ON saved_videos
    FOR DELETE USING (auth.uid() = user_id);

-- 6. Grant permissions
GRANT ALL ON saved_videos TO authenticated;
GRANT ALL ON saved_videos TO service_role;
