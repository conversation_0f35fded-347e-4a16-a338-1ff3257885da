import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:nau_gi_day/main.dart' as app;
import 'package:nau_gi_day/screens/onboarding_screen.dart';
import 'package:nau_gi_day/screens/new_home_screen.dart';
import 'package:nau_gi_day/screens/meal_suggestion_screen.dart';
import 'package:nau_gi_day/screens/weekly_menu_screen.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('CookSpark App Flow Integration Tests', () {
    testWidgets('Complete onboarding flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should show onboarding screen for new users
      expect(find.byType(OnboardingScreen), findsOneWidget);

      // Test gender selection
      expect(find.text('Bạn là nam hay nữ?'), findsOneWidget);
      
      // Select gender (assuming "Nam" option exists)
      await tester.tap(find.text('Nam'));
      await tester.pumpAndSettle();

      // Go to next page
      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // Test cooking preference selection
      expect(find.text('Bạn hướng tới sự đơn giản hay cầu kỳ khi nấu ăn?'), findsOneWidget);
      
      // Select cooking preference
      await tester.tap(find.text('Đơn giản'));
      await tester.pumpAndSettle();

      // Complete onboarding
      await tester.tap(find.text('Hoàn thành'));
      await tester.pumpAndSettle();

      // Should navigate to home screen
      expect(find.byType(NewHomeScreen), findsOneWidget);
      expect(find.text('CookSpark'), findsOneWidget);
    });

    testWidgets('Meal suggestion flow', (WidgetTester tester) async {
      // Assuming we're already on the home screen
      app.main();
      await tester.pumpAndSettle();

      // Navigate to meal suggestion
      await tester.tap(find.text('Gợi ý món ăn'));
      await tester.pumpAndSettle();

      expect(find.byType(MealSuggestionScreen), findsOneWidget);

      // Test meal type selection
      expect(find.text('Bạn muốn chuẩn bị bữa nào?'), findsOneWidget);
      
      // Select lunch
      await tester.tap(find.text('Bữa trưa'));
      await tester.pumpAndSettle();

      // Go to next page
      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // Test vegetarian selection
      expect(find.text('Bạn có ăn chay không?'), findsOneWidget);
      
      // Select "Không"
      await tester.tap(find.text('Không'));
      await tester.pumpAndSettle();

      // Go to next page
      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // Test ingredient selection
      expect(find.text('Nguyên liệu chính bạn muốn là gì?'), findsOneWidget);
      
      // Select an ingredient (assuming chicken exists)
      await tester.tap(find.text('Thịt gà'));
      await tester.pumpAndSettle();

      // Generate suggestions
      await tester.tap(find.text('Tìm món ăn'));
      await tester.pumpAndSettle();

      // Should show results screen with loading or videos
      // Note: This might show loading state or mock videos depending on implementation
    });

    testWidgets('Weekly menu navigation', (WidgetTester tester) async {
      // Assuming we're on the home screen
      app.main();
      await tester.pumpAndSettle();

      // Navigate to weekly menu
      await tester.tap(find.text('Thực đơn tuần'));
      await tester.pumpAndSettle();

      expect(find.byType(WeeklyMenuScreen), findsOneWidget);
      expect(find.text('Thực đơn tuần'), findsOneWidget);

      // Should show current week information
      expect(find.textContaining('Tuần'), findsOneWidget);
    });

    testWidgets('Settings navigation', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Open drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Navigate to settings
      await tester.tap(find.text('Cài đặt'));
      await tester.pumpAndSettle();

      // Should show settings screen
      expect(find.text('Cài đặt'), findsOneWidget);
    });

    testWidgets('Theme switching', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Get initial theme
      final BuildContext context = tester.element(find.byType(MaterialApp));
      final bool initialIsDark = Theme.of(context).brightness == Brightness.dark;

      // Open drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Navigate to settings
      await tester.tap(find.text('Cài đặt'));
      await tester.pumpAndSettle();

      // Find and tap theme toggle (assuming it exists)
      final themeToggle = find.byType(Switch).first;
      await tester.tap(themeToggle);
      await tester.pumpAndSettle();

      // Verify theme changed
      final bool newIsDark = Theme.of(context).brightness == Brightness.dark;
      expect(newIsDark, isNot(equals(initialIsDark)));
    });

    testWidgets('Navigation between screens', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test navigation to meal suggestion
      await tester.tap(find.text('Gợi ý món ăn'));
      await tester.pumpAndSettle();
      expect(find.byType(MealSuggestionScreen), findsOneWidget);

      // Go back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.byType(NewHomeScreen), findsOneWidget);

      // Test navigation to weekly menu
      await tester.tap(find.text('Thực đơn tuần'));
      await tester.pumpAndSettle();
      expect(find.byType(WeeklyMenuScreen), findsOneWidget);

      // Go back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.byType(NewHomeScreen), findsOneWidget);
    });

    testWidgets('Error handling', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test meal suggestion with no selection
      await tester.tap(find.text('Gợi ý món ăn'));
      await tester.pumpAndSettle();

      // Try to proceed without selecting meal type
      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // Should stay on same page (no navigation)
      expect(find.text('Bạn muốn chuẩn bị bữa nào?'), findsOneWidget);
    });

    testWidgets('Accessibility features', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Check that main buttons have semantic labels
      expect(find.bySemanticsLabel('Gợi ý món ăn'), findsOneWidget);
      
      // Check that navigation elements are accessible
      final Finder backButton = find.byIcon(Icons.arrow_back);
      if (backButton.evaluate().isNotEmpty) {
        final semantics = tester.getSemantics(backButton);
        expect(semantics.label, isNotNull);
      }
    });

    testWidgets('Performance test - screen transitions', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      final Stopwatch stopwatch = Stopwatch()..start();

      // Navigate to meal suggestion
      await tester.tap(find.text('Gợi ý món ăn'));
      await tester.pumpAndSettle();

      // Navigate back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Navigate to weekly menu
      await tester.tap(find.text('Thực đơn tuần'));
      await tester.pumpAndSettle();

      // Navigate back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Navigation should be reasonably fast (less than 5 seconds for all transitions)
      expect(stopwatch.elapsedMilliseconds, lessThan(5000));
    });

    testWidgets('Data persistence test', (WidgetTester tester) async {
      // This test would require actual database integration
      // For now, we test that the app doesn't crash when dealing with data operations
      
      app.main();
      await tester.pumpAndSettle();

      // Navigate through the app to trigger data operations
      await tester.tap(find.text('Gợi ý món ăn'));
      await tester.pumpAndSettle();

      // Select meal type
      await tester.tap(find.text('Bữa trưa'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // Select vegetarian option
      await tester.tap(find.text('Không'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Tiếp theo'));
      await tester.pumpAndSettle();

      // The app should not crash during these operations
      expect(find.byType(MealSuggestionScreen), findsOneWidget);
    });
  });
}
