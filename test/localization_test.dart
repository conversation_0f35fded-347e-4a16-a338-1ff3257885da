import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import '../lib/l10n/app_localizations.dart';
import '../lib/providers/language_provider.dart';

void main() {
  group('Localization Tests', () {
    testWidgets('Vietnamese localization works correctly', (WidgetTester tester) async {
      final languageProvider = LanguageProvider();
      await languageProvider.changeLanguage('vi');

      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('vi'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('vi'),
            Locale('en'),
          ],
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n?.appName ?? 'Default'),
                    Text(l10n?.home ?? 'Default'),
                    Text(l10n?.settings ?? 'Default'),
                    Text(l10n?.login ?? 'Default'),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Kiểm tra các text tiếng Việt
      expect(find.text('CookSpark'), findsOneWidget);
      expect(find.text('Trang chủ'), findsOneWidget);
      expect(find.text('Cài đặt'), findsOneWidget);
      expect(find.text('Đăng nhập'), findsOneWidget);
    });

    testWidgets('English localization works correctly', (WidgetTester tester) async {
      final languageProvider = LanguageProvider();
      await languageProvider.changeLanguage('en');

      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('vi'),
            Locale('en'),
          ],
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n?.appName ?? 'Default'),
                    Text(l10n?.home ?? 'Default'),
                    Text(l10n?.settings ?? 'Default'),
                    Text(l10n?.login ?? 'Default'),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Kiểm tra các text tiếng Anh
      expect(find.text('CookSpark'), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Login'), findsOneWidget);
    });

    testWidgets('Language switching works correctly', (WidgetTester tester) async {
      final languageProvider = LanguageProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          locale: Locale(languageProvider.currentLanguageCode),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('vi'),
            Locale('en'),
          ],
          home: StatefulBuilder(
            builder: (context, setState) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n?.home ?? 'Default'),
                    ElevatedButton(
                      onPressed: () async {
                        await languageProvider.changeLanguage('en');
                        setState(() {});
                      },
                      child: const Text('Switch to English'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        await languageProvider.changeLanguage('vi');
                        setState(() {});
                      },
                      child: const Text('Switch to Vietnamese'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Kiểm tra text ban đầu (Vietnamese)
      expect(find.text('Trang chủ'), findsOneWidget);

      // Chuyển sang tiếng Anh
      await tester.tap(find.text('Switch to English'));
      await tester.pumpAndSettle();

      // Rebuild widget với locale mới
      await tester.pumpWidget(
        MaterialApp(
          locale: Locale(languageProvider.currentLanguageCode),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('vi'),
            Locale('en'),
          ],
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Text(l10n?.home ?? 'Default'),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Home'), findsOneWidget);
    });

    test('LanguageProvider saves and loads language correctly', () async {
      final languageProvider = LanguageProvider();
      
      // Test default language
      expect(languageProvider.currentLanguageCode, 'en'); // Default is English

      // Test setting Vietnamese
      await languageProvider.changeLanguage('vi');
      expect(languageProvider.currentLanguageCode, 'vi');

      // Test setting English
      await languageProvider.changeLanguage('en');
      expect(languageProvider.currentLanguageCode, 'en');
    });

    test('AppLocalizations fallback works correctly', () {
      // Test với key không tồn tại
      const testKey = 'non_existent_key';
      
      // Tạo instance AppLocalizations với Vietnamese
      final viLocalizations = AppLocalizations(const Locale('vi'));
      expect(viLocalizations.getValue(testKey), testKey);
      
      // Tạo instance AppLocalizations với English
      final enLocalizations = AppLocalizations(const Locale('en'));
      expect(enLocalizations.getValue(testKey), testKey);
    });

    test('All required localization keys are present', () {
      final viLocalizations = AppLocalizations(const Locale('vi'));
      final enLocalizations = AppLocalizations(const Locale('en'));
      
      // Test các key quan trọng
      final requiredKeys = [
        'app_name',
        'home',
        'settings',
        'login',
        'meal_suggestion',
        'weekly_menu',
        'saved_dishes',
        'personal_information',
      ];
      
      for (final key in requiredKeys) {
        // Kiểm tra Vietnamese
        final viValue = viLocalizations.getValue(key);
        expect(viValue, isNotEmpty);
        expect(viValue, isNot(equals(key))); // Không phải fallback
        
        // Kiểm tra English
        final enValue = enLocalizations.getValue(key);
        expect(enValue, isNotEmpty);
        expect(enValue, isNot(equals(key))); // Không phải fallback
        
        // Kiểm tra Vietnamese và English khác nhau
        expect(viValue, isNot(equals(enValue)));
      }
    });
  });
}
