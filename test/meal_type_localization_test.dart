import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/l10n/app_localizations.dart';
import '../lib/models/user_profile.dart';

void main() {
  group('Meal Type Localization Tests', () {
    test('Vietnamese meal type localization', () {
      final viLocalizations = AppLocalizations(const Locale('vi'));
      
      expect(viLocalizations.breakfast, 'Bữa sáng');
      expect(viLocalizations.lunch, 'Bữa trưa');
      expect(viLocalizations.dinner, 'Bữa tối');
    });

    test('English meal type localization', () {
      final enLocalizations = AppLocalizations(const Locale('en'));
      
      expect(enLocalizations.breakfast, 'Breakfast');
      expect(enLocalizations.lunch, 'Lunch');
      expect(enLocalizations.dinner, 'Dinner');
    });

    test('MealType enum values', () {
      expect(MealType.breakfast.value, 'breakfast');
      expect(MealType.lunch.value, 'lunch');
      expect(MealType.dinner.value, 'dinner');
      
      expect(MealType.breakfast.emoji, '🌅');
      expect(MealType.lunch.emoji, '☀️');
      expect(MealType.dinner.emoji, '🌙');
    });

    test('MealType fromString conversion', () {
      expect(MealType.fromString('breakfast'), MealType.breakfast);
      expect(MealType.fromString('lunch'), MealType.lunch);
      expect(MealType.fromString('dinner'), MealType.dinner);
      expect(MealType.fromString('invalid'), MealType.lunch); // Default fallback
    });

    test('Meal type localization keys exist', () {
      final viLocalizations = AppLocalizations(const Locale('vi'));
      final enLocalizations = AppLocalizations(const Locale('en'));
      
      // Test breakfast
      final viBreakfast = viLocalizations.getValue('breakfast');
      final enBreakfast = enLocalizations.getValue('breakfast');
      expect(viBreakfast, 'Bữa sáng');
      expect(enBreakfast, 'Breakfast');
      expect(viBreakfast, isNot(equals(enBreakfast)));
      
      // Test lunch
      final viLunch = viLocalizations.getValue('lunch');
      final enLunch = enLocalizations.getValue('lunch');
      expect(viLunch, 'Bữa trưa');
      expect(enLunch, 'Lunch');
      expect(viLunch, isNot(equals(enLunch)));
      
      // Test dinner
      final viDinner = viLocalizations.getValue('dinner');
      final enDinner = enLocalizations.getValue('dinner');
      expect(viDinner, 'Bữa tối');
      expect(enDinner, 'Dinner');
      expect(viDinner, isNot(equals(enDinner)));
    });

    test('Please select meal type localization', () {
      final viLocalizations = AppLocalizations(const Locale('vi'));
      final enLocalizations = AppLocalizations(const Locale('en'));
      
      expect(viLocalizations.pleaseSelectMealType, 'Vui lòng chọn loại bữa ăn');
      expect(enLocalizations.pleaseSelectMealType, 'Please select meal type');
    });
  });
}
