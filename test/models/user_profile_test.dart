import 'package:flutter_test/flutter_test.dart';
import 'package:nau_gi_day/models/user_profile.dart';

void main() {
  group('UserProfile Model Tests', () {
    test('should create UserProfile with default values', () {
      final profile = UserProfile(
        userId: 'test-user-id',
        displayName: 'Test User',
        email: '<EMAIL>',
      );

      expect(profile.userId, 'test-user-id');
      expect(profile.displayName, 'Test User');
      expect(profile.email, '<EMAIL>');
      expect(profile.isOnboardingCompleted, false);
      expect(profile.favoriteIngredients, isEmpty);
      expect(profile.dietaryRestrictions, isEmpty);
      expect(profile.isVegetarian, false);
    });

    test('should create UserProfile from JSON', () {
      final json = {
        'id': 'profile-id',
        'user_id': 'user-id',
        'display_name': '<PERSON>',
        'email': '<EMAIL>',
        'photo_url': 'https://example.com/photo.jpg',
        'gender': 'male',
        'cooking_preference': 'simple',
        'favorite_ingredients': ['chicken', 'vegetables'],
        'dietary_restrictions': ['gluten-free'],
        'is_vegetarian': false,
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-01T00:00:00.000Z',
        'is_onboarding_completed': true,
      };

      final profile = UserProfile.fromJson(json);

      expect(profile.id, 'profile-id');
      expect(profile.userId, 'user-id');
      expect(profile.displayName, 'John Doe');
      expect(profile.email, '<EMAIL>');
      expect(profile.photoUrl, 'https://example.com/photo.jpg');
      expect(profile.gender, Gender.male);
      expect(profile.cookingPreference, CookingPreference.simple);
      expect(profile.favoriteIngredients, ['chicken', 'vegetables']);
      expect(profile.dietaryRestrictions, ['gluten-free']);
      expect(profile.isVegetarian, false);
      expect(profile.isOnboardingCompleted, true);
    });

    test('should convert UserProfile to JSON', () {
      final profile = UserProfile(
        userId: 'user-id',
        displayName: 'Jane Doe',
        email: '<EMAIL>',
        gender: Gender.female,
        cookingPreference: CookingPreference.elaborate,
        favoriteIngredients: ['seafood', 'vegetables'],
        isVegetarian: true,
        isOnboardingCompleted: true,
      );

      final json = profile.toJson();

      expect(json['user_id'], 'user-id');
      expect(json['display_name'], 'Jane Doe');
      expect(json['email'], '<EMAIL>');
      expect(json['gender'], 'female');
      expect(json['cooking_preference'], 'elaborate');
      expect(json['favorite_ingredients'], ['seafood', 'vegetables']);
      expect(json['is_vegetarian'], true);
      expect(json['is_onboarding_completed'], true);
    });

    test('should create copy with updated values', () {
      final original = UserProfile(
        userId: 'user-id',
        displayName: 'Original Name',
        gender: Gender.male,
        isOnboardingCompleted: false,
      );

      final updated = original.copyWith(
        displayName: 'Updated Name',
        gender: Gender.female,
        isOnboardingCompleted: true,
      );

      expect(updated.userId, original.userId);
      expect(updated.displayName, 'Updated Name');
      expect(updated.gender, Gender.female);
      expect(updated.isOnboardingCompleted, true);
      expect(updated.id, original.id); // ID should remain the same
    });
  });

  group('Gender Enum Tests', () {
    test('should convert string to Gender enum', () {
      expect(Gender.fromString('male'), Gender.male);
      expect(Gender.fromString('female'), Gender.female);
      expect(Gender.fromString('other'), Gender.other);
      expect(Gender.fromString('invalid'), Gender.other); // Default fallback
    });

    test('should have correct display names', () {
      expect(Gender.male.displayName, 'Nam');
      expect(Gender.female.displayName, 'Nữ');
      expect(Gender.other.displayName, 'Khác');
    });
  });

  group('CookingPreference Enum Tests', () {
    test('should convert string to CookingPreference enum', () {
      expect(CookingPreference.fromString('simple'), CookingPreference.simple);
      expect(CookingPreference.fromString('elaborate'), CookingPreference.elaborate);
      expect(CookingPreference.fromString('invalid'), CookingPreference.simple); // Default fallback
    });

    test('should have correct display names and descriptions', () {
      expect(CookingPreference.simple.displayName, 'Đơn giản');
      expect(CookingPreference.simple.description, 'Tôi thích những món ăn dễ làm, ít nguyên liệu');
      expect(CookingPreference.elaborate.displayName, 'Cầu kỳ');
      expect(CookingPreference.elaborate.description, 'Tôi thích thử nghiệm những món ăn phức tạp, đa dạng');
    });
  });

  group('MainIngredient Enum Tests', () {
    test('should convert string to MainIngredient enum', () {
      expect(MainIngredient.fromString('pork'), MainIngredient.pork);
      expect(MainIngredient.fromString('chicken'), MainIngredient.chicken);
      expect(MainIngredient.fromString('vegetables'), MainIngredient.vegetables);
      expect(MainIngredient.fromString('invalid'), MainIngredient.vegetables); // Default fallback
    });

    test('should have correct display names and emojis', () {
      expect(MainIngredient.pork.displayName, 'Thịt lợn');
      expect(MainIngredient.pork.emoji, '🐷');
      expect(MainIngredient.chicken.displayName, 'Thịt gà');
      expect(MainIngredient.chicken.emoji, '🐔');
      expect(MainIngredient.vegetables.displayName, 'Rau củ');
      expect(MainIngredient.vegetables.emoji, '🥬');
    });
  });

  group('MealType Enum Tests', () {
    test('should convert string to MealType enum', () {
      expect(MealType.fromString('breakfast'), MealType.breakfast);
      expect(MealType.fromString('lunch'), MealType.lunch);
      expect(MealType.fromString('dinner'), MealType.dinner);
      expect(MealType.fromString('invalid'), MealType.lunch); // Default fallback
    });

    test('should have correct display names and emojis', () {
      // Note: displayName is now hardcoded Vietnamese, use localization in UI
      expect(MealType.breakfast.displayName, 'Bữa sáng');
      expect(MealType.breakfast.emoji, '🌅');
      expect(MealType.lunch.displayName, 'Bữa trưa');
      expect(MealType.lunch.emoji, '☀️');
      expect(MealType.dinner.displayName, 'Bữa tối');
      expect(MealType.dinner.emoji, '🌙');
    });
  });

  group('MealSuggestionQuery Tests', () {
    test('should create MealSuggestionQuery with default values', () {
      final query = MealSuggestionQuery(
        mealType: MealType.lunch,
      );

      expect(query.mealType, MealType.lunch);
      expect(query.isVegetarian, false);
      expect(query.preferredIngredients, isEmpty);
      expect(query.autoSuggest, false);
    });

    test('should convert to JSON correctly', () {
      final query = MealSuggestionQuery(
        mealType: MealType.dinner,
        isVegetarian: true,
        preferredIngredients: [MainIngredient.vegetables, MainIngredient.tofu],
        autoSuggest: false,
      );

      final json = query.toJson();

      expect(json['meal_type'], 'dinner');
      expect(json['is_vegetarian'], true);
      expect(json['preferred_ingredients'], ['vegetables', 'tofu']);
      expect(json['auto_suggest'], false);
    });

    test('should generate YouTube search query correctly', () {
      final query1 = MealSuggestionQuery(
        mealType: MealType.breakfast,
        isVegetarian: false,
        preferredIngredients: [MainIngredient.eggs],
        autoSuggest: false,
      );

      final searchQuery1 = query1.toYouTubeSearchQuery();
      expect(searchQuery1, contains('Bữa sáng'));
      expect(searchQuery1, contains('Trứng'));
      expect(searchQuery1, contains('đơn giản ngon'));

      final query2 = MealSuggestionQuery(
        mealType: MealType.lunch,
        isVegetarian: true,
        preferredIngredients: [],
        autoSuggest: true,
      );

      final searchQuery2 = query2.toYouTubeSearchQuery();
      expect(searchQuery2, contains('Bữa trưa'));
      expect(searchQuery2, contains('chay'));
      expect(searchQuery2, contains('đơn giản ngon'));
    });
  });
}
