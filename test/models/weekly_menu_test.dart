import 'package:flutter_test/flutter_test.dart';
import 'package:nau_gi_day/models/weekly_menu.dart';
import 'package:nau_gi_day/models/user_profile.dart';
import 'package:nau_gi_day/services/youtube_service.dart';

void main() {
  group('WeeklyMenuItem Tests', () {
    late YouTubeVideo testVideo;
    late DateTime testDate;

    setUp(() {
      testVideo = YouTubeVideo(
        id: 'test-video-id',
        title: 'Test Video Title',
        description: 'Test video description',
        thumbnailUrl: 'https://example.com/thumbnail.jpg',
        channelTitle: 'Test Channel',
        duration: const Duration(minutes: 10, seconds: 30),
        viewCount: 1000,
        publishedAt: DateTime(2024, 1, 1),
      );
      testDate = DateTime(2024, 1, 15); // Monday
    });

    test('should create WeeklyMenuItem with default values', () {
      final item = WeeklyMenuItem(
        userId: 'test-user-id',
        date: testDate,
        mealType: MealType.lunch,
        video: testVideo,
      );

      expect(item.userId, 'test-user-id');
      expect(item.date, testDate);
      expect(item.mealType, MealType.lunch);
      expect(item.video, testVideo);
      expect(item.notes, '');
      expect(item.isCompleted, false);
    });

    test('should create WeeklyMenuItem from JSON', () {
      final json = {
        'id': 'item-id',
        'user_id': 'user-id',
        'date': '2024-01-15',
        'meal_type': 'lunch',
        'video_id': 'video-id',
        'video_title': 'Video Title',
        'video_description': 'Video Description',
        'video_thumbnail_url': 'https://example.com/thumb.jpg',
        'video_channel_title': 'Channel Title',
        'video_duration_seconds': 630,
        'video_view_count': 5000,
        'video_published_at': '2024-01-01T00:00:00.000Z',
        'notes': 'Test notes',
        'is_completed': true,
        'created_at': '2024-01-15T00:00:00.000Z',
        'updated_at': '2024-01-15T00:00:00.000Z',
      };

      final item = WeeklyMenuItem.fromJson(json);

      expect(item.id, 'item-id');
      expect(item.userId, 'user-id');
      expect(item.date, DateTime(2024, 1, 15));
      expect(item.mealType, MealType.lunch);
      expect(item.video.id, 'video-id');
      expect(item.video.title, 'Video Title');
      expect(item.video.duration.inSeconds, 630);
      expect(item.notes, 'Test notes');
      expect(item.isCompleted, true);
    });

    test('should convert WeeklyMenuItem to JSON', () {
      final item = WeeklyMenuItem(
        userId: 'user-id',
        date: testDate,
        mealType: MealType.dinner,
        video: testVideo,
        notes: 'Test notes',
        isCompleted: true,
      );

      final json = item.toJson();

      expect(json['user_id'], 'user-id');
      expect(json['date'], '2024-01-15');
      expect(json['meal_type'], 'dinner');
      expect(json['video_id'], 'test-video-id');
      expect(json['video_title'], 'Test Video Title');
      expect(json['notes'], 'Test notes');
      expect(json['is_completed'], true);
    });

    test('should generate correct date and meal keys', () {
      final item = WeeklyMenuItem(
        userId: 'user-id',
        date: DateTime(2024, 1, 15),
        mealType: MealType.breakfast,
        video: testVideo,
      );

      expect(item.dateKey, '2024-01-15');
      expect(item.mealKey, '2024-01-15_breakfast');
    });

    test('should create copy with updated values', () {
      final original = WeeklyMenuItem(
        userId: 'user-id',
        date: testDate,
        mealType: MealType.lunch,
        video: testVideo,
        notes: 'Original notes',
        isCompleted: false,
      );

      final updated = original.copyWith(
        notes: 'Updated notes',
        isCompleted: true,
      );

      expect(updated.id, original.id);
      expect(updated.userId, original.userId);
      expect(updated.notes, 'Updated notes');
      expect(updated.isCompleted, true);
      expect(updated.date, original.date);
      expect(updated.mealType, original.mealType);
    });
  });

  group('WeeklyMenu Tests', () {
    late DateTime weekStart;
    late List<WeeklyMenuItem> testItems;

    setUp(() {
      weekStart = DateTime(2024, 1, 15); // Monday
      testItems = [
        WeeklyMenuItem(
          userId: 'user-id',
          date: weekStart,
          mealType: MealType.breakfast,
          video: YouTubeVideo(
            id: 'video1',
            title: 'Breakfast Video',
            description: '',
            thumbnailUrl: '',
            channelTitle: '',
            duration: Duration.zero,
            viewCount: 0,
            publishedAt: DateTime.now(),
          ),
          isCompleted: true,
        ),
        WeeklyMenuItem(
          userId: 'user-id',
          date: weekStart,
          mealType: MealType.lunch,
          video: YouTubeVideo(
            id: 'video2',
            title: 'Lunch Video',
            description: '',
            thumbnailUrl: '',
            channelTitle: '',
            duration: Duration.zero,
            viewCount: 0,
            publishedAt: DateTime.now(),
          ),
          isCompleted: false,
        ),
        WeeklyMenuItem(
          userId: 'user-id',
          date: weekStart.add(const Duration(days: 1)),
          mealType: MealType.dinner,
          video: YouTubeVideo(
            id: 'video3',
            title: 'Dinner Video',
            description: '',
            thumbnailUrl: '',
            channelTitle: '',
            duration: Duration.zero,
            viewCount: 0,
            publishedAt: DateTime.now(),
          ),
          isCompleted: true,
        ),
      ];
    });

    test('should create WeeklyMenu with default values', () {
      final menu = WeeklyMenu(
        userId: 'test-user-id',
        weekStartDate: weekStart,
      );

      expect(menu.userId, 'test-user-id');
      expect(menu.weekStartDate, weekStart);
      expect(menu.items, isEmpty);
      expect(menu.name, '');
      expect(menu.description, '');
    });

    test('should calculate week end date correctly', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
      );

      final expectedEndDate = weekStart.add(const Duration(days: 6));
      expect(menu.weekEndDate, expectedEndDate);
    });

    test('should generate correct week name', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: DateTime(2024, 1, 15), // Jan 15
      );

      expect(menu.weekName, 'Tuần 15/1 - 21/1');
    });

    test('should get items for specific date', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      final itemsForFirstDay = menu.getItemsForDate(weekStart);
      expect(itemsForFirstDay.length, 2); // breakfast and lunch

      final itemsForSecondDay = menu.getItemsForDate(weekStart.add(const Duration(days: 1)));
      expect(itemsForSecondDay.length, 1); // dinner

      final itemsForEmptyDay = menu.getItemsForDate(weekStart.add(const Duration(days: 2)));
      expect(itemsForEmptyDay.length, 0);
    });

    test('should get item for specific meal', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      final breakfastItem = menu.getItemForMeal(weekStart, MealType.breakfast);
      expect(breakfastItem, isNotNull);
      expect(breakfastItem!.video.title, 'Breakfast Video');

      final nonExistentItem = menu.getItemForMeal(weekStart, MealType.dinner);
      expect(nonExistentItem, isNull);
    });

    test('should check if has items for date', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      expect(menu.hasItemsForDate(weekStart), true);
      expect(menu.hasItemsForDate(weekStart.add(const Duration(days: 1))), true);
      expect(menu.hasItemsForDate(weekStart.add(const Duration(days: 2))), false);
    });

    test('should calculate completion statistics', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      expect(menu.completedItemsCount, 2);
      expect(menu.completionPercentage, closeTo(66.67, 0.01));
    });

    test('should generate week days correctly', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
      );

      final weekDays = menu.weekDays;
      expect(weekDays.length, 7);
      expect(weekDays.first, weekStart);
      expect(weekDays.last, weekStart.add(const Duration(days: 6)));
    });

    test('should add item correctly', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      final newItem = WeeklyMenuItem(
        userId: 'user-id',
        date: weekStart,
        mealType: MealType.dinner,
        video: YouTubeVideo(
          id: 'new-video',
          title: 'New Dinner Video',
          description: '',
          thumbnailUrl: '',
          channelTitle: '',
          duration: Duration.zero,
          viewCount: 0,
          publishedAt: DateTime.now(),
        ),
      );

      final updatedMenu = menu.addItem(newItem);
      expect(updatedMenu.items.length, 4);
      
      final dinnerItem = updatedMenu.getItemForMeal(weekStart, MealType.dinner);
      expect(dinnerItem, isNotNull);
      expect(dinnerItem!.video.title, 'New Dinner Video');
    });

    test('should replace existing item when adding same meal type and date', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      final replacementItem = WeeklyMenuItem(
        userId: 'user-id',
        date: weekStart,
        mealType: MealType.breakfast,
        video: YouTubeVideo(
          id: 'replacement-video',
          title: 'Replacement Breakfast Video',
          description: '',
          thumbnailUrl: '',
          channelTitle: '',
          duration: Duration.zero,
          viewCount: 0,
          publishedAt: DateTime.now(),
        ),
      );

      final updatedMenu = menu.addItem(replacementItem);
      expect(updatedMenu.items.length, 3); // Same count, item replaced
      
      final breakfastItem = updatedMenu.getItemForMeal(weekStart, MealType.breakfast);
      expect(breakfastItem!.video.title, 'Replacement Breakfast Video');
    });

    test('should get statistics correctly', () {
      final menu = WeeklyMenu(
        userId: 'user-id',
        weekStartDate: weekStart,
        items: testItems,
      );

      final stats = menu.getStatistics();
      
      expect(stats['total_items'], 3);
      expect(stats['completed_items'], 2);
      expect(stats['pending_items'], 1);
      expect(stats['completion_percentage'], closeTo(66.67, 0.01));
      expect(stats['breakfast_count'], 1);
      expect(stats['lunch_count'], 1);
      expect(stats['dinner_count'], 1);
      expect(stats['week_name'], 'Tuần 15/1 - 21/1');
    });
  });

  group('WeekUtils Tests', () {
    test('should get week start date correctly', () {
      // Test with different days of the week
      final monday = DateTime(2024, 1, 15); // Monday
      final wednesday = DateTime(2024, 1, 17); // Wednesday
      final sunday = DateTime(2024, 1, 21); // Sunday

      expect(WeekUtils.getWeekStartDate(monday), monday);
      expect(WeekUtils.getWeekStartDate(wednesday), monday);
      expect(WeekUtils.getWeekStartDate(sunday), monday);
    });

    test('should get week end date correctly', () {
      final monday = DateTime(2024, 1, 15);
      final expectedSunday = DateTime(2024, 1, 21);

      expect(WeekUtils.getWeekEndDate(monday), expectedSunday);
    });

    test('should check if dates are in same week', () {
      final monday = DateTime(2024, 1, 15);
      final wednesday = DateTime(2024, 1, 17);
      final nextMonday = DateTime(2024, 1, 22);

      expect(WeekUtils.isSameWeek(monday, wednesday), true);
      expect(WeekUtils.isSameWeek(monday, nextMonday), false);
    });

    test('should get day names correctly', () {
      final monday = DateTime(2024, 1, 15);
      final tuesday = DateTime(2024, 1, 16);
      final sunday = DateTime(2024, 1, 21);

      expect(WeekUtils.getDayName(monday), 'Thứ 2');
      expect(WeekUtils.getDayName(tuesday), 'Thứ 3');
      expect(WeekUtils.getDayName(sunday), 'Chủ nhật');
    });

    test('should get short day names correctly', () {
      final monday = DateTime(2024, 1, 15);
      final tuesday = DateTime(2024, 1, 16);
      final sunday = DateTime(2024, 1, 21);

      expect(WeekUtils.getShortDayName(monday), 'T2');
      expect(WeekUtils.getShortDayName(tuesday), 'T3');
      expect(WeekUtils.getShortDayName(sunday), 'CN');
    });
  });
}
