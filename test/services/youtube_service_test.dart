import 'package:flutter_test/flutter_test.dart';
import 'package:nau_gi_day/services/youtube_service.dart';
import 'package:nau_gi_day/models/user_profile.dart';

void main() {
  group('YouTubeVideo Tests', () {
    test('should create YouTubeVideo with all properties', () {
      final video = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video Title',
        description: 'Test video description',
        thumbnailUrl: 'https://example.com/thumbnail.jpg',
        channelTitle: 'Test Channel',
        duration: const Duration(minutes: 5, seconds: 30),
        viewCount: 1000,
        publishedAt: DateTime(2024, 1, 1),
      );

      expect(video.id, 'test-id');
      expect(video.title, 'Test Video Title');
      expect(video.description, 'Test video description');
      expect(video.thumbnailUrl, 'https://example.com/thumbnail.jpg');
      expect(video.channelTitle, 'Test Channel');
      expect(video.duration, const Duration(minutes: 5, seconds: 30));
      expect(video.viewCount, 1000);
      expect(video.publishedAt, DateTime(2024, 1, 1));
    });

    test('should format duration correctly', () {
      final video1 = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video',
        description: '',
        thumbnailUrl: '',
        channelTitle: '',
        duration: const Duration(minutes: 5, seconds: 30),
        viewCount: 0,
        publishedAt: DateTime.now(),
      );

      final video2 = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video',
        description: '',
        thumbnailUrl: '',
        channelTitle: '',
        duration: const Duration(minutes: 12, seconds: 5),
        viewCount: 0,
        publishedAt: DateTime.now(),
      );

      expect(video1.formattedDuration, '5:30');
      expect(video2.formattedDuration, '12:05');
    });

    test('should format view count correctly', () {
      final video1 = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video',
        description: '',
        thumbnailUrl: '',
        channelTitle: '',
        duration: Duration.zero,
        viewCount: 500,
        publishedAt: DateTime.now(),
      );

      final video2 = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video',
        description: '',
        thumbnailUrl: '',
        channelTitle: '',
        duration: Duration.zero,
        viewCount: 1500,
        publishedAt: DateTime.now(),
      );

      final video3 = YouTubeVideo(
        id: 'test-id',
        title: 'Test Video',
        description: '',
        thumbnailUrl: '',
        channelTitle: '',
        duration: Duration.zero,
        viewCount: 1500000,
        publishedAt: DateTime.now(),
      );

      expect(video1.formattedViewCount, '500 lượt xem');
      expect(video2.formattedViewCount, '1.5K lượt xem');
      expect(video3.formattedViewCount, '1.5M lượt xem');
    });

    test('should parse ISO 8601 duration correctly', () {
      // Test the private _parseDuration method through fromJson
      final json1 = {
        'id': 'test-id',
        'snippet': {
          'title': 'Test Video',
          'description': '',
          'thumbnails': {'medium': {'url': ''}},
          'channelTitle': '',
          'publishedAt': '2024-01-01T00:00:00Z',
        },
        'contentDetails': {
          'duration': 'PT4M13S', // 4 minutes 13 seconds
        },
        'statistics': {
          'viewCount': '1000',
        },
      };

      final json2 = {
        'id': 'test-id',
        'snippet': {
          'title': 'Test Video',
          'description': '',
          'thumbnails': {'medium': {'url': ''}},
          'channelTitle': '',
          'publishedAt': '2024-01-01T00:00:00Z',
        },
        'contentDetails': {
          'duration': 'PT1H30M45S', // 1 hour 30 minutes 45 seconds
        },
        'statistics': {
          'viewCount': '1000',
        },
      };

      final video1 = YouTubeVideo.fromJson(json1);
      final video2 = YouTubeVideo.fromJson(json2);

      expect(video1.duration, const Duration(minutes: 4, seconds: 13));
      expect(video2.duration, const Duration(hours: 1, minutes: 30, seconds: 45));
    });

    test('should handle missing or invalid duration', () {
      final json = {
        'id': 'test-id',
        'snippet': {
          'title': 'Test Video',
          'description': '',
          'thumbnails': {'medium': {'url': ''}},
          'channelTitle': '',
          'publishedAt': '2024-01-01T00:00:00Z',
        },
        'contentDetails': {
          'duration': 'INVALID', // Invalid duration
        },
        'statistics': {
          'viewCount': '1000',
        },
      };

      final video = YouTubeVideo.fromJson(json);
      expect(video.duration, Duration.zero);
    });

    test('should create from JSON with missing fields', () {
      final json = {
        'id': 'test-id',
        'snippet': {
          'title': 'Test Video',
          // Missing other fields
        },
        // Missing contentDetails and statistics
      };

      final video = YouTubeVideo.fromJson(json);

      expect(video.id, 'test-id');
      expect(video.title, 'Test Video');
      expect(video.description, '');
      expect(video.thumbnailUrl, '');
      expect(video.channelTitle, '');
      expect(video.duration, Duration.zero);
      expect(video.viewCount, 0);
    });
  });

  group('YouTubeService Tests', () {
    late YouTubeService youtubeService;

    setUp(() {
      youtubeService = YouTubeService();
    });

    test('should generate correct embed URL', () {
      const videoId = 'test-video-id';
      final embedUrl = youtubeService.getEmbedUrl(videoId);
      expect(embedUrl, 'https://www.youtube.com/embed/test-video-id');
    });

    test('should generate correct watch URL', () {
      const videoId = 'test-video-id';
      final watchUrl = youtubeService.getWatchUrl(videoId);
      expect(watchUrl, 'https://www.youtube.com/watch?v=test-video-id');
    });

    test('should search videos with mock data', () async {
      final query = MealSuggestionQuery(
        mealType: MealType.lunch,
        isVegetarian: false,
        preferredIngredients: [MainIngredient.chicken],
        autoSuggest: false,
      );

      final result = await youtubeService.searchVideos(query);

      expect(result['success'], true);
      expect(result['videos'], isA<List<YouTubeVideo>>());
      expect(result['query'], isA<String>());

      final videos = result['videos'] as List<YouTubeVideo>;
      expect(videos.isNotEmpty, true);

      // Check that videos have required properties
      for (final video in videos) {
        expect(video.id, isNotEmpty);
        expect(video.title, isNotEmpty);
        expect(video.duration.inSeconds, greaterThan(0));
      }
    });

    test('should handle search errors gracefully', () async {
      // This test would require mocking HTTP calls to test actual error scenarios
      // For now, we test that the mock data returns successfully
      final query = MealSuggestionQuery(
        mealType: MealType.breakfast,
        isVegetarian: true,
        preferredIngredients: [],
        autoSuggest: true,
      );

      final result = await youtubeService.searchVideos(query);

      expect(result['success'], true);
      expect(result.containsKey('videos'), true);
      expect(result.containsKey('query'), true);
    });

    test('should filter videos by duration in mock data', () async {
      final query = MealSuggestionQuery(
        mealType: MealType.dinner,
        isVegetarian: false,
        preferredIngredients: [MainIngredient.beef],
        autoSuggest: false,
      );

      final result = await youtubeService.searchVideos(query);
      final videos = result['videos'] as List<YouTubeVideo>;

      // All videos should be between 5-30 minutes (mock data constraint)
      for (final video in videos) {
        final minutes = video.duration.inMinutes;
        expect(minutes, greaterThanOrEqualTo(5));
        expect(minutes, lessThanOrEqualTo(30));
      }
    });

    test('should limit results to 10 videos', () async {
      final query = MealSuggestionQuery(
        mealType: MealType.lunch,
        isVegetarian: false,
        preferredIngredients: [],
        autoSuggest: true,
      );

      final result = await youtubeService.searchVideos(query);
      final videos = result['videos'] as List<YouTubeVideo>;

      expect(videos.length, lessThanOrEqualTo(10));
    });
  });

  group('MealSuggestionQuery YouTube Integration Tests', () {
    test('should generate appropriate search query for breakfast', () {
      final query = MealSuggestionQuery(
        mealType: MealType.breakfast,
        isVegetarian: false,
        preferredIngredients: [MainIngredient.eggs],
        autoSuggest: false,
      );

      final searchQuery = query.toYouTubeSearchQuery();

      expect(searchQuery, contains('Bữa sáng'));
      expect(searchQuery, contains('Trứng'));
      expect(searchQuery, contains('đơn giản ngon'));
      expect(searchQuery, isNot(contains('chay')));
    });

    test('should generate appropriate search query for vegetarian meal', () {
      final query = MealSuggestionQuery(
        mealType: MealType.lunch,
        isVegetarian: true,
        preferredIngredients: [MainIngredient.vegetables],
        autoSuggest: false,
      );

      final searchQuery = query.toYouTubeSearchQuery();

      expect(searchQuery, contains('Bữa trưa'));
      expect(searchQuery, contains('chay'));
      expect(searchQuery, contains('Rau củ'));
      expect(searchQuery, contains('đơn giản ngon'));
    });

    test('should generate appropriate search query for auto suggest', () {
      final query = MealSuggestionQuery(
        mealType: MealType.dinner,
        isVegetarian: false,
        preferredIngredients: [MainIngredient.chicken, MainIngredient.vegetables],
        autoSuggest: true,
      );

      final searchQuery = query.toYouTubeSearchQuery();

      expect(searchQuery, contains('Bữa tối'));
      expect(searchQuery, contains('đơn giản ngon'));
      // When auto suggest is true, ingredients should not be included
      expect(searchQuery, isNot(contains('Thịt gà')));
      expect(searchQuery, isNot(contains('Rau củ')));
    });

    test('should generate search query with multiple ingredients', () {
      final query = MealSuggestionQuery(
        mealType: MealType.lunch,
        isVegetarian: false,
        preferredIngredients: [
          MainIngredient.pork,
          MainIngredient.vegetables,
          MainIngredient.rice,
        ],
        autoSuggest: false,
      );

      final searchQuery = query.toYouTubeSearchQuery();

      expect(searchQuery, contains('Bữa trưa'));
      expect(searchQuery, contains('Thịt lợn'));
      expect(searchQuery, contains('Rau củ'));
      expect(searchQuery, contains('Cơm'));
      expect(searchQuery, contains('đơn giản ngon'));
    });
  });
}
