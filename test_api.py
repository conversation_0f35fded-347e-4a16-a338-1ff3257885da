#!/usr/bin/env python3
import requests
import json

def test_youtube_api():
    print("🧪 Testing YouTube API...")
    
    api_key = "AIzaSyDLAUzqDcL00qTn6iiKIfNGI4pJp8YnC64"
    query = "phở bò nấu ăn recipe cooking"
    
    url = "https://www.googleapis.com/youtube/v3/search"
    params = {
        'part': 'snippet',
        'q': query,
        'type': 'video',
        'maxResults': 3,
        'order': 'relevance',
        'regionCode': 'VN',
        'relevanceLanguage': 'vi',
        'videoDuration': 'medium',
        'key': api_key
    }
    
    print(f"🔗 URL: {url}")
    print(f"🔍 Query: {query}")
    print(f"🔑 API Key: {api_key[:10]}...")
    
    try:
        response = requests.get(url, params=params, timeout=15)
        print(f"📡 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            print(f"✅ Success! Found {len(items)} videos")
            
            for i, item in enumerate(items[:3]):
                snippet = item['snippet']
                print(f"   📺 {i+1}. {snippet['title']}")
                print(f"      Channel: {snippet['channelTitle']}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"❌ Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"💥 Exception: {e}")

if __name__ == "__main__":
    test_youtube_api()
