#!/bin/bash

echo "🧪 Testing OAuth Callback with Real Code"
echo "========================================"

# Kiểm tra xem có thiết bị Android kết nối không
echo "📱 Checking connected Android devices..."
adb devices

echo ""
echo "🔗 Testing OAuth callback with real authorization code..."

# Test với code giống như trong log
echo "Test: OAuth callback with authorization code"
adb shell am start -W -a android.intent.action.VIEW -d "com.minhduc.naugiday://callback?code=41fdc83e-e709-4bde-8cb5-7e47a955e8a0" com.minhduc.naugiday

sleep 3

echo ""
echo "✅ OAuth callback test completed!"
echo "📋 Check the Flutter logs for detailed processing:"
echo "   flutter logs | grep -E '(🔐|📋|✅|❌|STEP)'"

echo ""
echo "🔍 Expected logs:"
echo "   🔐 PROCESSING OAUTH CALLBACK"
echo "   📋 STEP 3: Processing session from URL"
echo "   📋 STEP 4: Checking current session"
echo "   ✅ OAuth callback processed successfully"

echo ""
echo "❌ If you see timeout or failure:"
echo "   1. Check Supabase Dashboard > Authentication > URL Configuration"
echo "   2. Ensure redirect URI 'com.minhduc.naugiday://callback' is added"
echo "   3. Check Google OAuth configuration"
echo "   4. Verify app is running and deep link service is initialized"
